package cn.tl.cloud.module.monitor.service.energy;

import cn.tl.cloud.module.monitor.controller.admin.energy.energyelectric.vo.*;
import cn.tl.cloud.module.monitor.controller.admin.energy.energyindicator.vo.EnergyIndicatorRespVO;
import cn.tl.cloud.module.monitor.dal.clickhouse.EnergyIndicatorDataMapper;
import cn.tl.cloud.module.monitor.dal.dataobject.energy.energyindicator.EnergyIndicatorDO;
import cn.tl.cloud.module.monitor.dal.dataobject.energy.energynode.EnergyNodeDO;
import cn.tl.cloud.module.monitor.dal.mysql.energy.EnergyIndicatorMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 能耗分析服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class EnergyAnalysisServiceImpl implements EnergyAnalysisService {

    @Autowired
    private EnergyNodeService energyNodeService;

    @Autowired
    private EnergyIndicatorService energyIndicatorService;

    @Autowired
    private EnergyIndicatorMapper energyIndicatorMapper;

    @Autowired
    private EnergyIndicatorDataMapper energyIndicatorDataMapper;


    @Override
    public EnergyAnalysisRespVO getEnergyStatistics(EnergyStatisticsReqVO reqVO) {
        log.info("开始查询能耗统计数据，厂站ID: {}, 节点ID: {}", reqVO.getFactoryId(), reqVO.getNodeId());

        try {
            // 1. 获取节点指标编码列表
            List<String> indicatorCodes = getIndicatorCodesForNode(reqVO.getNodeId());
            log.info("获取到指标编码列表: {}", indicatorCodes);

            if (CollectionUtils.isEmpty(indicatorCodes)) {
                log.warn("节点及其子节点均未绑定任何指标，节点ID: {}", reqVO.getNodeId());
                EnergyAnalysisRespVO emptyResult = createEmptyResult(reqVO.getNodeId());
                emptyResult.setNodeName(getNodeName(reqVO.getNodeId()));
                emptyResult.setTimeRangeDesc(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy年MM月")));
                emptyResult.setIndicatorName("能耗统计");
                return emptyResult;
            }

            // 2. 查询能耗数据
            BigDecimal currentTotal = energyIndicatorDataMapper.selectCurrentMonthTotal(reqVO.getFactoryId(), indicatorCodes);
            BigDecimal lastMonthTotal = energyIndicatorDataMapper.selectLastMonthTotal(reqVO.getFactoryId(), indicatorCodes);
            BigDecimal lastYearTotal = energyIndicatorDataMapper.selectLastYearSameMonthTotal(reqVO.getFactoryId(), indicatorCodes);

            log.info("能耗数据查询结果 - 当月: {}, 上月: {}, 去年同月: {}", currentTotal, lastMonthTotal, lastYearTotal);

            // 3. 计算增长率
            BigDecimal yearOnYearGrowthRate = calculateGrowthRate(currentTotal, lastYearTotal);
            BigDecimal periodOnPeriodGrowthRate = calculateGrowthRate(currentTotal, lastMonthTotal);


            // 4. 获取节点信息
            String nodeName = getNodeName(reqVO.getNodeId());
            String unit = getIndicatorUnit(indicatorCodes);

            // 5. 构建返回结果
            EnergyAnalysisRespVO result = new EnergyAnalysisRespVO();
            result.setCurrentTotal(currentTotal != null ? currentTotal : BigDecimal.ZERO);
            result.setLastYearTotal(lastYearTotal != null ? lastYearTotal : BigDecimal.ZERO);
            result.setLastPeriodTotal(lastMonthTotal != null ? lastMonthTotal : BigDecimal.ZERO);
            result.setYearOnYearGrowthRate(yearOnYearGrowthRate);
            result.setPeriodOnPeriodGrowthRate(periodOnPeriodGrowthRate);
            result.setUnit(unit);
            result.setNodeName(nodeName);
            result.setTimeRangeDesc(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy年MM月")));

            // 暂时不计算吨水能耗相关字段
            result.setEnergyPerTon(null);
            result.setLastYearEnergyPerTon(null);
            result.setLastPeriodEnergyPerTon(null);
            result.setIndicatorName("能耗统计");

            log.info("能耗统计查询完成，结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("查询能耗统计数据失败，厂站ID: {}, 节点ID: {}", reqVO.getFactoryId(), reqVO.getNodeId(), e);
            return createEmptyResult(reqVO.getNodeId());
        }
    }

    @Override
    public EnergyTrendRespVO getTotalEnergyTrend(EnergyAnalysisReqVO reqVO) {
        log.info("开始查询总能耗趋势数据，厂站ID: {}, 节点ID: {}, 时间范围: {} - {}", 
                reqVO.getFactoryId(), reqVO.getNodeId(), reqVO.getStartTime(), reqVO.getEndTime());

        try {
            // 1. 获取节点指标编码列表
            List<String> indicatorCodes = getIndicatorCodesForNode(reqVO.getNodeId());
            log.info("获取到指标编码列表: {}", indicatorCodes);

            if (indicatorCodes == null) {
                log.warn("无法获取指标编码列表，可能节点不存在或没有权限，节点ID: {}", reqVO.getNodeId());
                return createEmptyTrendResult(reqVO.getNodeId());
            }

            if (CollectionUtils.isEmpty(indicatorCodes)) {
                log.warn("节点未绑定任何指标，节点ID: {}", reqVO.getNodeId());
                return createEmptyTrendResult(reqVO.getNodeId());
            }

            // 2. 查询能耗趋势数据
            List<EnergyIndicatorDataMapper.DailyEnergyData> dailyEnergyDataList = 
                    energyIndicatorDataMapper.selectDailyEnergyTrend(
                            reqVO.getFactoryId(), indicatorCodes, reqVO.getStartTime(), reqVO.getEndTime());

            // 3. 构建返回结果
            EnergyTrendRespVO result = new EnergyTrendRespVO();
            
            // 提取时间轴和能耗数据
            List<String> timeAxis = dailyEnergyDataList.stream()
                    .map(EnergyIndicatorDataMapper.DailyEnergyData::getDate)
                    .collect(Collectors.toList());
            
            List<BigDecimal> energyData = dailyEnergyDataList.stream()
                    .map(data -> data.getTotalEnergy() != null ? data.getTotalEnergy() : BigDecimal.ZERO)
                    .collect(Collectors.toList());
            
            result.setTimeAxis(timeAxis);
            result.setEnergyData(energyData);
            
            // 获取节点名称和单位
            result.setNodeName(getNodeName(reqVO.getNodeId()));
            result.setUnit(getIndicatorUnit(indicatorCodes));
            
            log.info("总能耗趋势查询完成，数据点数量: {}", dailyEnergyDataList.size());
            return result;

        } catch (Exception e) {
            log.error("查询总能耗趋势数据失败，厂站ID: {}, 节点ID: {}", reqVO.getFactoryId(), reqVO.getNodeId(), e);
            return createEmptyTrendResult(reqVO.getNodeId());
        }
    }

    @Override
    public EnergyCompareRespVO getEnergyYearOnYearCompare(EnergyAnalysisReqVO reqVO) {
        return null;
    }

    @Override
    public EnergyTrendRespVO getEnergyPerTonTrend(EnergyAnalysisReqVO reqVO) {
        return null;
    }

    @Override
    public EnergyRingChartRespVO getEnergyCompare(EnergyAnalysisReqVO reqVO) {
        return null;
    }


    /**
     * 根据节点ID列表查询指标编码
     */
    private List<String> getIndicatorCodesByNodeIds(List<Long> nodeIds) {
        List<EnergyIndicatorDO> indicators = energyIndicatorMapper.selectList(
                new LambdaQueryWrapper<EnergyIndicatorDO>()
                        .in(EnergyIndicatorDO::getNodeId, nodeIds)
                        .eq(EnergyIndicatorDO::getDeleted, false)
        );

        return indicators.stream()
                .map(EnergyIndicatorDO::getIndicatorCode)
                .filter(code -> code != null && !code.trim().isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * 计算增长率
     */
    private BigDecimal calculateGrowthRate(BigDecimal current, BigDecimal previous) {
        if (previous == null || previous.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        if (current == null) {
            current = BigDecimal.ZERO;
        }

        // 增长率 = (当前值 - 上期值) / 上期值 * 100
        return current.subtract(previous)
                .divide(previous, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"))
                .setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 获取节点指标编码列表
     * 优先获取当前节点绑定的指标，如果当前节点没有绑定指标，则获取所有子节点绑定的指标
     *
     * @param nodeId 节点ID
     * @return 指标编码列表，如果没有任何指标则返回null
     */
    private List<String> getIndicatorCodesForNode(Long nodeId) {
        // 1. 优先获取当前节点所绑定的指标
        List<EnergyIndicatorRespVO> indicatorsByNodeId = energyIndicatorService.getIndicatorsByNodeId(nodeId);
        List<String> indicatorCodes;

        if (!CollectionUtils.isEmpty(indicatorsByNodeId)) {
            // 当前节点绑定了指标，直接提取编码
            indicatorCodes = indicatorsByNodeId.stream()
                    .map(EnergyIndicatorRespVO::getIndicatorCode)
                    .collect(Collectors.toList());
            log.info("当前节点已绑定指标，节点ID: {}, 指标编码: {}", nodeId, indicatorCodes);
        } else {
            // 当前节点未绑定指标，查询所有子节点并获取其绑定指标
            List<Long> childNodeIds = energyNodeService.getAllChildNodeIds(nodeId);
            log.info("当前节点未绑定指标，查询子节点，节点ID列表: {}", childNodeIds);

            if (CollectionUtils.isEmpty(childNodeIds)) {
                log.warn("未找到子节点，节点ID: {}", nodeId);
                return null;
            }

            indicatorCodes = getIndicatorCodesByNodeIds(childNodeIds);
            log.info("子节点指标编码: {}", indicatorCodes);

            if (CollectionUtils.isEmpty(indicatorCodes)) {
                log.warn("子节点未绑定任何指标，节点ID列表: {}", childNodeIds);
                return null;
            }
        }
        
        return indicatorCodes;
    }

    /**
     * 获取节点名称
     */
    private String getNodeName(Long nodeId) {
        try {
            EnergyNodeDO node = energyNodeService.getById(nodeId);
            return node != null ? node.getName() : "未知节点";
        } catch (Exception e) {
            log.warn("获取节点名称失败，节点ID: {}", nodeId, e);
            return "未知节点";
        }
    }

    /**
     * 获取指标单位（取第一个指标的单位）
     */
    private String getIndicatorUnit(List<String> indicatorCodes) {
        if (CollectionUtils.isEmpty(indicatorCodes)) {
            return "kWh";
        }

        try {
            EnergyIndicatorDO indicator = energyIndicatorMapper.selectOne(
                    new LambdaQueryWrapper<EnergyIndicatorDO>()
                            .eq(EnergyIndicatorDO::getIndicatorCode, indicatorCodes.get(0))
                            .eq(EnergyIndicatorDO::getDeleted, false)
                            .last("LIMIT 1")
            );

            return indicator != null && indicator.getUnit() != null ? indicator.getUnit() : "kWh";
        } catch (Exception e) {
            log.warn("获取指标单位失败，指标编码: {}", indicatorCodes.get(0), e);
            return "kWh";
        }
    }

    /**
     * 创建空结果
     */
    private EnergyAnalysisRespVO createEmptyResult(Long nodeId) {
        EnergyAnalysisRespVO result = new EnergyAnalysisRespVO();
        result.setCurrentTotal(BigDecimal.ZERO);
        result.setLastYearTotal(BigDecimal.ZERO);
        result.setLastPeriodTotal(BigDecimal.ZERO);
        result.setYearOnYearGrowthRate(BigDecimal.ZERO);
        result.setPeriodOnPeriodGrowthRate(BigDecimal.ZERO);
        result.setEnergyPerTon(null);
        result.setLastYearEnergyPerTon(null);
        result.setLastPeriodEnergyPerTon(null);
        result.setUnit("kWh");
        result.setTimeRangeDesc(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy年MM月")));
        result.setIndicatorName("能耗统计");
        result.setNodeName(getNodeName(nodeId));

        return result;
    }

    /**
     * 创建空的能耗趋势结果
     */
    private EnergyTrendRespVO createEmptyTrendResult(Long nodeId) {
        EnergyTrendRespVO result = new EnergyTrendRespVO();
        result.setTimeAxis(Collections.emptyList());
        result.setEnergyData(Collections.emptyList());
        result.setUnit("kWh");
        result.setNodeName(getNodeName(nodeId));
        return result;
    }
}
