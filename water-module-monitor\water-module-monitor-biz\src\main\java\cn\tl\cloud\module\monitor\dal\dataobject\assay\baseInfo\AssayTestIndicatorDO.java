package cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo;

import cn.tl.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 检测指标 DO
 *
 * <AUTHOR>
 */
@TableName("assay_test_indicator")
@KeySequence("assay_test_indicator_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssayTestIndicatorDO extends BaseDO {

    /**
     * 指标ID
     */
    @TableId
    private Long id;
    
    /**
     * 水厂ID
     */
    private Long factoryId;
    
    /**
     * 指标代码
     */
    private String code;
    
    /**
     * 指标名称
     */
    private String name;
    
    /**
     * 所属项目ID
     */
    private Long projectId;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 检测方法
     */
    private String method;
    
    /**
     * 检测设备
     */
    private String equipment;
    
    /**
     * 标准下限
     */
    private BigDecimal standardMin;
    
    /**
     * 标准上限
     */
    private BigDecimal standardMax;
    
    /**
     * 精度要求
     */
    private String precisionLimit;
    
    /**
     * 所需样品量(mL)
     */
    private Integer sampleVolume;
    
    /**
     * 检测耗时(分钟)
     */
    private Integer detectionTimeMinutes;
    
    /**
     * 是否启用
     */
    private Boolean isEnabled;
    
    /**
     * 扩展字段，存储额外的业务数据
     */
    private String extraField;

}
