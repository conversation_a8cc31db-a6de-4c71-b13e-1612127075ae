package cn.tl.cloud.module.report.service.report.prodqualitydata;

import cn.hutool.core.lang.Pair;
import cn.tl.cloud.framework.common.exception.ErrorCode;
import cn.tl.cloud.framework.common.exception.ServiceException;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.framework.security.core.util.SecurityFrameworkUtils;
import cn.tl.cloud.module.report.controller.admin.indicator.indicatordata.vo.IndicatorDataSaveReqVO;
import cn.tl.cloud.module.report.controller.admin.report.vo.consumption.MonthDataReqVO;
import cn.tl.cloud.module.report.controller.admin.report.vo.quality.DateRangeReqVO;
import cn.tl.cloud.module.report.controller.admin.report.vo.quality.ProdQualityDataPageReqVO;
import cn.tl.cloud.module.report.controller.admin.report.vo.quality.ProdQualityDataSaveReqVO;
import cn.tl.cloud.module.report.controller.admin.report.vo.quality.ProdQualityStatRespVO;
import cn.tl.cloud.module.report.dal.dataobject.stat.ProdQualityDataDO;
import cn.tl.cloud.module.report.dal.mysql.indicator.IndicatorDataMapper;
import cn.tl.cloud.module.report.dal.mysql.indicator.IndicatorReviewFlowMapper;
import cn.tl.cloud.module.report.dal.mysql.prodqualitydata.ProdQualityDataMapper;
import cn.tl.cloud.module.report.service.indicator.IndicatorDataService;
import cn.tl.cloud.module.system.api.permission.PermissionApi;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.tl.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.tl.cloud.module.report.common.contants.FactoryConstant.REPORT_MODULE_CODE;
import static cn.tl.cloud.module.report.common.contants.FactoryConstant.REPORT_ROLE_CODE_FILLER;
import static cn.tl.cloud.module.report.enums.ErrorCodeConstants.PROD_QUALITY_DATA_NOT_EXISTS;

/**
 * 进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProdQualityDataServiceImpl extends ServiceImpl<ProdQualityDataMapper, ProdQualityDataDO> implements ProdQualityDataService {

    @Resource
    private ProdQualityDataMapper prodQualityDataMapper;

    @Resource
    private IndicatorDataMapper indicatorDataMapper;

    @Resource
    private IndicatorDataService indicatorDataService;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private IndicatorReviewFlowMapper indicatorReviewFlowMapper;

    @Override
    public Long createProdQualityData(ProdQualityDataSaveReqVO createReqVO) {
        // 插入
        ProdQualityDataDO prodQualityData = BeanUtils.toBean(createReqVO, ProdQualityDataDO.class);
        prodQualityDataMapper.insert(prodQualityData);
        // 返回
        return prodQualityData.getId();
    }

    @Override
    public void updateProdQualityData(ProdQualityDataSaveReqVO updateReqVO) {

        // 获取当前填报人
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        Long factoryId = updateReqVO.getFactoryId();

        // 权限校验
        hasFactoryOperationPermission(currentUserId, factoryId);

        // 校验存在
        validateProdQualityDataExists(updateReqVO.getId());
        // 更新
        ProdQualityDataDO updateObj = BeanUtils.toBean(updateReqVO, ProdQualityDataDO.class);
        prodQualityDataMapper.updateById(updateObj);
    }

    @Override
    public void deleteProdQualityData(Long id) {
        // 校验存在
        validateProdQualityDataExists(id);
        // 删除
        prodQualityDataMapper.deleteById(id);
    }

    private void validateProdQualityDataExists(Long id) {
        if (prodQualityDataMapper.selectById(id) == null) {
            throw exception(PROD_QUALITY_DATA_NOT_EXISTS);
        }
    }

    @Override
    public ProdQualityDataDO getProdQualityData(Long id) {
        return prodQualityDataMapper.selectById(id);
    }

    @Override
    public PageResult<ProdQualityDataDO> getProdQualityDataPage(ProdQualityDataPageReqVO pageReqVO) {
        return prodQualityDataMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ProdQualityDataDO> getProdQualityDataList(MonthDataReqVO monthDataReqVO) {
        // 解析月份（如 "2025-04"）获取起止日期
        String month = monthDataReqVO.getMonth(); // 格式为 yyyy-MM
        LocalDate startDate = LocalDate.parse(month + "-01");
        LocalDate endDate = startDate.with(TemporalAdjusters.lastDayOfMonth());
        // 查询数据
        return prodQualityDataMapper.selectList(
                Wrappers.lambdaQuery(ProdQualityDataDO.class)
                        .eq(ProdQualityDataDO::getFactoryId, monthDataReqVO.getFactoryId())
                        .between(ProdQualityDataDO::getDate, startDate, endDate)
                        .orderByAsc(ProdQualityDataDO::getDate)
        );
    }

    @Override
    public List<ProdQualityDataDO> getProdQualityDataByDateRange(DateRangeReqVO reqVO) {
        return prodQualityDataMapper.selectList(Wrappers.lambdaQuery(ProdQualityDataDO.class).eq(ProdQualityDataDO::getFactoryId, reqVO.getFactoryId())
                .between(ProdQualityDataDO::getDate, reqVO.getStartDate(), reqVO.getEndDate())
                .orderByAsc(ProdQualityDataDO::getDate));
    }

    /**
     * 查询所有生产水质的统计/汇总数据
     *
     * @param factoryId
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public List<ProdQualityStatRespVO> getQualityReport(Long factoryId, LocalDate startDate, LocalDate endDate) {
        // 判断是否为 2024 年且查询整月数据
        if (startDate.getYear() == 2024 && endDate.getYear() == 2024 && isFullMonth(startDate, endDate)) {
            // 查询聚合表 water_prod_quality_stat
            Map<String, Object> statMap = prodQualityDataMapper.selectStatFromStatTable(factoryId, startDate, endDate);
            ProdQualityStatRespVO result = convertMapToStatRespVO(statMap, factoryId);
            return Collections.singletonList(result);
        } else {
            // 查询原始表 water_prod_quality_data 后进行统计计算
            List<ProdQualityDataDO> dataList = prodQualityDataMapper.selectDataFromDataTable(factoryId, startDate, endDate);
            ProdQualityStatRespVO result = computeStats(dataList, factoryId);
            return Collections.singletonList(result);
        }
    }

    /**
     * 计算统计值
     */
    private ProdQualityStatRespVO computeStats(List<ProdQualityDataDO> dataList, Long factoryId) {
        ProdQualityStatRespVO result = new ProdQualityStatRespVO();
        result.setFactoryId(factoryId);

        Field[] fields = ProdQualityDataDO.class.getDeclaredFields();
        for (Field field : fields) {
            if (!field.getType().equals(BigDecimal.class)) continue;

            field.setAccessible(true);
            List<BigDecimal> values = dataList.stream()
                    .map(d -> {
                        try {
                            return (BigDecimal) field.get(d);
                        } catch (IllegalAccessException e) {
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            ProdQualityStatRespVO.StatValue stat = calculateStat(values, field.getName());

            try {
                Field respField = ProdQualityStatRespVO.class.getDeclaredField(field.getName());
                respField.setAccessible(true);
                respField.set(result, stat);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                // 字段可能不匹配，记录日志或忽略
            }
        }

        return result;
    }

    /**
     * 计算最大值、最小值、平均值和总和
     *
     * @param values    数值列表
     * @param fieldName 字段名，用于判断是否为流量字段
     */
    private ProdQualityStatRespVO.StatValue calculateStat(List<BigDecimal> values, String fieldName) {
        ProdQualityStatRespVO.StatValue stat = new ProdQualityStatRespVO.StatValue();

        if (CollectionUtils.isEmpty(values)) {
            stat.setAvg(BigDecimal.ZERO);
            stat.setMax(BigDecimal.ZERO);
            stat.setMin(BigDecimal.ZERO);
            // 只有流量字段才设置累计值
            if (isVolumeField(fieldName)) {
                stat.setTotal(BigDecimal.ZERO);
            }
            return stat;
        }

        BigDecimal max = values.stream().max(Comparator.naturalOrder()).orElse(BigDecimal.ZERO);
        BigDecimal min = values.stream().min(Comparator.naturalOrder()).orElse(BigDecimal.ZERO);
        BigDecimal total = values.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal avg = total.divide(BigDecimal.valueOf(values.size()), 2, RoundingMode.HALF_UP);

        stat.setMax(max);
        stat.setMin(min);
        stat.setAvg(avg);

        // 只有流量字段才设置累计值
        if (isVolumeField(fieldName)) {
            stat.setTotal(total);
        }

        return stat;
    }

    /**
     * 判断是否为流量字段（需要计算累计值的字段）
     */
    private boolean isVolumeField(String fieldName) {
        // 支持Java字段名（驼峰命名）和数据库字段名（下划线命名）
        return "inFlowWaterVolume".equals(fieldName) || "dailyTreatmentVol".equals(fieldName) ||
                "in_flow_water_volume".equals(fieldName) || "daily_treatment_vol".equals(fieldName);
    }

    /**
     * 判断查询日期范围是否为整月
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return true表示是整月查询，false表示不是整月查询
     */
    private boolean isFullMonth(LocalDate startDate, LocalDate endDate) {
        // 检查开始日期是否为月初（1号）
        if (startDate.getDayOfMonth() != 1) {
            return false;
        }

        // 检查结束日期是否为月末
        LocalDate lastDayOfMonth = startDate.with(TemporalAdjusters.lastDayOfMonth());
        return endDate.equals(lastDayOfMonth);
    }

    /**
     * 将Map数据转换为ProdQualityStatRespVO对象
     */
    private ProdQualityStatRespVO convertMapToStatRespVO(Map<String, Object> statMap, Long factoryId) {
        if (statMap == null || statMap.isEmpty()) {
            ProdQualityStatRespVO emptyResult = new ProdQualityStatRespVO();
            emptyResult.setFactoryId(factoryId);
            return emptyResult;
        }

        ProdQualityStatRespVO result = new ProdQualityStatRespVO();
        result.setFactoryId(factoryId);

        // 进水指标
        result.setInPh(createStatValue(statMap, "in_ph"));
        result.setInTemp(createStatValue(statMap, "in_temp"));
        result.setInCodcr(createStatValue(statMap, "in_codcr"));
        result.setInBod5(createStatValue(statMap, "in_bod5"));
        result.setInSs(createStatValue(statMap, "in_ss"));
        result.setInNh3n(createStatValue(statMap, "in_nh3n"));
        result.setInTn(createStatValue(statMap, "in_tn"));
        result.setInTp(createStatValue(statMap, "in_tp"));

        // 出水指标
        result.setOutPh(createStatValue(statMap, "out_ph"));
        result.setOutTemp(createStatValue(statMap, "out_temp"));
        result.setOutCodcr(createStatValue(statMap, "out_codcr"));
        result.setOutBod5(createStatValue(statMap, "out_bod5"));
        result.setOutSs(createStatValue(statMap, "out_ss"));
        result.setOutNh3n(createStatValue(statMap, "out_nh3n"));
        result.setOutTn(createStatValue(statMap, "out_tn"));
        result.setOutTp(createStatValue(statMap, "out_tp"));

        // 流量指标
        result.setInFlowWaterVolume(createStatValue(statMap, "in_flow_water_volume"));
        result.setDailyTreatmentVol(createStatValue(statMap, "daily_treatment_vol"));

        return result;
    }

    /**
     * 从Map中创建StatValue对象
     */
    private ProdQualityStatRespVO.StatValue createStatValue(Map<String, Object> statMap, String prefix) {
        ProdQualityStatRespVO.StatValue statValue = new ProdQualityStatRespVO.StatValue();

        Object avgObj = statMap.get(prefix + "_avg");
        Object maxObj = statMap.get(prefix + "_max");
        Object minObj = statMap.get(prefix + "_min");
        Object totalObj = statMap.get(prefix + "_total");

        statValue.setAvg(avgObj != null ? new BigDecimal(avgObj.toString()) : null);
        statValue.setMax(maxObj != null ? new BigDecimal(maxObj.toString()) : null);
        statValue.setMin(minObj != null ? new BigDecimal(minObj.toString()) : null);

        // 只有流量字段才设置累计值
        if (isVolumeField(prefix)) {
            statValue.setTotal(totalObj != null ? new BigDecimal(totalObj.toString()) : null);
        }

        return statValue;
    }

    @Override
    @Transactional
    public List<Long> saveOrUpdateBatch(List<ProdQualityDataSaveReqVO> reqList) {
        if (CollectionUtils.isEmpty(reqList)) {
            return Collections.emptyList();
        }

        // 获取当前用户信息
        /*填写或审核均会经过此方法
                填写时，填写人id在此处不处理,在indicatorData相关方法中写入
                审核时，indicatorData相关方法构造的reqList中的每个对象有填写人id；审核人id将在归档时写入
                故当宽表中填写人id为空时，审核人id就是填写人id*/
        //或者填写人id在前端赋值
        //或者填写人id在前端赋值
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        Long factoryId = reqList.get(0).getFactoryId();
        // 默认审核阶段为 1（初审）
        int flowStage = 1;

        // 权限校验
        boolean isAdmin = hasFactoryOperationPermission(currentUserId, factoryId);

        // 判断是否已审核
        boolean alreadyReviewed = reqList.get(0).getReviewStatus() == 1;
        // 是否直接归档
        // 如果是管理员或者已经审核就直接归档
        boolean shouldArchive = isAdmin || alreadyReviewed;

        // 如果是管理员或审核流程走完，数据直接归档到宽表[审核状态为已审核]
        // 设置审核状态和审核人
        boolean finalIsAdmin = isAdmin;
        reqList.forEach(vo -> {
            vo.setReviewStatus(shouldArchive ? 1 : 0);
            if (finalIsAdmin) {
                vo.setReviewerId(currentUserId);
            }
        });

        if (isAdmin) {
            flowStage = 4; // 管理员归档阶段
        } else if (alreadyReviewed) {
            flowStage = 2; // 普通用户归档阶段
        }
        //存入宽表
        List<Long> resultIds = saveUpdateWideTable(reqList);
        //避免审核通过二次创建
        /*什么时候走这个逻辑
         *   仅当所有用户第一次提交【】
         * */
        // 若为首次提交或当前用户具有管理员权限，则生成并保存指标数据
        Integer reviewStatus = reqList.get(0).getReviewStatus();
        boolean isFirstSubmission = reviewStatus == 0;
        boolean reporterIsAdmin = Boolean.TRUE.equals(
                permissionApi.hasAnyRoles(reqList.get(0).getReporterId(), "report_admin", "super_admin").getData());
        if (isFirstSubmission || reporterIsAdmin) {
            reviewStatus = reviewStatus == 0 ? 2 : 3;
            List<IndicatorDataSaveReqVO> indicatorDataList = createIndicatorDataByReportData(reqList, shouldArchive ? 1 : 0,
                    reviewStatus);
            // 2. 调用指标数据服务批量保存
            if (!indicatorDataList.isEmpty()) {
                return indicatorDataService.saveOrUpdateBatch(indicatorDataList, flowStage);
            }
        }
        return resultIds;
    }

    private List<Long> saveUpdateWideTable(List<ProdQualityDataSaveReqVO> reqList) {
        if (CollectionUtils.isEmpty(reqList)) {
            return Collections.emptyList();
        }

        // 组装 key：factoryId + date
        List<Pair<Long, LocalDate>> keys = reqList.stream()
                .map(vo -> Pair.of(vo.getFactoryId(), vo.getDate()))
                .collect(Collectors.toList());

        // 查询已有数据
        LambdaQueryWrapper<ProdQualityDataDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(wrapper -> {
            for (Pair<Long, LocalDate> key : keys) {
                wrapper.or(w -> w.eq(ProdQualityDataDO::getFactoryId, key.getKey())
                        .eq(ProdQualityDataDO::getDate, key.getValue()));
            }
        });

        List<ProdQualityDataDO> existingList = prodQualityDataMapper.selectList(queryWrapper);

        // 构建已存在的数据映射
        Map<String, ProdQualityDataDO> existingMap = existingList.stream()
                .collect(Collectors.toMap(
                        e -> e.getFactoryId() + "_" + e.getDate(),
                        Function.identity()
                ));

        // 分类处理新增和更新
        List<ProdQualityDataDO> toInsert = new ArrayList<>();
        List<ProdQualityDataDO> toUpdate = new ArrayList<>();
        List<Long> resultIds = new ArrayList<>();
        for (ProdQualityDataSaveReqVO reqVO : reqList) {
            String key = reqVO.getFactoryId() + "_" + reqVO.getDate();

            ProdQualityDataDO existing = new ProdQualityDataDO();
            BeanUtils.copyProperties(reqVO, existing);

            if (existingMap.containsKey(key)) {
               /* ProdQualityDataDO existing = existingMap.get(key);
                BeanUtils.copyProperties(reqVO, existing);*/
                existing.setId(existingMap.get(key).getId());
                toUpdate.add(existing);
                resultIds.add(existing.getId());
            } else {
                ProdQualityDataDO entity = new ProdQualityDataDO();
                BeanUtils.copyProperties(reqVO, entity);
                toInsert.add(entity);
            }
        }

        if (!toInsert.isEmpty()) {
            this.saveBatch(toInsert);
            resultIds.addAll(toInsert.stream().map(ProdQualityDataDO::getId).collect(Collectors.toList()));
        }

        if (!toUpdate.isEmpty()) {
            this.updateBatchById(toUpdate);
        }
        return resultIds;

    }

    private final Map<String, Function<ProdQualityDataSaveReqVO, BigDecimal>> FIELD_EXTRACTORS = createFieldExtractors();


    private Map<String, Function<ProdQualityDataSaveReqVO, BigDecimal>> createFieldExtractors() {
        Map<String, Function<ProdQualityDataSaveReqVO, BigDecimal>> map = new HashMap<>();
        map.put("进水水温（℃）", ProdQualityDataSaveReqVO::getInTemp);
        map.put("进水CODcr（mg/L）", ProdQualityDataSaveReqVO::getInCodcr);
        map.put("进水BOD5（mg/L）", ProdQualityDataSaveReqVO::getInBod5);
        map.put("进水pH", ProdQualityDataSaveReqVO::getInPh);
        map.put("进水SS（mg/L）", ProdQualityDataSaveReqVO::getInSs);
        map.put("进水NH3-N（mg/L）", ProdQualityDataSaveReqVO::getInNh3n);
        map.put("进水TN（mg/L）", ProdQualityDataSaveReqVO::getInTn);
        map.put("进水TP（mg/L）", ProdQualityDataSaveReqVO::getInTp);
        map.put("出水pH", ProdQualityDataSaveReqVO::getOutPh);
        map.put("出水水温（℃）", ProdQualityDataSaveReqVO::getOutTemp);
        map.put("出水CODcr（mg/L）", ProdQualityDataSaveReqVO::getOutCodcr);
        map.put("出水BOD5（mg/L）", ProdQualityDataSaveReqVO::getOutBod5);
        map.put("出水SS（mg/L）", ProdQualityDataSaveReqVO::getOutSs);
        map.put("出水NH3-N（mg/L）", ProdQualityDataSaveReqVO::getOutNh3n);
        map.put("出水TN（mg/L）", ProdQualityDataSaveReqVO::getOutTn);
        map.put("出水TP（mg/L）", ProdQualityDataSaveReqVO::getOutTp);
        map.put("进水流量（m³）", ProdQualityDataSaveReqVO::getInFlowWaterVolume);
        map.put("出水流量（m³）", ProdQualityDataSaveReqVO::getDailyTreatmentVol);
        return map;
    }


    private List<IndicatorDataSaveReqVO> createIndicatorDataByReportData(List<ProdQualityDataSaveReqVO> reqList,
                                                                         Integer isArchive, Integer reviewStatus) {

        List<IndicatorDataSaveReqVO> indicatorDataList = new ArrayList<>();

        for (ProdQualityDataSaveReqVO reqVO : reqList) {
            for (Map.Entry<String, Function<ProdQualityDataSaveReqVO, BigDecimal>> entry : FIELD_EXTRACTORS.entrySet()) {
                BigDecimal value = entry.getValue().apply(reqVO);
                if (value != null) {
                    indicatorDataList.add(IndicatorDataSaveReqVO.builder()
                            .factoryId(reqVO.getFactoryId())
                            .bizType("quality")
                            .indicatorName(entry.getKey())
                            .indicatorValue(value)
                            .reportDate(reqVO.getDate())
                            .reporterId(reqVO.getReporterId())
                            .isArchived(isArchive)
                            .status(reviewStatus)
                            .build());
                }
            }
        }

        return indicatorDataList;
    }

    @Override
    public List<Long> insert(List<IndicatorDataSaveReqVO> indicatorDataSaveReqVOList) {

        List<ProdQualityDataSaveReqVO> voList = new ArrayList<>();

        for (IndicatorDataSaveReqVO vo : indicatorDataSaveReqVOList) {
            ProdQualityDataSaveReqVO dataSaveReqVO = new ProdQualityDataSaveReqVO();
            dataSaveReqVO.setReporterId(SecurityFrameworkUtils.getLoginUser().getId());
            dataSaveReqVO.setFactoryId(vo.getFactoryId());
            dataSaveReqVO.setDate(LocalDate.now());

            dataSaveReqVO.setReporterId(SecurityFrameworkUtils.getLoginUser().getId());

            String field = vo.getIndicatorName();
            if (field != null) {
                try {
                    // 动态生成 setter 方法名
                    String setterMethodName = "set" + field.substring(0, 1).toUpperCase() + field.substring(1);
                    // 获取对应的 setter 方法
                    Method setterMethod = dataSaveReqVO.getClass().getMethod(setterMethodName, vo.getIndicatorValue().getClass());
                    // 调用 setter 方法设置值
                    setterMethod.invoke(dataSaveReqVO, vo.getIndicatorValue());
                    voList.add(dataSaveReqVO);
                } catch (Exception e) {
                    e.printStackTrace(); // 处理异常，可以根据需求做更详细的错误处理
                }
            }
        }
        return saveOrUpdateBatch(voList);
    }

    @Override
    @Transactional
    public List<Long> additionalRecording(List<ProdQualityDataSaveReqVO> reqVOList) {
        // 获取当前填报人
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        Long factoryId = reqVOList.get(0).getFactoryId();

        // 权限校验
        hasFactoryOperationPermission(currentUserId, factoryId);

        reqVOList.forEach(reqVO -> {
            //设置为已补录
            reqVO.setReviewStatus(2);
        });
        List<Long> res = saveUpdateWideTable(reqVOList);
        //直接归档并且新增审核状态与审核流程为“补录：5”
        Integer isArchive = 1;
        Integer reviewStatus = 5;
        Integer flowStage = 5;
        List<IndicatorDataSaveReqVO> indicatorDataList = createIndicatorDataByReportData(reqVOList, isArchive, reviewStatus);
        // 2. 调用指标数据服务批量保存
        if (!indicatorDataList.isEmpty()) {
            return indicatorDataService.saveOrUpdateBatch(indicatorDataList, flowStage);
        }
        return res;
    }

    @Override
    public List<Long> saveTemporarily(List<ProdQualityDataSaveReqVO> createReqVOList) {
        // 如果为空，直接返回空list
        if (CollectionUtils.isEmpty(createReqVOList)) {
            return Collections.emptyList();
        }

        // 获取当前填报人
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        Long factoryId = createReqVOList.get(0).getFactoryId();
        // 权限校验
        hasFactoryOperationPermission(currentUserId, factoryId);


        createReqVOList.forEach(reqVO -> {
            reqVO.setReviewStatus(5); // 记录为暂存状态
            reqVO.setReporterId(currentUserId);
        });

        return saveUpdateWideTable(createReqVOList);
    }


    /**
     * 提取权限校验方法
     *
     * @param userId
     * @param factoryId
     * @return
     */
    private boolean hasFactoryOperationPermission(Long userId, Long factoryId) {
        boolean isAdmin;
        try {
            isAdmin = Boolean.TRUE.equals(permissionApi.hasAnyRoles(userId, "report_admin", "super_admin").getData());
        } catch (Exception e) {
            // 权限检查失败时，默认为非管理员
            isAdmin = false;
        }

        // 判断是否有模块功能权限
        if (!isAdmin) {
            Boolean hasOperationPermission = permissionApi.hasFactoryOperationPermission(userId, factoryId, REPORT_MODULE_CODE, REPORT_ROLE_CODE_FILLER);
            if (!hasOperationPermission) {
                throw new ServiceException(new ErrorCode(40100, "操作失败：当前用户没有权限进行该厂站填报操作"));
            }
        }

        return isAdmin;
    }


}
