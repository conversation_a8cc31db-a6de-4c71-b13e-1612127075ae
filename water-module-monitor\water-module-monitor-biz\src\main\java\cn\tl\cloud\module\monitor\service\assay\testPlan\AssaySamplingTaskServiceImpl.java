package cn.tl.cloud.module.monitor.service.assay.testPlan;

import cn.tl.cloud.framework.common.exception.util.ServiceExceptionUtil;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo.AssayTaskAssignReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo.AssayTaskGenerateReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo.AssaySamplingTaskPageReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.testPlan.AssaySamplingPlanDO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.testPlan.AssaySamplingTaskDO;
import cn.tl.cloud.module.monitor.dal.mysql.assay.testPlan.AssaySamplingPlanMapper;
import cn.tl.cloud.module.monitor.dal.mysql.assay.testPlan.AssaySamplingTaskMapper;
import cn.tl.cloud.module.monitor.enums.ErrorCodeConstants;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 采样任务 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssaySamplingTaskServiceImpl implements AssaySamplingTaskService {

    @Resource
    private AssaySamplingTaskMapper samplingTaskMapper;
    
    @Resource
    private AssaySamplingPlanMapper samplingPlanMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> generateTasksFromPlan(@Valid AssayTaskGenerateReqVO generateReqVO) {
        // 校验计划存在
        AssaySamplingPlanDO plan = validatePlanExists(generateReqVO.getPlanId(), generateReqVO.getFactoryId());
        
        // 根据计划类型生成任务
        List<AssaySamplingTaskDO> tasks = new ArrayList<>();
        
        if ("regular".equals(plan.getType())) {
            // 常规计划：根据频率和日期范围生成任务
            tasks = generateRegularTasks(plan, generateReqVO.getStartDate(), generateReqVO.getEndDate());
        } else if ("temporary".equals(plan.getType())) {
            // 临时计划：生成单个任务
            tasks = generateTemporaryTasks(plan);
        }
        
        // 批量插入任务
        if (!tasks.isEmpty()) {
            for (AssaySamplingTaskDO task : tasks) {
                samplingTaskMapper.insert(task);
            }
        }
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("taskIds", tasks.stream().map(AssaySamplingTaskDO::getId).toArray());
        result.put("totalCount", tasks.size());
        result.put("message", "成功生成" + tasks.size() + "个采样任务");
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> assignTask(@Valid AssayTaskAssignReqVO assignReqVO) {
        // 校验任务存在且状态为pending
        AssaySamplingTaskDO task = samplingTaskMapper.selectById(assignReqVO.getTaskId());
        if (task == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_TASK_NOT_EXISTS);
        }
        if (!"pending".equals(task.getStatus())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_TASK_STATUS_ERROR);
        }
        
        // 校验人员不能相同（如果重新分配了人员）
        Long samplerId = assignReqVO.getSamplerId() != null ? assignReqVO.getSamplerId() : task.getSamplerId();
        Long testerId = assignReqVO.getTesterId() != null ? assignReqVO.getTesterId() : task.getTesterId();
        Long reviewerId = assignReqVO.getReviewerId() != null ? assignReqVO.getReviewerId() : task.getReviewerId();
        
        if (samplerId.equals(testerId) || samplerId.equals(reviewerId) || testerId.equals(reviewerId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_TASK_ASSIGN_SAME_PERSON_ERROR);
        }
        
        // 更新任务信息
        AssaySamplingTaskDO updateObj = new AssaySamplingTaskDO();
        updateObj.setId(assignReqVO.getTaskId());
        updateObj.setStatus("sampling");
        updateObj.setStartTime(LocalDateTime.now());
        
        if (assignReqVO.getSamplerId() != null) {
            updateObj.setSamplerId(assignReqVO.getSamplerId());
        }
        if (assignReqVO.getTesterId() != null) {
            updateObj.setTesterId(assignReqVO.getTesterId());
        }
        if (assignReqVO.getReviewerId() != null) {
            updateObj.setReviewerId(assignReqVO.getReviewerId());
        }
        if (assignReqVO.getAssignReason() != null) {
            updateObj.setRemark(assignReqVO.getAssignReason());
        }
        
        samplingTaskMapper.updateById(updateObj);
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("taskId", assignReqVO.getTaskId());
        result.put("status", "sampling");
        result.put("assignTime", LocalDateTime.now());
        
        return result;
    }

    @Override
    public AssaySamplingTaskDO getSamplingTask(Long id, Long factoryId) {
        AssaySamplingTaskDO task = samplingTaskMapper.selectById(id);
        if (task == null) {
            return null;
        }
        if (!task.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FACTORY_DATA_ACCESS_DENIED);
        }
        return task;
    }

    @Override
    public PageResult<AssaySamplingTaskDO> getSamplingTaskPage(AssaySamplingTaskPageReqVO pageReqVO) {
        return samplingTaskMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AssaySamplingTaskDO> getSamplingTaskList(AssaySamplingTaskPageReqVO reqVO) {
        return samplingTaskMapper.selectList(reqVO);
    }

    @Override
    public Map<String, Object> getTaskDetail(Long id, Long factoryId) {
        // 校验任务存在
        AssaySamplingTaskDO task = getSamplingTask(id, factoryId);
        if (task == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_TASK_NOT_EXISTS);
        }
        
        // TODO: 查询任务详情（包含关联信息和状态历史）
        Map<String, Object> result = new HashMap<>();
        result.put("taskInfo", task);
        result.put("statusHistory", new ArrayList<>());
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskStatus(Long taskId, String status, String remark) {
        AssaySamplingTaskDO updateObj = new AssaySamplingTaskDO();
        updateObj.setId(taskId);
        updateObj.setStatus(status);
        updateObj.setRemark(remark);
        
        if ("completed".equals(status)) {
            updateObj.setCompleteTime(LocalDateTime.now());
        }
        
        return samplingTaskMapper.updateById(updateObj) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateTaskStatus(List<Long> taskIds, String status, String remark) {
        int count = 0;
        for (Long taskId : taskIds) {
            if (updateTaskStatus(taskId, status, remark)) {
                count++;
            }
        }
        return count;
    }

    @Override
    public List<AssaySamplingTaskDO> getTasksByPlanId(Long planId) {
        return samplingTaskMapper.selectByPlanId(planId);
    }

    @Override
    public List<AssaySamplingTaskDO> getTasksByStatus(Long factoryId, String status) {
        return samplingTaskMapper.selectByStatus(factoryId, status);
    }

    private AssaySamplingPlanDO validatePlanExists(Long planId, Long factoryId) {
        AssaySamplingPlanDO plan = samplingPlanMapper.selectById(planId);
        if (plan == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_TASK_PLAN_NOT_EXISTS);
        }
        if (!plan.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FACTORY_DATA_ACCESS_DENIED);
        }
        return plan;
    }

    private List<AssaySamplingTaskDO> generateRegularTasks(AssaySamplingPlanDO plan, LocalDate startDate, LocalDate endDate) {
        List<AssaySamplingTaskDO> tasks = new ArrayList<>();
        
        LocalDate currentDate = startDate != null ? startDate : plan.getStartDate();
        LocalDate finalDate = endDate != null ? endDate : plan.getEndDate();
        
        while (!currentDate.isAfter(finalDate)) {
            AssaySamplingTaskDO task = createTaskFromPlan(plan, currentDate.atTime(8, 0));
            tasks.add(task);
            
            // 根据频率计算下一个日期
            switch (plan.getFrequency()) {
                case "daily":
                    currentDate = currentDate.plusDays(1);
                    break;
                case "weekly":
                    currentDate = currentDate.plusWeeks(1);
                    break;
                case "monthly":
                    currentDate = currentDate.plusMonths(1);
                    break;
                case "quarterly":
                    currentDate = currentDate.plusMonths(3);
                    break;
                default:
                    currentDate = currentDate.plusDays(1);
                    break;
            }
        }
        
        return tasks;
    }

    private List<AssaySamplingTaskDO> generateTemporaryTasks(AssaySamplingPlanDO plan) {
        List<AssaySamplingTaskDO> tasks = new ArrayList<>();
        AssaySamplingTaskDO task = createTaskFromPlan(plan, plan.getPlanDatetime());
        tasks.add(task);
        return tasks;
    }

    private AssaySamplingTaskDO createTaskFromPlan(AssaySamplingPlanDO plan, LocalDateTime taskDatetime) {
        AssaySamplingTaskDO task = new AssaySamplingTaskDO();
        
        // 生成任务编号
        String taskCode = generateTaskCode(plan.getFactoryId(), plan.getId(), taskDatetime);
        task.setTaskCode(taskCode);
        
        // 复制计划信息
        task.setFactoryId(plan.getFactoryId());
        task.setPlanId(plan.getId());
        task.setTaskDatetime(taskDatetime);
        task.setTestItem(plan.getTestItem());
        task.setSamplingPoint(plan.getSamplingPoint());
        task.setSamplerId(plan.getSamplerId());
        task.setTesterId(plan.getTesterId());
        task.setReviewerId(plan.getReviewerId());
        task.setPriority(plan.getPriority());
        task.setExpectedSampleQuantity(plan.getExpectedSampleQuantity());
        task.setExpectedSampleNature(plan.getExpectedSampleNature());
        task.setExpectedSampleAppearance(plan.getExpectedSampleAppearance());
        task.setExpectedSupernatant(plan.getExpectedSupernatant());
        task.setSamplingInstructions(plan.getSamplingInstructions());
        task.setStatus("pending");
        task.setRemark("计划启用自动生成任务");
        
        return task;
    }

    private String generateTaskCode(Long factoryId, Long planId, LocalDateTime taskDatetime) {
        String dateStr = taskDatetime.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return String.format("TASK-%d-%s-%03d", planId, dateStr, 
            (int)(System.currentTimeMillis() % 1000));
    }

}
