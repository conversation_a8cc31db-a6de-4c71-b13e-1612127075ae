package cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling;

import cn.tl.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 样品管理 DO
 *
 * <AUTHOR>
 */
@TableName("assay_sample_management")
@KeySequence("assay_sample_management_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssaySampleManagementDO extends BaseDO {

    /**
     * 样品ID
     */
    @TableId
    private Long id;
    
    /**
     * 水厂ID
     */
    private Long factoryId;
    
    /**
     * 样品编号
     */
    private String sampleCode;
    
    /**
     * 关联采样执行ID
     */
    private Long executionId;
    
    /**
     * 样品类型(water-水样,sludge-污泥,gas-气体)
     */
    private String sampleType;
    
    /**
     * 样品体积(mL)
     */
    private Integer volume;
    
    /**
     * 样品外观
     */
    private String appearance;
    
    /**
     * 保存方法(normal-常温,refrigerated-冷藏,frozen-冷冻)
     */
    private String preservationMethod;
    
    /**
     * 存储位置
     */
    private String storageLocation;
    
    /**
     * 存储温度(℃)
     */
    private Integer storageTemperature;
    
    /**
     * 有效期
     */
    private LocalDate expiryDate;
    
    /**
     * 检测项目ID
     */
    private Long testItem;
    
    /**
     * 采样人员ID
     */
    private Long samplingPersonId;
    
    /**
     * 采样日期
     */
    private LocalDate samplingDate;
    
    /**
     * 状态(stored-已入库,testing-检测中,completed-已完成,destroyed-已销毁)
     */
    private String status;
    
    /**
     * 销毁原因
     */
    private String destroyReason;
    
    /**
     * 销毁时间
     */
    private LocalDateTime destroyTime;
    
    /**
     * 销毁操作人ID
     */
    private Long destroyOperatorId;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 扩展字段，存储额外的业务数据
     */
    private String extraField;

}
