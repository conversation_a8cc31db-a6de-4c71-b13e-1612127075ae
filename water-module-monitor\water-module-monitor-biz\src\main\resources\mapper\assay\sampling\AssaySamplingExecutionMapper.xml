<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.tl.cloud.module.monitor.dal.mysql.assay.sampling.AssaySamplingExecutionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySamplingExecutionDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="factory_id" property="factoryId" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="sampling_location" property="samplingLocation" jdbcType="VARCHAR"/>
        <result column="actual_sampling_time" property="actualSamplingTime" jdbcType="TIMESTAMP"/>
        <result column="sampling_condition" property="samplingCondition" jdbcType="VARCHAR"/>
        <result column="actual_sample_quantity" property="actualSampleQuantity" jdbcType="INTEGER"/>
        <result column="actual_sample_appearance" property="actualSampleAppearance" jdbcType="VARCHAR"/>
        <result column="sample_status" property="sampleStatus" jdbcType="VARCHAR"/>
        <result column="abnormal_reason" property="abnormalReason" jdbcType="VARCHAR"/>
        <result column="handle_measures" property="handleMeasures" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="complete_time" property="completeTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="extra_field" property="extraField" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="BIT"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, factory_id, task_id, sampling_location, actual_sampling_time, sampling_condition,
        actual_sample_quantity, actual_sample_appearance, sample_status, abnormal_reason,
        handle_measures, status, complete_time, remark, extra_field,
        creator, create_time, updater, update_time, deleted
    </sql>

    <!-- 查询执行详情（包含任务信息） -->
    <select id="selectExecutionWithTaskInfo" parameterType="java.lang.Long" resultType="java.util.Map">
        SELECT 
            e.*,
            t.task_code,
            t.task_datetime,
            t.priority as task_priority,
            t.expected_sample_quantity,
            t.expected_sample_nature,
            t.expected_sample_appearance,
            t.expected_supernatant,
            t.sampling_instructions,
            sp.name as sampling_point_name,
            ti.name as test_item_name
        FROM assay_sampling_execution e
        LEFT JOIN assay_sampling_task t ON e.task_id = t.id
        LEFT JOIN assay_sampling_point sp ON t.sampling_point = sp.id
        LEFT JOIN assay_test_indicator ti ON t.test_item = ti.id
        WHERE e.id = #{id} AND e.deleted = 0
    </select>

    <!-- 统计执行状态分布 -->
    <select id="selectStatusStatistics" parameterType="java.lang.Long" resultType="java.util.Map">
        SELECT 
            status,
            COUNT(*) as count
        FROM assay_sampling_execution
        WHERE factory_id = #{factoryId}
          AND deleted = 0
        GROUP BY status
    </select>

    <!-- 查询异常执行记录 -->
    <select id="selectAbnormalExecutions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM assay_sampling_execution
        WHERE factory_id = #{factoryId}
          AND (status = 'abnormal' OR sample_status = 'abnormal')
          AND deleted = 0
        ORDER BY actual_sampling_time DESC
    </select>

    <!-- 查询待处理的执行记录 -->
    <select id="selectPendingExecutions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM assay_sampling_execution
        WHERE factory_id = #{factoryId}
          AND status IN ('pending', 'processing')
          AND deleted = 0
        ORDER BY create_time ASC
    </select>

</mapper>
