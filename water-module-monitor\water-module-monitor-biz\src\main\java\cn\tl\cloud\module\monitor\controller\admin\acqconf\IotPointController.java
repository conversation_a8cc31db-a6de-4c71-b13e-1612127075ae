package cn.tl.cloud.module.monitor.controller.admin.acqconf;

import cn.tl.cloud.framework.common.pojo.CommonResult;
import cn.tl.cloud.module.monitor.controller.admin.acqconf.vo.IotPointPageReqVO;
import cn.tl.cloud.module.monitor.service.acqconf.IotService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 *
 */
@RestController
@RequestMapping("/acq-conf/iot-point")
public class IotPointController {

    @Resource
    private IotService iotService;

    @Operation(summary = "分页查询点位列表")
    @PostMapping("/point-page")
    public CommonResult pointPage(@RequestBody IotPointPageReqVO reqVO) {
        return CommonResult.success(iotService.pointPage(reqVO));
    }

}
