package cn.tl.cloud.module.monitor.service.monitor.alarm.rule;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.rule.vo.RulePageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.rule.vo.RuleSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.monitor.alarm.rule.RuleDO;
import cn.tl.cloud.module.monitor.dal.dataobject.monitor.alarm.rulefactor.RuleFactorDO;
import cn.tl.cloud.module.monitor.dal.dataobject.monitor.ods.OdsIotDataDO;


import javax.validation.Valid;
import java.util.List;

/**
 * 告警规则 Service 接口
 *
 * <AUTHOR>
 */
public interface RuleService {

    /**
     * 创建告警规则
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRule(@Valid RuleSaveReqVO createReqVO);

    /**
     * 更新告警规则
     *
     * @param updateReqVO 更新信息
     */
    void updateRule(@Valid RuleSaveReqVO updateReqVO);

    /**
     * 删除告警规则
     *
     * @param id 编号
     */
    void deleteRule(Long id);

    /**
     * 获得告警规则
     *
     * @param id 编号
     * @return 告警规则
     */
    RuleDO getRule(Long id);

    /**
     * 获得告警规则分页
     *
     * @param pageReqVO 分页查询
     * @return 告警规则分页
     */
    PageResult<RuleDO> getRulePage(RulePageReqVO pageReqVO);


    /**
     * 获得告警规则因子表（组合表达式变量配置/监测因子）列表
     *
     * @param ruleId 关联告警规则ID
     * @return 告警规则因子表（组合表达式变量配置/监测因子）列表
     */
    List<RuleFactorDO> getRuleFactorListByRuleId(Long ruleId);



    /**
     * 触发告警规则
     */
    void evaluateAlarmRulesByFactory(String factoryId, List<OdsIotDataDO> odsIotDataDOList);

    //TODO:测试功能
    void evaluateAlarmRulesByFactory(String factoryId);

    /**
     * 获取带ruleFactors的rule用于编辑
     * @param id
     * @return
     */
    RuleSaveReqVO getRuleAndRuleFactors(Long id);
}
