package cn.tl.cloud.module.report.dal.mysql.prodqualitydata;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tl.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tl.cloud.module.report.controller.admin.report.vo.quality.ProdQualityDataPageReqVO;
import cn.tl.cloud.module.report.dal.dataobject.stat.ProdQualityDataDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProdQualityDataMapper extends BaseMapperX<ProdQualityDataDO> {

    default PageResult<ProdQualityDataDO> selectPage(ProdQualityDataPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProdQualityDataDO>()
                .betweenIfPresent(ProdQualityDataDO::getDate, reqVO.getDate())
                .eqIfPresent(ProdQualityDataDO::getFactoryId, reqVO.getFactoryId())
                .eqIfPresent(ProdQualityDataDO::getReporterId, reqVO.getReporterId())
                .eqIfPresent(ProdQualityDataDO::getReviewerId, reqVO.getReviewerId())
                .eqIfPresent(ProdQualityDataDO::getReviewStatus, reqVO.getReviewStatus())
                .eqIfPresent(ProdQualityDataDO::getInPh, reqVO.getInPh())
                .eqIfPresent(ProdQualityDataDO::getInTemp, reqVO.getInTemp())
                .eqIfPresent(ProdQualityDataDO::getInCodcr, reqVO.getInCodcr())
                .eqIfPresent(ProdQualityDataDO::getInBod5, reqVO.getInBod5())
                .eqIfPresent(ProdQualityDataDO::getInSs, reqVO.getInSs())
                .eqIfPresent(ProdQualityDataDO::getInNh3n, reqVO.getInNh3n())
                .eqIfPresent(ProdQualityDataDO::getInTn, reqVO.getInTn())
                .eqIfPresent(ProdQualityDataDO::getInTp, reqVO.getInTp())
                .eqIfPresent(ProdQualityDataDO::getOutPh, reqVO.getOutPh())
                .eqIfPresent(ProdQualityDataDO::getOutTemp, reqVO.getOutTemp())
                .eqIfPresent(ProdQualityDataDO::getOutCodcr, reqVO.getOutCodcr())
                .eqIfPresent(ProdQualityDataDO::getOutBod5, reqVO.getOutBod5())
                .eqIfPresent(ProdQualityDataDO::getOutSs, reqVO.getOutSs())
                .eqIfPresent(ProdQualityDataDO::getOutNh3n, reqVO.getOutNh3n())
                .eqIfPresent(ProdQualityDataDO::getOutTn, reqVO.getOutTn())
                .eqIfPresent(ProdQualityDataDO::getOutTp, reqVO.getOutTp())
                .eqIfPresent(ProdQualityDataDO::getInFlowWaterVolume, reqVO.getInFlowWaterVolume())
                .eqIfPresent(ProdQualityDataDO::getDailyTreatmentVol, reqVO.getDailyTreatmentVol())
                .betweenIfPresent(ProdQualityDataDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProdQualityDataDO::getId));
    }

    // 查询聚合数据表（2024年专用）- 返回Map便于处理复杂嵌套对象
    Map<String, Object> selectStatFromStatTable(@Param("factoryId") Long factoryId,
                                               @Param("startDate") LocalDate startDate,
                                               @Param("endDate") LocalDate endDate);

    // 查询原始数据表进行统计计算
    List<ProdQualityDataDO> selectDataFromDataTable(@Param("factoryId") Long factoryId,
                                                    @Param("startDate") LocalDate startDate,
                                                    @Param("endDate") LocalDate endDate);
}