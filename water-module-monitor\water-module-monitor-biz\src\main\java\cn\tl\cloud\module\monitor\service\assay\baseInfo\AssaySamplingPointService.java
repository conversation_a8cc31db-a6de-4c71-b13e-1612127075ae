package cn.tl.cloud.module.monitor.service.assay.baseInfo;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssaySamplingPointPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssaySamplingPointSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssaySamplingPointDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 采样点 Service 接口
 *
 * <AUTHOR>
 */
public interface AssaySamplingPointService {

    /**
     * 创建采样点
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSamplingPoint(@Valid AssaySamplingPointSaveReqVO createReqVO);

    /**
     * 更新采样点
     *
     * @param updateReqVO 更新信息
     */
    void updateSamplingPoint(@Valid AssaySamplingPointSaveReqVO updateReqVO);

    /**
     * 删除采样点
     *
     * @param id 编号
     * @param factoryId 水厂ID
     */
    void deleteSamplingPoint(Long id, Long factoryId);

    /**
     * 获得采样点
     *
     * @param id 编号
     * @param factoryId 水厂ID
     * @return 采样点
     */
    AssaySamplingPointDO getSamplingPoint(Long id, Long factoryId);

    /**
     * 获得采样点分页
     *
     * @param pageReqVO 分页查询
     * @return 采样点分页
     */
    PageResult<AssaySamplingPointDO> getSamplingPointPage(AssaySamplingPointPageReqVO pageReqVO);

    /**
     * 获得采样点列表
     *
     * @param reqVO 查询条件
     * @return 采样点列表
     */
    List<AssaySamplingPointDO> getSamplingPointList(AssaySamplingPointPageReqVO reqVO);

    /**
     * 根据类型获取采样点列表
     *
     * @param factoryId 水厂ID
     * @param type 类型
     * @return 采样点列表
     */
    List<AssaySamplingPointDO> getSamplingPointListByType(Long factoryId, String type);

    /**
     * 获得采样点精简列表
     *
     * @param factoryId 水厂ID
     * @return 采样点精简列表
     */
    List<AssaySamplingPointDO> getSamplingPointSimpleList(Long factoryId);

}
