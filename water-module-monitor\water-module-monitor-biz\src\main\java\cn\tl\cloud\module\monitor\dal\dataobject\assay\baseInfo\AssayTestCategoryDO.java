package cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo;

import cn.tl.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 检测项目类型 DO
 *
 * <AUTHOR>
 */
@TableName("assay_test_category")
@KeySequence("assay_test_category_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssayTestCategoryDO extends BaseDO {

    /**
     * 类型ID
     */
    @TableId
    private Long id;
    
    /**
     * 水厂ID
     */
    private Long factoryId;
    
    /**
     * 类型代码
     */
    private String code;
    
    /**
     * 类型名称
     */
    private String name;
    
    /**
     * 类型描述
     */
    private String description;
    
    /**
     * 是否启用
     */
    private Boolean isEnabled;

}
