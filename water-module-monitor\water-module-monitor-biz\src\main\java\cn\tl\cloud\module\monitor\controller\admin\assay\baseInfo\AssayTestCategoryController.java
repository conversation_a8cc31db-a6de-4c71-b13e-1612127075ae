package cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo;

import cn.tl.cloud.framework.common.pojo.CommonResult;
import cn.tl.cloud.framework.common.pojo.PageParam;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.framework.excel.core.util.ExcelUtils;

import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestCategoryPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestCategoryRespVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestCategorySaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestCategoryDO;
import cn.tl.cloud.module.monitor.service.assay.baseInfo.AssayTestCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.tl.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 检测项目类型")
@RestController
@RequestMapping("/monitor/assay-test-category")
@Validated
public class AssayTestCategoryController {

    @Resource
    private AssayTestCategoryService testCategoryService;

    @PostMapping("/create")
    @Operation(summary = "创建检测项目类型")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-category:create')")
    public CommonResult<Long> createTestCategory(@Valid @RequestBody AssayTestCategorySaveReqVO createReqVO) {
        return success(testCategoryService.createTestCategory(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新检测项目类型")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-category:update')")
    public CommonResult<Boolean> updateTestCategory(@Valid @RequestBody AssayTestCategorySaveReqVO updateReqVO) {
        testCategoryService.updateTestCategory(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除检测项目类型")
    @Parameter(name = "id", description = "编号", required = true)
    @Parameter(name = "factoryId", description = "水厂ID", required = true)
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-category:delete')")
    public CommonResult<Boolean> deleteTestCategory(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        testCategoryService.deleteTestCategory(id, factoryId);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得检测项目类型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-category:query')")
    public CommonResult<AssayTestCategoryRespVO> getTestCategory(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        AssayTestCategoryDO testCategory = testCategoryService.getTestCategory(id, factoryId);
        return success(BeanUtils.toBean(testCategory, AssayTestCategoryRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得检测项目类型分页")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-category:query')")
    public CommonResult<PageResult<AssayTestCategoryRespVO>> getTestCategoryPage(@Valid AssayTestCategoryPageReqVO pageReqVO) {
        PageResult<AssayTestCategoryDO> pageResult = testCategoryService.getTestCategoryPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssayTestCategoryRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得检测项目类型列表")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-category:query')")
    public CommonResult<List<AssayTestCategoryRespVO>> getTestCategoryList(@Valid AssayTestCategoryPageReqVO reqVO) {
        List<AssayTestCategoryDO> list = testCategoryService.getTestCategoryList(reqVO);
        return success(BeanUtils.toBean(list, AssayTestCategoryRespVO.class));
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获得检测项目类型精简列表")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-category:query')")
    public CommonResult<List<AssayTestCategoryRespVO>> getTestCategorySimpleList(@RequestParam("factoryId") Long factoryId) {
        List<AssayTestCategoryDO> list = testCategoryService.getTestCategorySimpleList(factoryId);
        return success(BeanUtils.toBean(list, AssayTestCategoryRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出检测项目类型 Excel")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-category:export')")

    public void exportTestCategoryExcel(@Valid AssayTestCategoryPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AssayTestCategoryDO> list = testCategoryService.getTestCategoryList(pageReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "检测项目类型.xls", "数据", AssayTestCategoryRespVO.class,
                        BeanUtils.toBean(list, AssayTestCategoryRespVO.class));
    }

}
