package cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling;

import cn.tl.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 采样执行 DO
 *
 * <AUTHOR>
 */
@TableName("assay_sampling_execution")
@KeySequence("assay_sampling_execution_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssaySamplingExecutionDO extends BaseDO {

    /**
     * 执行ID
     */
    @TableId
    private Long id;
    
    /**
     * 水厂ID
     */
    private Long factoryId;
    
    /**
     * 关联任务ID
     */
    private Long taskId;
    
    /**
     * 具体采样位置
     */
    private String samplingLocation;
    
    /**
     * 实际采样时间
     */
    private LocalDateTime actualSamplingTime;
    
    /**
     * 现场采样情况
     */
    private String samplingCondition;
    
    /**
     * 实际样品量(mL)
     */
    private Integer actualSampleQuantity;
    
    /**
     * 实际样品外观
     */
    private String actualSampleAppearance;
    
    /**
     * 样品状态(normal-正常,abnormal-异常)
     */
    private String sampleStatus;
    
    /**
     * 异常原因
     */
    private String abnormalReason;
    
    /**
     * 处理措施
     */
    private String handleMeasures;
    
    /**
     * 状态(pending-待采样,processing-采样中,completed-已完成,abnormal-异常)
     */
    private String status;
    
    /**
     * 完成时间
     */
    private LocalDateTime completeTime;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 扩展字段，存储额外的业务数据
     */
    private String extraField;

}
