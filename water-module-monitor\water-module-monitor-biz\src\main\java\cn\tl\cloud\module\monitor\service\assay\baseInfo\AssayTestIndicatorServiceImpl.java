package cn.tl.cloud.module.monitor.service.assay.baseInfo;

import cn.hutool.core.collection.CollUtil;
import cn.tl.cloud.framework.common.exception.util.ServiceExceptionUtil;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestIndicatorPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestIndicatorSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestCategoryDO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestIndicatorDO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestProjectDO;
import cn.tl.cloud.module.monitor.dal.mysql.assay.baseInfo.AssayTestCategoryMapper;
import cn.tl.cloud.module.monitor.dal.mysql.assay.baseInfo.AssayTestIndicatorMapper;
import cn.tl.cloud.module.monitor.dal.mysql.assay.baseInfo.AssayTestProjectMapper;
import cn.tl.cloud.module.monitor.enums.ErrorCodeConstants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 检测指标 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssayTestIndicatorServiceImpl implements AssayTestIndicatorService {

    @Resource
    private AssayTestIndicatorMapper testIndicatorMapper;
    
    @Resource
    private AssayTestProjectMapper testProjectMapper;
    
    @Resource
    private AssayTestCategoryMapper testCategoryMapper;

    @Override
    public Long createTestIndicator(@Valid AssayTestIndicatorSaveReqVO createReqVO) {
        // 校验项目存在
        validateProjectExists(createReqVO.getFactoryId(), createReqVO.getProjectId());
        
        // 校验代码唯一性
        validateCodeUnique(createReqVO.getFactoryId(), createReqVO.getCode(), null);
        
        // 校验标准值范围
        validateStandardRange(createReqVO.getStandardMin(), createReqVO.getStandardMax());
        
        // 插入
        AssayTestIndicatorDO testIndicator = BeanUtils.toBean(createReqVO, AssayTestIndicatorDO.class);
        testIndicatorMapper.insert(testIndicator);
        
        // 返回
        return testIndicator.getId();
    }

    @Override
    public void updateTestIndicator(@Valid AssayTestIndicatorSaveReqVO updateReqVO) {
        // 校验存在
        validateTestIndicatorExists(updateReqVO.getId(), updateReqVO.getFactoryId());
        
        // 校验项目存在
        validateProjectExists(updateReqVO.getFactoryId(), updateReqVO.getProjectId());
        
        // 校验代码唯一性
        validateCodeUnique(updateReqVO.getFactoryId(), updateReqVO.getCode(), updateReqVO.getId());
        
        // 校验标准值范围
        validateStandardRange(updateReqVO.getStandardMin(), updateReqVO.getStandardMax());
        
        // 更新
        AssayTestIndicatorDO updateObj = BeanUtils.toBean(updateReqVO, AssayTestIndicatorDO.class);
        testIndicatorMapper.updateById(updateObj);
    }

    @Override
    public void deleteTestIndicator(Long id, Long factoryId) {
        // 校验存在
        validateTestIndicatorExists(id, factoryId);
        
        // TODO: 校验是否被采样计划引用
        
        // 删除
        testIndicatorMapper.deleteById(id);
    }

    private void validateTestIndicatorExists(Long id, Long factoryId) {
        AssayTestIndicatorDO testIndicator = testIndicatorMapper.selectById(id);
        if (testIndicator == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.TEST_INDICATOR_NOT_EXISTS);
        }
        if (!testIndicator.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FACTORY_DATA_ACCESS_DENIED);
        }
    }

    private void validateProjectExists(Long factoryId, Long projectId) {
        AssayTestProjectDO project = testProjectMapper.selectById(projectId);
        if (project == null || !project.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.TEST_INDICATOR_PROJECT_NOT_EXISTS);
        }
    }

    private void validateCodeUnique(Long factoryId, String code, Long id) {
        AssayTestIndicatorDO testIndicator = testIndicatorMapper.selectByFactoryIdAndCode(factoryId, code);
        if (testIndicator == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的记录
        if (id == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.TEST_INDICATOR_CODE_DUPLICATE);
        }
        if (!testIndicator.getId().equals(id)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.TEST_INDICATOR_CODE_DUPLICATE);
        }
    }

    private void validateStandardRange(BigDecimal standardMin, BigDecimal standardMax) {
        if (standardMin != null && standardMax != null && standardMin.compareTo(standardMax) >= 0) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.TEST_INDICATOR_STANDARD_RANGE_ERROR);
        }
    }

    @Override
    public AssayTestIndicatorDO getTestIndicator(Long id, Long factoryId) {
        AssayTestIndicatorDO testIndicator = testIndicatorMapper.selectById(id);
        if (testIndicator == null) {
            return null;
        }
        if (!testIndicator.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FACTORY_DATA_ACCESS_DENIED);
        }
        return testIndicator;
    }

    @Override
    public PageResult<AssayTestIndicatorDO> getTestIndicatorPage(AssayTestIndicatorPageReqVO pageReqVO) {
        return testIndicatorMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AssayTestIndicatorDO> getTestIndicatorList(AssayTestIndicatorPageReqVO reqVO) {
        return testIndicatorMapper.selectList(reqVO);
    }

    @Override
    public List<AssayTestIndicatorDO> getTestIndicatorListByProjectId(Long factoryId, Long projectId) {
        return testIndicatorMapper.selectListByProjectId(factoryId, projectId);
    }


    @Override
    public PageResult<Map<String, Object>> getTestIndicatorTree(AssayTestIndicatorPageReqVO pageReqVO) {
        Long factoryId = pageReqVO.getFactoryId();

        // 1. 分页查询类型
        Page<AssayTestCategoryDO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<AssayTestCategoryDO> pagedCategory = testCategoryMapper.selectPageByFactoryId(page, factoryId);
        List<AssayTestCategoryDO> categories = pagedCategory.getRecords();

        if (CollUtil.isEmpty(categories)) {
            return new PageResult<>(Collections.emptyList(), 0L);
        }

        // 提前收集 typeId
        List<Long> categoryIds = categories.stream().map(AssayTestCategoryDO::getId).collect(Collectors.toList());

        // 2. 一次性查出这批类型下所有项目
        List<AssayTestProjectDO> allProjects = testProjectMapper.selectListByCategoryIds(factoryId, categoryIds);

        // 3. 查出这些项目下的所有指标
        List<Long> projectIds = allProjects.stream().map(AssayTestProjectDO::getId).collect(Collectors.toList());
        List<AssayTestIndicatorDO> allIndicators = projectIds.isEmpty()
                ? Collections.emptyList()
                : testIndicatorMapper.selectListByProjectIds(factoryId, projectIds);

        // 4. 分组
        Map<Long, List<AssayTestProjectDO>> projectMap = allProjects.stream()
                .collect(Collectors.groupingBy(AssayTestProjectDO::getCategoryId));

        Map<Long, List<AssayTestIndicatorDO>> indicatorMap = allIndicators.stream()
                .collect(Collectors.groupingBy(AssayTestIndicatorDO::getProjectId));

        // 5. 构建树结构
        List<Map<String, Object>> treeData = new ArrayList<>();

        for (AssayTestCategoryDO category : categories) {
            Map<String, Object> categoryNode = buildCategoryNode(category);

            List<Map<String, Object>> projectNodes = new ArrayList<>();
            for (AssayTestProjectDO project : projectMap.getOrDefault(category.getId(), Collections.emptyList())) {
                Map<String, Object> projectNode = buildProjectNode(project);

                List<Map<String, Object>> indicatorNodes = indicatorMap.getOrDefault(project.getId(), Collections.emptyList())
                        .stream()
                        .map(this::buildIndicatorNode)
                        .collect(Collectors.toList());

                projectNode.put("children", indicatorNodes);
                projectNodes.add(projectNode);
            }

            categoryNode.put("children", projectNodes);
            treeData.add(categoryNode);
        }

        return new PageResult<>(treeData, pagedCategory.getTotal());
    }
    private Map<String, Object> buildCategoryNode(AssayTestCategoryDO category) {
        Map<String, Object> node = new HashMap<>();
        node.put("id", category.getId());
        node.put("factoryId", category.getFactoryId());
        node.put("name", category.getName());
        node.put("code", category.getCode());
        node.put("type", "category");
        node.put("description", category.getDescription());
        node.put("isEnabled", category.getIsEnabled());
        node.put("hasChildren", true);
        return node;
    }

    private Map<String, Object> buildProjectNode(AssayTestProjectDO project) {
        Map<String, Object> node = new HashMap<>();
        node.put("id", project.getId());
        node.put("factoryId", project.getFactoryId());
        node.put("name", project.getName());
        node.put("code", project.getCode());
        node.put("type", "project");
        node.put("categoryId", project.getCategoryId());
        node.put("description", project.getDescription());
        node.put("isEnabled", project.getIsEnabled());
        node.put("hasChildren", true);
        return node;
    }

    private Map<String, Object> buildIndicatorNode(AssayTestIndicatorDO indicator) {
        Map<String, Object> node = new HashMap<>();
        node.put("id", indicator.getId());
        node.put("factoryId", indicator.getFactoryId());
        node.put("name", indicator.getName());
        node.put("code", indicator.getCode());
        node.put("type", "indicator");
        node.put("projectId", indicator.getProjectId());
        node.put("method", indicator.getMethod());
        node.put("unit", indicator.getUnit());
        node.put("standardMin", indicator.getStandardMin());
        node.put("standardMax", indicator.getStandardMax());
        node.put("equipment", indicator.getEquipment());
        node.put("precisionLimit", indicator.getPrecisionLimit());
        node.put("sampleVolume", indicator.getSampleVolume());
        node.put("detectionTimeMinutes", indicator.getDetectionTimeMinutes());
        node.put("isEnabled", indicator.getIsEnabled());
        node.put("hasChildren", false);
        return node;
    }





}
