<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.tl.cloud.module.monitor.dal.mysql.assay.baseInfo.AssayTestProjectMapper">
    <select id="selectListByCategoryIds" resultType="cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestProjectDO">
        SELECT * FROM assay_test_project
        WHERE factory_id = #{factoryId}
        AND category_id IN
        <foreach collection="categoryIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>