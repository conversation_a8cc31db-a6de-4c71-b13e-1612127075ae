package cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 采样执行新增/修改 Request VO")
@Data
public class AssaySamplingExecutionSaveReqVO {

    @Schema(description = "执行ID", example = "1")
    private Long id;

    @Schema(description = "水厂ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "水厂ID不能为空")
    private Long factoryId;

    @Schema(description = "关联任务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "关联任务ID不能为空")
    private Long taskId;

    @Schema(description = "具体采样位置", example = "进水口东侧2米处")
    @Size(max = 200, message = "具体采样位置长度不能超过200个字符")
    private String samplingLocation;

    @Schema(description = "实际采样时间", example = "2024-01-15T08:30:00")
    private LocalDateTime actualSamplingTime;

    @Schema(description = "现场采样情况", example = "天气晴朗，水质清澈")
    @Size(max = 1000, message = "现场采样情况长度不能超过1000个字符")
    private String samplingCondition;

    @Schema(description = "实际样品量(mL)", example = "100")
    private Integer actualSampleQuantity;

    @Schema(description = "实际样品外观", example = "微黄色，略浑浊")
    @Size(max = 500, message = "实际样品外观长度不能超过500个字符")
    private String actualSampleAppearance;

    @Schema(description = "样品状态", example = "normal")
    @Pattern(regexp = "^(normal|abnormal)$", message = "样品状态只能是normal或abnormal")
    private String sampleStatus;

    @Schema(description = "异常原因", example = "样品颜色异常")
    @Size(max = 1000, message = "异常原因长度不能超过1000个字符")
    private String abnormalReason;

    @Schema(description = "处理措施", example = "重新采样")
    @Size(max = 1000, message = "处理措施长度不能超过1000个字符")
    private String handleMeasures;

    @Schema(description = "备注", example = "正常采样")
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String remark;

}
