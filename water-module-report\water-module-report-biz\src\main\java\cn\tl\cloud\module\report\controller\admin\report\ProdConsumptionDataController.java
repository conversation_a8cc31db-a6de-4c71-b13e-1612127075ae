package cn.tl.cloud.module.report.controller.admin.report;

import cn.tl.cloud.framework.apilog.core.annotation.ApiAccessLog;
import cn.tl.cloud.framework.common.pojo.CommonResult;
import cn.tl.cloud.framework.common.pojo.PageParam;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.framework.excel.core.util.ExcelUtils;
import cn.tl.cloud.framework.idempotent.core.annotation.Idempotent;
import cn.tl.cloud.module.report.controller.admin.report.vo.consumption.*;
import cn.tl.cloud.module.report.dal.dataobject.stat.ProdConsumptionDataDO;
import cn.tl.cloud.module.report.service.report.prodconsumptiondata.ProdConsumptionDataService;
import cn.tl.cloud.module.report.service.report.supplement.DataSupplementConfigService;
import cn.tl.cloud.module.report.utils.ConsumptionStatConverter;
import cn.tl.cloud.module.system.api.permission.PermissionApi;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.tl.cloud.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.tl.cloud.framework.common.pojo.CommonResult.error;
import static cn.tl.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 报表生产消耗数据")
@RestController
@RequestMapping("/report/prod-consumption-data")
@Validated
public class ProdConsumptionDataController {

    @Resource
    private ProdConsumptionDataService prodConsumptionDataService;

    @Resource
    private DataSupplementConfigService dataSupplementConfigService;
    @Resource
    private PermissionApi permissionApi;

    @Deprecated
    @PostMapping("/create")
    @Operation(summary = "创建生产消耗数据表")
    @PreAuthorize("@ss.hasPermission('report:prod-consumption-data:create')")
    public CommonResult<Long> createProdConsumptionData(@Valid @RequestBody ProdConsumptionDataSaveReqVO createReqVO) {
        if(Objects.isNull(createReqVO)){
            return error(50010, "操作失败：数据为空");
        }
        return success(prodConsumptionDataService.createProdConsumptionData(createReqVO));
    }

    @Idempotent(timeout = 5,message =  "请勿重复提交")
    @PostMapping("/saveOrUpdateBatch")
    @Operation(summary = "批量创建生产消耗数据表")
    public CommonResult<List<Long>> saveOrUpdateProdConsumptionDataBatch(@Valid @RequestBody List<ProdConsumptionDataSaveReqVO> createReqVOList) {
        return success(prodConsumptionDataService.saveOrUpdateBatch(createReqVOList));
    }


    @Idempotent(timeout = 5, message = "请勿重复提交")
    @PostMapping("/saveTemporarily")
    @Operation(summary = "暂存生产消耗填报数据")
    public CommonResult<List<Long>> saveTemporarily(@Valid @RequestBody List<ProdConsumptionDataSaveReqVO> createReqVOList) {
        return success(prodConsumptionDataService.saveTemporarily(createReqVOList));
    }

    @Deprecated
    @PutMapping("/update")
    @Operation(summary = "更新生产消耗数据表")
    @PreAuthorize("@ss.hasPermission('report:prod-consumption-data:update')")
    public CommonResult<Boolean> updateProdConsumptionData(@Valid @RequestBody ProdConsumptionDataSaveReqVO updateReqVO) {
        if(Objects.isNull(updateReqVO)){
            return error(50010, "操作失败：数据为空");
        }
        prodConsumptionDataService.updateProdConsumptionData(updateReqVO);
        return success(true);
    }

    @Deprecated
    @DeleteMapping("/delete")
    @Operation(summary = "删除生产消耗数据表")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('report:prod-consumption-data:delete')")
    public CommonResult<Boolean> deleteProdConsumptionData(@RequestParam("id") Long id) {
        prodConsumptionDataService.deleteProdConsumptionData(id);
        return success(true);
    }

    @Deprecated
    @GetMapping("/get")
    @Operation(summary = "获得生产消耗数据表")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('report:prod-consumption-data:query')")
    public CommonResult<ProdConsumptionDataRespVO> getProdConsumptionData(@RequestParam("id") Long id) {
        ProdConsumptionDataDO prodConsumptionData = prodConsumptionDataService.getProdConsumptionData(id);
        return success(BeanUtils.toBean(prodConsumptionData, ProdConsumptionDataRespVO.class));
    }

    @Deprecated
    @GetMapping("/page")
    @Operation(summary = "分页查询获得生产消耗数据表")
    @PreAuthorize("@ss.hasPermission('report:prod-consumption-data:query')")
    public CommonResult<PageResult<ProdConsumptionDataRespVO>> getProdConsumptionDataPage(@Valid ProdConsumptionDataPageReqVO pageReqVO) {
        PageResult<ProdConsumptionDataDO> pageResult = prodConsumptionDataService.getProdConsumptionDataPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProdConsumptionDataRespVO.class));
    }

    @Idempotent(timeout = 5, message = "请勿重复提交")
    @Deprecated
    @GetMapping("/export-excel")
    @Operation(summary = "导出生产消耗数据表-excel")
    @PreAuthorize("@ss.hasPermission('report:prod-consumption-data:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProdConsumptionDataExcel(@Valid ProdConsumptionDataPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProdConsumptionDataDO> list = prodConsumptionDataService.getProdConsumptionDataPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "生产消耗数据表，记录每日的产量、能耗、原材料消耗和污泥处理情况.xls", "数据", ProdConsumptionDataRespVO.class,
                        BeanUtils.toBean(list, ProdConsumptionDataRespVO.class));
    }


    @Idempotent(timeout = 5, message = "请勿重复提交")
    @PostMapping("/additional-recording")
    @Operation(summary = "补录")
    public CommonResult<List<Long>> additionalRecording(@Valid @RequestBody List<ProdConsumptionDataSaveReqVO> createReqVOList) {
        if(CollectionUtils.isEmpty(createReqVOList)){
            return error(50010, "操作失败：数据为空");
        }
        List<LocalDate> dataDateList = createReqVOList.stream().map(ProdConsumptionDataSaveReqVO::getDate).collect(Collectors.toList());

        // 校验是否可以进行补录
        if (dataSupplementConfigService.isSupplementAllowed("consumption", dataDateList, LocalDateTime.now())) {
            return success(prodConsumptionDataService.additionalRecording(createReqVOList));
        }
        return error(500, "补录失败：数据日期或补录操作时间不在允许的范围内");
    }

    @PostMapping("/list")
    @Operation(summary = "获得生产消耗数据表-list")
    public CommonResult<List<ProdConsumptionDataReportRespVO>> getProdConsumptionDataList(@RequestBody MonthDataReqVO monthDataReqVO) {
        List<ProdConsumptionDataDO> pageResult = prodConsumptionDataService.getProdConsumptionDataList(monthDataReqVO);
        return success(BeanUtils.toBean(pageResult, ProdConsumptionDataReportRespVO.class));
    }


    /**
     * 查询所有水厂的数据
     */
    @Operation(summary = "查询所有生产消耗的统计/汇总数据")
    @GetMapping("/stat/all")
    public String getAllStats(
            @RequestParam("startDate")@DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate, @RequestParam("endDate")@DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {

        List<WaterProdConsumptionStats> allFactoryStats = prodConsumptionDataService.getAllFactoryStats(startDate, endDate);
        List<Object> list = new ArrayList<>();
        allFactoryStats.forEach(item -> {
            ConsumptionStatConverter.ProdConsumptionDataReportRespVO convert = ConsumptionStatConverter.convert(item);
            list.add(convert);
        });
        ObjectMapper objectMapper = new ObjectMapper();
        String s = null;
        try {
            s = objectMapper.writeValueAsString(list);
            return s;
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/consumption-stat")
    @Operation(summary = "生产消耗统计，查询平均值，最大值，最小值，累计值")
    @PermitAll
    public CommonResult<List<ProdConsumptionStatRespVO>> getConsumptionStatReport(
            @RequestParam("factoryId") Long factoryId,
            @RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        return success(prodConsumptionDataService.getQualityReport(factoryId, startDate, endDate));
    }

}
