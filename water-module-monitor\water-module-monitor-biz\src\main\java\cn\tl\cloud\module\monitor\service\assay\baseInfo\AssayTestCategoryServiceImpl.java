package cn.tl.cloud.module.monitor.service.assay.baseInfo;

import cn.tl.cloud.framework.common.exception.util.ServiceExceptionUtil;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestCategoryPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestCategorySaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestCategoryDO;
import cn.tl.cloud.module.monitor.dal.mysql.assay.baseInfo.AssayTestCategoryMapper;
import cn.tl.cloud.module.monitor.dal.mysql.assay.baseInfo.AssayTestProjectMapper;
import cn.tl.cloud.module.monitor.enums.ErrorCodeConstants;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 检测项目类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssayTestCategoryServiceImpl implements AssayTestCategoryService {

    @Resource
    private AssayTestCategoryMapper testCategoryMapper;
    
    @Resource
    private AssayTestProjectMapper testProjectMapper;

    @Override
    public Long createTestCategory(@Valid AssayTestCategorySaveReqVO createReqVO) {
        // 校验代码唯一性
        validateCodeUnique(createReqVO.getFactoryId(), createReqVO.getCode(), null);
        
        // 插入
        AssayTestCategoryDO testCategory = BeanUtils.toBean(createReqVO, AssayTestCategoryDO.class);
        testCategoryMapper.insert(testCategory);
        
        // 返回
        return testCategory.getId();
    }

    @Override
    public void updateTestCategory(@Valid AssayTestCategorySaveReqVO updateReqVO) {
        // 校验存在
        validateTestCategoryExists(updateReqVO.getId(), updateReqVO.getFactoryId());
        
        // 校验代码唯一性
        validateCodeUnique(updateReqVO.getFactoryId(), updateReqVO.getCode(), updateReqVO.getId());
        
        // 更新
        AssayTestCategoryDO updateObj = BeanUtils.toBean(updateReqVO, AssayTestCategoryDO.class);
        testCategoryMapper.updateById(updateObj);
    }

    @Override
    public void deleteTestCategory(Long id, Long factoryId) {
        // 校验存在
        validateTestCategoryExists(id, factoryId);
        
        // 校验是否存在关联的检测项目
        Long projectCount = testProjectMapper.selectCountByCategoryId(id);
        if (projectCount > 0) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.TEST_CATEGORY_HAS_PROJECTS);
        }
        
        // 删除
        testCategoryMapper.deleteById(id);
    }

    private void validateTestCategoryExists(Long id, Long factoryId) {
        AssayTestCategoryDO testCategory = testCategoryMapper.selectById(id);
        if (testCategory == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.TEST_CATEGORY_NOT_EXISTS);
        }
        if (!testCategory.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FACTORY_DATA_ACCESS_DENIED);
        }
    }

    private void validateCodeUnique(Long factoryId, String code, Long id) {
        AssayTestCategoryDO testCategory = testCategoryMapper.selectByFactoryIdAndCode(factoryId, code);
        if (testCategory == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的记录
        if (id == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.TEST_CATEGORY_CODE_DUPLICATE);
        }
        if (!testCategory.getId().equals(id)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.TEST_CATEGORY_CODE_DUPLICATE);
        }
    }

    @Override
    public AssayTestCategoryDO getTestCategory(Long id, Long factoryId) {
        AssayTestCategoryDO testCategory = testCategoryMapper.selectById(id);
        if (testCategory == null) {
            return null;
        }
        if (!testCategory.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FACTORY_DATA_ACCESS_DENIED);
        }
        return testCategory;
    }

    @Override
    public PageResult<AssayTestCategoryDO> getTestCategoryPage(AssayTestCategoryPageReqVO pageReqVO) {
        return testCategoryMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AssayTestCategoryDO> getTestCategoryList(AssayTestCategoryPageReqVO reqVO) {
        return testCategoryMapper.selectList(reqVO);
    }

    @Override
    public List<AssayTestCategoryDO> getTestCategorySimpleList(Long factoryId) {
        return testCategoryMapper.selectSimpleList(factoryId);
    }

}
