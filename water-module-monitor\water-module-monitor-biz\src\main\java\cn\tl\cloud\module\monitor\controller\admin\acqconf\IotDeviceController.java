package cn.tl.cloud.module.monitor.controller.admin.acqconf;

import cn.tl.cloud.framework.common.pojo.CommonResult;
import cn.tl.cloud.module.monitor.controller.admin.acqconf.vo.IotDevicePageReqVO;
import cn.tl.cloud.module.monitor.service.acqconf.IotService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 *
 */
@RestController
@RequestMapping("/acq-conf/iot-device")
public class IotDeviceController {


    @Resource
    private IotService iotService;

    /**
     * 统计厂站数量、设备数量、指标数量
     */
    @PostMapping("/statistics")
    public CommonResult statistics() {
        return CommonResult.success(iotService.statistics());
    }

    /**
     * 分页查询设备列表
     * @return
     */
    @PostMapping("/device-page")
    public CommonResult devicePage(@RequestBody IotDevicePageReqVO reqVO) {
        return CommonResult.success(iotService.devicePage(reqVO));
    }

}
