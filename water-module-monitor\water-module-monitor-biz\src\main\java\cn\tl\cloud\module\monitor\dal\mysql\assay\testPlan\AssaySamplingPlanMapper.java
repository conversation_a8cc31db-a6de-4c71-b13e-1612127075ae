package cn.tl.cloud.module.monitor.dal.mysql.assay.testPlan;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tl.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo.AssaySamplingPlanPageReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.testPlan.AssaySamplingPlanDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.util.List;

/**
 * 采样计划 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssaySamplingPlanMapper extends BaseMapperX<AssaySamplingPlanDO> {

    default PageResult<AssaySamplingPlanDO> selectPage(AssaySamplingPlanPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AssaySamplingPlanDO>()
                .eqIfPresent(AssaySamplingPlanDO::getFactoryId, reqVO.getFactoryId())
                .likeIfPresent(AssaySamplingPlanDO::getName, reqVO.getName())
                .eqIfPresent(AssaySamplingPlanDO::getType, reqVO.getType())
                .eqIfPresent(AssaySamplingPlanDO::getFrequency, reqVO.getFrequency())
                .eqIfPresent(AssaySamplingPlanDO::getPriority, reqVO.getPriority())
                .eqIfPresent(AssaySamplingPlanDO::getIsEnabled, reqVO.getIsEnabled())
                .eqIfPresent(AssaySamplingPlanDO::getSamplerId, reqVO.getSamplerId())
                .orderByDesc(AssaySamplingPlanDO::getId));
    }

    default List<AssaySamplingPlanDO> selectList(AssaySamplingPlanPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AssaySamplingPlanDO>()
                .eqIfPresent(AssaySamplingPlanDO::getFactoryId, reqVO.getFactoryId())
                .likeIfPresent(AssaySamplingPlanDO::getName, reqVO.getName())
                .eqIfPresent(AssaySamplingPlanDO::getType, reqVO.getType())
                .eqIfPresent(AssaySamplingPlanDO::getFrequency, reqVO.getFrequency())
                .eqIfPresent(AssaySamplingPlanDO::getPriority, reqVO.getPriority())
                .eqIfPresent(AssaySamplingPlanDO::getIsEnabled, reqVO.getIsEnabled())
                .eqIfPresent(AssaySamplingPlanDO::getSamplerId, reqVO.getSamplerId())
                .orderByDesc(AssaySamplingPlanDO::getId));
    }

    default AssaySamplingPlanDO selectByFactoryIdAndPlanCode(Long factoryId, String planCode) {
        return selectOne(new LambdaQueryWrapperX<AssaySamplingPlanDO>()
                .eq(AssaySamplingPlanDO::getFactoryId, factoryId)
                .eq(AssaySamplingPlanDO::getPlanCode, planCode));
    }

    default AssaySamplingPlanDO selectByFactoryIdAndName(Long factoryId, String name) {
        return selectOne(new LambdaQueryWrapperX<AssaySamplingPlanDO>()
                .eq(AssaySamplingPlanDO::getFactoryId, factoryId)
                .eq(AssaySamplingPlanDO::getName, name));
    }

    default List<AssaySamplingPlanDO> selectEnabledPlans(Long factoryId) {
        return selectList(new LambdaQueryWrapperX<AssaySamplingPlanDO>()
                .eq(AssaySamplingPlanDO::getFactoryId, factoryId)
                .eq(AssaySamplingPlanDO::getIsEnabled, true)
                .orderByDesc(AssaySamplingPlanDO::getId));
    }

    default List<AssaySamplingPlanDO> selectConflictPlans(Long factoryId, Long samplingPoint, 
                                                          LocalDate startDate, LocalDate endDate, 
                                                          Long excludePlanId) {
        LambdaQueryWrapper<AssaySamplingPlanDO> query = new LambdaQueryWrapper<AssaySamplingPlanDO>()
                .eq(AssaySamplingPlanDO::getFactoryId, factoryId)
                .eq(AssaySamplingPlanDO::getSamplingPoint, samplingPoint)
                .eq(AssaySamplingPlanDO::getIsEnabled, true)
                .and(wrapper -> wrapper
                        .and(w -> w.eq(AssaySamplingPlanDO::getType, "regular")
                                .le(AssaySamplingPlanDO::getStartDate, endDate)
                                .ge(AssaySamplingPlanDO::getEndDate, startDate))
                        .or(w -> w.eq(AssaySamplingPlanDO::getType, "temporary")
                                .between(AssaySamplingPlanDO::getPlanDatetime,
                                        startDate.atStartOfDay(), endDate.atTime(23, 59, 59))));
        if (excludePlanId != null) {
            query.ne(AssaySamplingPlanDO::getId, excludePlanId);
        }
        return selectList(query);


    }

}
