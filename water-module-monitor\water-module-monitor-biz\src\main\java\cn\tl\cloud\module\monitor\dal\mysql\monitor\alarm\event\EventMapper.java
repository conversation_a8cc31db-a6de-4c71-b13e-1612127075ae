package cn.tl.cloud.module.monitor.dal.mysql.monitor.alarm.event;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tl.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;

import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.event.vo.EventPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.event.vo.EventRespVO;
import cn.tl.cloud.module.monitor.dal.dataobject.monitor.alarm.actionlog.ActionLogDO;
import cn.tl.cloud.module.monitor.dal.dataobject.monitor.alarm.event.EventDO;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;


/**
 * 告警事件 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface EventMapper extends BaseMapperX<EventDO> {

    default PageResult<EventDO> selectPage(EventPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EventDO>()
                .eqIfPresent(EventDO::getRuleId, reqVO.getRuleId())
                .likeIfPresent(EventDO::getRuleName, reqVO.getRuleName())
                .eqIfPresent(EventDO::getFactoryId, reqVO.getFactoryId())
                .likeIfPresent(EventDO::getFactoryName, reqVO.getFactoryName())
                .eqIfPresent(EventDO::getDeviceId, reqVO.getDeviceId())
                .likeIfPresent(EventDO::getDeviceName, reqVO.getDeviceName())
                .betweenIfPresent(EventDO::getAlarmTime, reqVO.getAlarmTime())
                .eqIfPresent(EventDO::getFactorCode, reqVO.getFactorCode())
                .eqIfPresent(EventDO::getFactorType, reqVO.getFactorType())
                .likeIfPresent(EventDO::getFactorName, reqVO.getFactorName())
                .eqIfPresent(EventDO::getFactorValue, reqVO.getFactorValue())
                .eqIfPresent(EventDO::getRuleType, reqVO.getRuleType())
                .eqIfPresent(EventDO::getOperator, reqVO.getOperator())
                .eqIfPresent(EventDO::getThreshold, reqVO.getThreshold())
                .eqIfPresent(EventDO::getUnit, reqVO.getUnit())
                .eqIfPresent(EventDO::getExpression, reqVO.getExpression())
                .eqIfPresent(EventDO::getLevel, reqVO.getLevel())
                .eqIfPresent(EventDO::getStatus, reqVO.getStatus())
                .eqIfPresent(EventDO::getHandler, reqVO.getHandler())
                .betweenIfPresent(EventDO::getProcessTime, reqVO.getProcessTime())
                .eqIfPresent(EventDO::getNotifyStatus, reqVO.getNotifyStatus())
                .eqIfPresent(EventDO::getNotifyRecords, reqVO.getNotifyRecords())
                .betweenIfPresent(EventDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(EventDO::getId));
    }


    @Update("update alarm_event set status = #{actionLog.processStatus}, process_time = #{actionLog.processTime}, " +
            "handler = #{actionLog.handler} " +
            "WHERE id =" +
            " #{eventId}")
    void updateByAction(@Param("eventId") Long eventId, @Param("actionLog") ActionLogDO actionLog);

    @Select("select * from alarm_event ${ew.customSqlSegment}")
    List<EventRespVO> queryList(@Param(Constants.WRAPPER) LambdaQueryWrapperX<EventDO> lambdaQueryWrapperX);
}
