package cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling;

import cn.tl.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDate;

/**
 * 送检记录 DO
 *
 * <AUTHOR>
 */
@TableName("assay_submission_record")
@KeySequence("assay_submission_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssaySubmissionRecordDO extends BaseDO {

    /**
     * 记录ID
     */
    @TableId
    private Long id;
    
    /**
     * 水厂ID
     */
    private Long factoryId;
    
    /**
     * 记录编号
     */
    private String recordCode;
    
    /**
     * 关联采样执行ID
     */
    private Long executionId;
    
    /**
     * 送检日期
     */
    private LocalDate submissionDate;
    
    /**
     * 送检人员ID
     */
    private Long submissionPersonId;
    
    /**
     * 检测项目ID
     */
    private Long testItem;
    
    /**
     * 状态(unSubmitted-未送检,submitted-已送检)
     */
    private String status;
    
    /**
     * 送检单文件URL
     */
    private String fileUrl;
    
    /**
     * 备注信息
     */
    private String remark;
    
    /**
     * 扩展字段，存储额外的业务数据
     */
    private String extraField;

}
