package cn.tl.cloud.module.monitor.dal.mysql.acqconf;

import cn.tl.cloud.module.monitor.dal.dataobject.acqconf.IotGroup;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 *
 */
@Mapper
@DS("iot")
public interface IotGroupMapper {

    /**
     * 查询已开启的厂站个数
     * @return
     */
    @Select("select count(*) from dc3_group where deleted = 0 and enable_flag = 1 ")
    Long count();

    /**
     * 查询分组/厂站列表
     * @return
     */
    @Select(" select id,code as groupCode,group_name as groupName from dc3_group where deleted = 0 and enable_flag = 1 order by position asc")
    List<IotGroup> queryList();

}
