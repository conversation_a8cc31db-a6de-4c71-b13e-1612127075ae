package cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.event;

import cn.tl.cloud.framework.apilog.core.annotation.ApiAccessLog;
import cn.tl.cloud.framework.common.pojo.CommonResult;
import cn.tl.cloud.framework.common.pojo.PageParam;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.framework.excel.core.util.ExcelUtils;

import cn.tl.cloud.framework.signature.core.annotation.ApiSignature;
import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.event.vo.EventPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.event.vo.EventRespVO;
import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.event.vo.EventSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.monitor.alarm.event.EventDO;
import cn.tl.cloud.module.monitor.job.AlarmRuleJob;
import cn.tl.cloud.module.monitor.service.monitor.alarm.event.EventService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static cn.tl.cloud.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.tl.cloud.framework.common.pojo.CommonResult.success;



@Tag(name = "管理后台 - 告警事件")
@RestController
@RequestMapping("/monitor/alarm/event")
@Validated
public class EventController {

    @Resource
    private EventService eventService;

    @Resource
    private AlarmRuleJob alarmRuleJob;

    @PostMapping("/create")
    @Operation(summary = "创建告警事件")
    //@PreAuthorize("@ss.hasPermission('alarm:event:create')")
    public CommonResult<Long> createEvent(@Valid @RequestBody EventSaveReqVO createReqVO) {
        return success(eventService.createEvent(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新告警事件")
    //@PreAuthorize("@ss.hasPermission('alarm:event:update')")
    public CommonResult<Boolean> updateEvent(@Valid @RequestBody EventSaveReqVO updateReqVO) {
        eventService.updateEvent(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除告警事件")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermission('alarm:event:delete')")
    public CommonResult<Boolean> deleteEvent(@RequestParam("id") Long id) {
        eventService.deleteEvent(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得告警事件")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('alarm:event:query')")
    public CommonResult<EventRespVO> getEvent(@RequestParam("id") Long id) {
        EventDO event = eventService.getEvent(id);
        return success(BeanUtils.toBean(event, EventRespVO.class));
    }

    @GetMapping("/autoGet")
    @Operation(summary = "获得告警事件")
    @PermitAll
    //@PreAuthorize("@ss.hasPermission('alarm:event:query')")
    public void autoGetEvent() {
        alarmRuleJob.execute();

    }

    @GetMapping("/page")
    @Operation(summary = "获得告警事件分页")
    public CommonResult<PageResult<EventRespVO>> getEventPage(@Valid EventPageReqVO pageReqVO) {
        PageResult<EventDO> pageResult = eventService.getEventPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EventRespVO.class));
    }

    @GetMapping("/listPage")
    @Operation(summary = "获得告警事件分页")
    @PermitAll
    @ApiSignature(timeout = 10, timeUnit = TimeUnit.MINUTES,appId = "appId", timestamp = "timestamp", nonce = "nonce", sign = "sign")
    public CommonResult<PageResult<EventRespVO>> getEventPageExport(@Valid EventPageReqVO pageReqVO) {
        PageResult<EventDO> pageResult = eventService.getEventPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EventRespVO.class));
    }

    @GetMapping("/list-all")
    @Operation(summary = "获得告警事件列表")
    @PermitAll
    @ApiSignature(timeout = 10, timeUnit = TimeUnit.MINUTES,appId = "appId", timestamp = "timestamp", nonce = "nonce", sign = "sign")
    public CommonResult<List<EventRespVO>> listAll(EventPageReqVO reqVO) {
        List<EventRespVO> eventList = eventService.getEventList(reqVO);
        return success(eventList);
    }


    @GetMapping("/export-excel")
    @Operation(summary = "导出告警事件 Excel")
    //@PreAuthorize("@ss.hasPermission('alarm:event:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportEventExcel(@Valid EventPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<EventDO> list = eventService.getEventPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "告警事件.xls", "数据", EventRespVO.class,
                        BeanUtils.toBean(list, EventRespVO.class));
    }

}
