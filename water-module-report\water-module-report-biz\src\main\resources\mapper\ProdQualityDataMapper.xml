<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.tl.cloud.module.report.dal.mysql.prodqualitydata.ProdQualityDataMapper">

    <!-- 查询原始数据表进行统计计算 -->
    <select id="selectDataFromDataTable" resultType="cn.tl.cloud.module.report.dal.dataobject.stat.ProdQualityDataDO">
        SELECT
        id,
        date,
        factory_id,
        reporter_id,
        reviewer_id,
        review_status,
        in_ph,
        in_temp,
        in_codcr,
        in_bod5,
        in_ss,
        in_nh3n,
        in_tn,
        in_tp,
        out_ph,
        out_temp,
        out_codcr,
        out_bod5,
        out_ss,
        out_nh3n,
        out_tn,
        out_tp,
        in_flow_water_volume,
        daily_treatment_vol,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        FROM water_prod_quality_data
        WHERE deleted = 0
<!--        AND review_status IN (1, 2)-->
        AND factory_id = #{factoryId}
        AND date BETWEEN #{startDate} AND #{endDate}
        ORDER BY date ASC
    </select>

    <!-- 查询聚合统计表（2024年专用） -->
    <!-- 注意：由于MyBatis无法直接映射复杂嵌套对象，这里返回扁平化数据，在Service层进行组装 -->
    <select id="selectStatFromStatTable" resultType="java.util.Map">
        SELECT
        -- 进水指标
        ROUND(AVG(in_ph_avg), 4) AS in_ph_avg,
        MAX(in_ph_max) AS in_ph_max,
        MIN(in_ph_min) AS in_ph_min,
        SUM(in_ph_sum) AS in_ph_total,

        ROUND(AVG(in_temp_avg), 4) AS in_temp_avg,
        MAX(in_temp_max) AS in_temp_max,
        MIN(in_temp_min) AS in_temp_min,
        SUM(in_temp_sum) AS in_temp_total,

        ROUND(AVG(in_codcr_avg), 4) AS in_codcr_avg,
        MAX(in_codcr_max) AS in_codcr_max,
        MIN(in_codcr_min) AS in_codcr_min,
        SUM(in_codcr_sum) AS in_codcr_total,

        ROUND(AVG(in_bod5_avg), 4) AS in_bod5_avg,
        MAX(in_bod5_max) AS in_bod5_max,
        MIN(in_bod5_min) AS in_bod5_min,
        SUM(in_bod5_sum) AS in_bod5_total,

        ROUND(AVG(in_ss_avg), 4) AS in_ss_avg,
        MAX(in_ss_max) AS in_ss_max,
        MIN(in_ss_min) AS in_ss_min,
        SUM(in_ss_sum) AS in_ss_total,

        ROUND(AVG(in_nh3n_avg), 4) AS in_nh3n_avg,
        MAX(in_nh3n_max) AS in_nh3n_max,
        MIN(in_nh3n_min) AS in_nh3n_min,
        SUM(in_nh3n_sum) AS in_nh3n_total,

        ROUND(AVG(in_tn_avg), 4) AS in_tn_avg,
        MAX(in_tn_max) AS in_tn_max,
        MIN(in_tn_min) AS in_tn_min,
        SUM(in_tn_sum) AS in_tn_total,

        ROUND(AVG(in_tp_avg), 4) AS in_tp_avg,
        MAX(in_tp_max) AS in_tp_max,
        MIN(in_tp_min) AS in_tp_min,
        SUM(in_tp_sum) AS in_tp_total,

        -- 出水指标
        ROUND(AVG(out_ph_avg), 4) AS out_ph_avg,
        MAX(out_ph_max) AS out_ph_max,
        MIN(out_ph_min) AS out_ph_min,
        SUM(out_ph_sum) AS out_ph_total,

        ROUND(AVG(out_temp_avg), 4) AS out_temp_avg,
        MAX(out_temp_max) AS out_temp_max,
        MIN(out_temp_min) AS out_temp_min,
        SUM(out_temp_sum) AS out_temp_total,

        ROUND(AVG(out_codcr_avg), 4) AS out_codcr_avg,
        MAX(out_codcr_max) AS out_codcr_max,
        MIN(out_codcr_min) AS out_codcr_min,
        SUM(out_codcr_sum) AS out_codcr_total,

        ROUND(AVG(out_bod5_avg), 4) AS out_bod5_avg,
        MAX(out_bod5_max) AS out_bod5_max,
        MIN(out_bod5_min) AS out_bod5_min,
        SUM(out_bod5_sum) AS out_bod5_total,

        ROUND(AVG(out_ss_avg), 4) AS out_ss_avg,
        MAX(out_ss_max) AS out_ss_max,
        MIN(out_ss_min) AS out_ss_min,
        SUM(out_ss_sum) AS out_ss_total,

        ROUND(AVG(out_nh3n_avg), 4) AS out_nh3n_avg,
        MAX(out_nh3n_max) AS out_nh3n_max,
        MIN(out_nh3n_min) AS out_nh3n_min,
        SUM(out_nh3n_sum) AS out_nh3n_total,

        ROUND(AVG(out_tn_avg), 4) AS out_tn_avg,
        MAX(out_tn_max) AS out_tn_max,
        MIN(out_tn_min) AS out_tn_min,
        SUM(out_tn_sum) AS out_tn_total,

        ROUND(AVG(out_tp_avg), 4) AS out_tp_avg,
        MAX(out_tp_max) AS out_tp_max,
        MIN(out_tp_min) AS out_tp_min,
        SUM(out_tp_sum) AS out_tp_total,

        -- 流量统计（进水 + 出水）
        ROUND(AVG(in_flow_water_volume_avg), 4) AS in_flow_water_volume_avg,
        MAX(in_flow_water_volume_max) AS in_flow_water_volume_max,
        MIN(in_flow_water_volume_min) AS in_flow_water_volume_min,
        SUM(in_flow_water_volume_sum) AS in_flow_water_volume_total,

        ROUND(AVG(daily_treatment_vol_avg), 4) AS daily_treatment_vol_avg,
        MAX(daily_treatment_vol_max) AS daily_treatment_vol_max,
        MIN(daily_treatment_vol_min) AS daily_treatment_vol_min,
        SUM(daily_treatment_vol_sum) AS daily_treatment_vol_total

        FROM water_prod_quality_stat
        WHERE factory_id = #{factoryId}
        AND stat_date BETWEEN #{startDate} AND #{endDate}

    </select>

</mapper>
