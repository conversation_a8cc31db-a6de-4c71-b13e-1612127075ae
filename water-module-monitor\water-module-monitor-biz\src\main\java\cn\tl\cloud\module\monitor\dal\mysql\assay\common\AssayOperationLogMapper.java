package cn.tl.cloud.module.monitor.dal.mysql.assay.common;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tl.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.common.AssayOperationLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 操作日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssayOperationLogMapper extends BaseMapperX<AssayOperationLogDO> {

    default List<AssayOperationLogDO> selectByRecordId(Long factoryId, String tableId, Long recordId) {
        return selectList(new LambdaQueryWrapperX<AssayOperationLogDO>()
                .eq(AssayOperationLogDO::getFactoryId, factoryId)
                .eq(AssayOperationLogDO::getTableId, tableId)
                .eq(AssayOperationLogDO::getRecordId, recordId)
                .orderByDesc(AssayOperationLogDO::getOperationTime));
    }

    default List<AssayOperationLogDO> selectByOperationType(Long factoryId, String operationType) {
        return selectList(new LambdaQueryWrapperX<AssayOperationLogDO>()
                .eq(AssayOperationLogDO::getFactoryId, factoryId)
                .eq(AssayOperationLogDO::getOperationType, operationType)
                .orderByDesc(AssayOperationLogDO::getOperationTime));
    }

    default List<AssayOperationLogDO> selectByOperator(Long factoryId, Long operatorId) {
        return selectList(new LambdaQueryWrapperX<AssayOperationLogDO>()
                .eq(AssayOperationLogDO::getFactoryId, factoryId)
                .eq(AssayOperationLogDO::getOperatorId, operatorId)
                .orderByDesc(AssayOperationLogDO::getOperationTime));
    }

    default List<AssayOperationLogDO> selectByTimeRange(Long factoryId, LocalDateTime startTime, LocalDateTime endTime) {
        return selectList(new LambdaQueryWrapperX<AssayOperationLogDO>()
                .eq(AssayOperationLogDO::getFactoryId, factoryId)
                .between(AssayOperationLogDO::getOperationTime, startTime, endTime)
                .orderByDesc(AssayOperationLogDO::getOperationTime));
    }

    default List<AssayOperationLogDO> selectStatusChangeLogs(Long factoryId, String tableId, Long recordId) {
        return selectList(new LambdaQueryWrapperX<AssayOperationLogDO>()
                .eq(AssayOperationLogDO::getFactoryId, factoryId)
                .eq(AssayOperationLogDO::getTableId, tableId)
                .eq(AssayOperationLogDO::getRecordId, recordId)
                .eq(AssayOperationLogDO::getOperationType, "status_change")
                .orderByDesc(AssayOperationLogDO::getOperationTime));
    }

    default Long selectCountByOperationType(Long factoryId, String operationType) {
        return selectCount(new LambdaQueryWrapperX<AssayOperationLogDO>()
                .eq(AssayOperationLogDO::getFactoryId, factoryId)
                .eq(AssayOperationLogDO::getOperationType, operationType));
    }

    /**
     * 批量插入操作日志
     */
    void insertBatch(@Param("list") List<AssayOperationLogDO> logs);

    /**
     * 查询操作日志详情（包含操作人信息）
     */
    List<Map<String, Object>> selectLogWithOperatorInfo(@Param("factoryId") Long factoryId,
                                                        @Param("tableId") String tableId,
                                                        @Param("recordId") Long recordId);

    /**
     * 查询状态变更历史
     */
    List<Map<String, Object>> selectStatusChangeHistory(@Param("factoryId") Long factoryId,
                                                        @Param("tableId") String tableId,
                                                        @Param("recordId") Long recordId);

    /**
     * 统计操作类型分布
     */
    List<Map<String, Object>> selectOperationStatistics(@Param("factoryId") Long factoryId);

    /**
     * 查询最近的操作日志
     */
    List<AssayOperationLogDO> selectRecentLogs(@Param("factoryId") Long factoryId, @Param("limit") Integer limit);

}
