package cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo;

import cn.tl.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDate;

@Schema(description = "管理后台 - 送检记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AssaySubmissionRecordPageReqVO extends PageParam {

    @Schema(description = "水厂ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long factoryId;

    @Schema(description = "记录编号", example = "SUB-001")
    private String recordCode;

    @Schema(description = "执行ID", example = "1")
    private Long executionId;

    @Schema(description = "送检状态", example = "unSubmitted")
    private String status;

    @Schema(description = "送检人员ID", example = "1001")
    private Long submissionPersonId;

    @Schema(description = "开始日期", example = "2024-01-01")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-01-31")
    private LocalDate endDate;

}
