package cn.tl.cloud.module.monitor.service.acqconf;

import cn.hutool.core.util.StrUtil;
import cn.tl.cloud.module.monitor.common.contants.IotConstants;
import cn.tl.cloud.module.monitor.controller.admin.acqconf.vo.IotDevicePageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.acqconf.vo.IotPointPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.acqconf.vo.IotStatisticsRespVO;
import cn.tl.cloud.module.monitor.controller.admin.monitor.databoard.dto.PointValueRedisDTO;
import cn.tl.cloud.module.monitor.dal.dataobject.acqconf.IotDevice;
import cn.tl.cloud.module.monitor.dal.dataobject.acqconf.IotGroup;
import cn.tl.cloud.module.monitor.dal.dataobject.acqconf.IotPoint;
import cn.tl.cloud.module.monitor.dal.mysql.acqconf.IotDeviceMapper;
import cn.tl.cloud.module.monitor.dal.mysql.acqconf.IotGroupMapper;
import cn.tl.cloud.module.monitor.dal.mysql.acqconf.IotPointMapper;
import cn.tl.cloud.module.monitor.dal.redis.PointValueRedisDAO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 *
 */
@Service
public class IotServiceImpl implements IotService {


    @Resource
    private IotGroupMapper iotGroupMapper;

    @Resource
    private IotDeviceMapper iotDeviceMapper;

    @Resource
    private IotPointMapper iotPointMapper;

    @Resource
    private PointValueRedisDAO pointValueRedisDAO;


    @Override
    public IotStatisticsRespVO statistics() {
        // 统计已接入厂站的个数
        Long groupCount = iotGroupMapper.count();
        // 统计已接入设备的个数
        Long deviceCount = iotDeviceMapper.count();
        // 统计已接入指标的个数
        Long pointCount = iotPointMapper.count();
        IotStatisticsRespVO iotStatisticsRespVO = new IotStatisticsRespVO();
        iotStatisticsRespVO.setFactoryCount(groupCount);
        iotStatisticsRespVO.setDeviceCount(deviceCount);
        iotStatisticsRespVO.setPointCount(pointCount);
        return iotStatisticsRespVO;
    }


    @Override
    public Page devicePage(IotDevicePageReqVO reqVO) {
        Page<IotDevice> iotDevicePage = new Page<>(reqVO.getCurrentPage(), reqVO.getPageSize());
        QueryWrapper<IotDevice> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(reqVO.getFactoryId() != null, "dd.group_id", reqVO.getFactoryId());
        queryWrapper.like(StrUtil.isNotBlank(reqVO.getDeviceCode()), "dd.device_code", reqVO.getDeviceCode());
        queryWrapper.like(StrUtil.isNotBlank(reqVO.getDeviceName()), "dd.device_name", reqVO.getDeviceName());
        queryWrapper.eq("dd.deleted", 0).eq("dg.deleted", 0).eq("ddr.deleted", 0).eq("dg.enable_flag", 1);
        Page page = iotDeviceMapper.queryPage(iotDevicePage, queryWrapper);
        return page;
    }


    /**
     * 查询分组/厂站的列表
     *
     * @return
     */
    @Override
    public List<IotGroup> listGroup() {
        return iotGroupMapper.queryList();
    }


    /**
     * 分页查询点位列表
     *
     * @param reqVO
     * @return
     */
    @Override
    public Page<IotPoint> pointPage(IotPointPageReqVO reqVO) {
        Page<IotPoint> page = new Page<>(reqVO.getCurrentPage(), reqVO.getPageSize());
        QueryWrapper<IotPoint> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(reqVO.getDeviceId() != null, "device_id", reqVO.getDeviceId());
        queryWrapper.like(StrUtil.isNotBlank(reqVO.getPointCode()), "point_code", reqVO.getPointCode());
        queryWrapper.like(StrUtil.isNotBlank(reqVO.getPointName()), "point_name", reqVO.getPointName());
        queryWrapper.eq("deleted", 0);
        Page<IotPoint> pageResult = iotPointMapper.queryPage(page, queryWrapper);
        // 查询点位最新采集值
        List<IotPoint> records = pageResult.getRecords();
        if (records.size() > 0) {
            for (IotPoint record : records) {
                PointValueRedisDTO pointValue = pointValueRedisDAO.getPointValue(IotConstants.POINT_VALUE_PREFIX + reqVO.getDeviceId() + "." + record.getId());
                if (Objects.nonNull(pointValue)) {
                    record.setLatestValue(pointValue.getValue());
                    record.setLatestTime(pointValue.getOriginTime());
                }
            }

        }
        return pageResult;
    }
}
