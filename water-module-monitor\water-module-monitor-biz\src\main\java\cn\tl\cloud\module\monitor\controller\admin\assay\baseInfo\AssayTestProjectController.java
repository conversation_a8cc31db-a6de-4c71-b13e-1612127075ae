package cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo;

import cn.tl.cloud.framework.common.pojo.CommonResult;
import cn.tl.cloud.framework.common.pojo.PageParam;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.framework.excel.core.util.ExcelUtils;

import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestProjectPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestProjectRespVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestProjectSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestProjectDO;
import cn.tl.cloud.module.monitor.service.assay.baseInfo.AssayTestProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.tl.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 检测项目")
@RestController
@RequestMapping("/monitor/assay-test-project")
@Validated
public class AssayTestProjectController {

    @Resource
    private AssayTestProjectService testProjectService;

    @PostMapping("/create")
    @Operation(summary = "创建检测项目")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-project:create')")
    public CommonResult<Long> createTestProject(@Valid @RequestBody AssayTestProjectSaveReqVO createReqVO) {
        return success(testProjectService.createTestProject(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新检测项目")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-project:update')")
    public CommonResult<Boolean> updateTestProject(@Valid @RequestBody AssayTestProjectSaveReqVO updateReqVO) {
        testProjectService.updateTestProject(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除检测项目")
    @Parameter(name = "id", description = "编号", required = true)
    @Parameter(name = "factoryId", description = "水厂ID", required = true)
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-project:delete')")
    public CommonResult<Boolean> deleteTestProject(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        testProjectService.deleteTestProject(id, factoryId);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得检测项目")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-project:query')")
    public CommonResult<AssayTestProjectRespVO> getTestProject(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        AssayTestProjectDO testProject = testProjectService.getTestProject(id, factoryId);
        return success(BeanUtils.toBean(testProject, AssayTestProjectRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得检测项目分页")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-project:query')")
    public CommonResult<PageResult<AssayTestProjectRespVO>> getTestProjectPage(@Valid AssayTestProjectPageReqVO pageReqVO) {
        PageResult<AssayTestProjectDO> pageResult = testProjectService.getTestProjectPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssayTestProjectRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得检测项目列表")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-project:query')")
    public CommonResult<List<AssayTestProjectRespVO>> getTestProjectList(@Valid AssayTestProjectPageReqVO reqVO) {
        List<AssayTestProjectDO> list = testProjectService.getTestProjectList(reqVO);
        return success(BeanUtils.toBean(list, AssayTestProjectRespVO.class));
    }

    @GetMapping("/list-by-category")
    @Operation(summary = "根据类型获取检测项目列表")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    @Parameter(name = "categoryId", description = "类型ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-project:query')")
    public CommonResult<List<AssayTestProjectRespVO>> getTestProjectListByCategoryId(@RequestParam("factoryId") Long factoryId, 
                                                                                     @RequestParam("categoryId") Long categoryId) {
        List<AssayTestProjectDO> list = testProjectService.getTestProjectListByCategoryId(factoryId, categoryId);
        return success(BeanUtils.toBean(list, AssayTestProjectRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出检测项目 Excel")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-project:export')")

    public void exportTestProjectExcel(@Valid AssayTestProjectPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AssayTestProjectDO> list = testProjectService.getTestProjectList(pageReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "检测项目.xls", "数据", AssayTestProjectRespVO.class,
                        BeanUtils.toBean(list, AssayTestProjectRespVO.class));
    }

}
