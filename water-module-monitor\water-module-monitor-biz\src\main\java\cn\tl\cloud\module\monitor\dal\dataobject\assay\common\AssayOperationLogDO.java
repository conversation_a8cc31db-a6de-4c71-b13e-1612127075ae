package cn.tl.cloud.module.monitor.dal.dataobject.assay.common;

import cn.tl.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 操作日志 DO
 *
 * <AUTHOR>
 */
@TableName("assay_operation_log")
@KeySequence("assay_operation_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssayOperationLogDO extends BaseDO {

    /**
     * 日志ID
     */
    @TableId
    private Long id;
    
    /**
     * 水厂ID
     */
    private Long factoryId;
    
    /**
     * 表id关联assay_table_id表
     */
    private String tableId;
    
    /**
     * 表中记录ID
     */
    private Long recordId;
    
    /**
     * 操作类型(create-创建,update-更新,delete-删除,status_change-状态变更)
     */
    private String operationType;
    
    /**
     * 操作描述
     */
    private String operationDesc;
    
    /**
     * 操作人ID
     */
    private Long operatorId;
    
    /**
     * 操作时间
     */
    private LocalDateTime operationTime;
    
    /**
     * 操作前数据(JSON格式)
     */
    private String beforeData;
    
    /**
     * 操作后数据(JSON格式)
     */
    private String afterData;
    
    /**
     * 扩展字段，存储额外的业务数据
     */
    private String extraField;

}
