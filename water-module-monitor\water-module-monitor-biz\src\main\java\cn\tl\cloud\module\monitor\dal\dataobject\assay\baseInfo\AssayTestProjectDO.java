package cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo;

import cn.tl.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 检测项目 DO
 *
 * <AUTHOR>
 */
@TableName("assay_test_project")
@KeySequence("assay_test_project_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssayTestProjectDO extends BaseDO {

    /**
     * 项目ID
     */
    @TableId
    private Long id;
    
    /**
     * 水厂ID
     */
    private Long factoryId;
    
    /**
     * 项目代码
     */
    private String code;
    
    /**
     * 项目名称
     */
    private String name;
    
    /**
     * 所属类型ID
     */
    private Long categoryId;
    
    /**
     * 项目描述
     */
    private String description;
    
    /**
     * 是否启用
     */
    private Boolean isEnabled;

}
