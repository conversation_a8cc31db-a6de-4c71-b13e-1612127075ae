package cn.tl.cloud.module.monitor.service.assay.common;

import cn.tl.cloud.module.monitor.dal.dataobject.assay.common.AssayOperationLogDO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 操作日志 Service 接口
 *
 * <AUTHOR>
 */
public interface AssayOperationLogService {

    /**
     * 记录操作日志
     *
     * @param factoryId 水厂ID
     * @param tableId 表ID
     * @param recordId 记录ID
     * @param operationType 操作类型
     * @param operationDesc 操作描述
     * @param operatorId 操作人ID
     * @param beforeData 操作前数据
     * @param afterData 操作后数据
     */
    void recordLog(Long factoryId, String tableId, Long recordId, String operationType, 
                   String operationDesc, Long operatorId, String beforeData, String afterData);

    /**
     * 批量记录操作日志
     *
     * @param logs 日志列表
     */
    void batchRecordLogs(List<AssayOperationLogDO> logs);

    /**
     * 记录状态变更日志
     *
     * @param factoryId 水厂ID
     * @param tableId 表ID
     * @param recordId 记录ID
     * @param oldStatus 旧状态
     * @param newStatus 新状态
     * @param operatorId 操作人ID
     * @param remark 备注
     */
    void recordStatusChange(Long factoryId, String tableId, Long recordId, 
                           String oldStatus, String newStatus, Long operatorId, String remark);

    /**
     * 获取记录的操作日志
     *
     * @param factoryId 水厂ID
     * @param tableId 表ID
     * @param recordId 记录ID
     * @return 操作日志列表
     */
    List<AssayOperationLogDO> getRecordLogs(Long factoryId, String tableId, Long recordId);

    /**
     * 获取状态变更历史
     *
     * @param factoryId 水厂ID
     * @param tableId 表ID
     * @param recordId 记录ID
     * @return 状态变更历史
     */
    List<Map<String, Object>> getStatusChangeHistory(Long factoryId, String tableId, Long recordId);

    /**
     * 获取操作统计
     *
     * @param factoryId 水厂ID
     * @return 操作统计
     */
    Map<String, Object> getOperationStatistics(Long factoryId);

    /**
     * 获取最近的操作日志
     *
     * @param factoryId 水厂ID
     * @param limit 限制数量
     * @return 最近的操作日志
     */
    List<AssayOperationLogDO> getRecentLogs(Long factoryId, Integer limit);

}
