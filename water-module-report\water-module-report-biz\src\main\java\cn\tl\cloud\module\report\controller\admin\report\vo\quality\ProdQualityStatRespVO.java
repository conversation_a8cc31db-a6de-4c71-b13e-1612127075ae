package cn.tl.cloud.module.report.controller.admin.report.vo.quality;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "水质指标统计结果")
public class ProdQualityStatRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24991")
    private Long id;

    @Schema(description = "水厂ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21354")
    private Long factoryId;

    @Schema(description = "进水 pH 统计")
    private StatValue inPh;

    @Schema(description = "进水温度（℃）统计")
    private StatValue inTemp;

    @Schema(description = "进水 CODcr（mg/L）统计")
    private StatValue inCodcr;

    @Schema(description = "进水 BOD5（mg/L）统计")
    private StatValue inBod5;

    @Schema(description = "进水 SS（mg/L）统计")
    private StatValue inSs;

    @Schema(description = "进水 NH₃-N（mg/L）统计")
    private StatValue inNh3n;

    @Schema(description = "进水 TN（mg/L）统计")
    private StatValue inTn;

    @Schema(description = "进水 TP（mg/L）统计")
    private StatValue inTp;

    @Schema(description = "出水 pH 统计")
    private StatValue outPh;

    @Schema(description = "出水温度（℃）统计")
    private StatValue outTemp;

    @Schema(description = "出水 CODcr（mg/L）统计")
    private StatValue outCodcr;

    @Schema(description = "出水 BOD5（mg/L）统计")
    private StatValue outBod5;

    @Schema(description = "出水 SS（mg/L）统计")
    private StatValue outSs;

    @Schema(description = "出水 NH₃-N（mg/L）统计")
    private StatValue outNh3n;

    @Schema(description = "出水 TN（mg/L）统计")
    private StatValue outTn;

    @Schema(description = "出水 TP（mg/L）统计")
    private StatValue outTp;

    @Schema(description = "进水流量（Km³）统计")
    private StatValue inFlowWaterVolume;

    @Schema(description = "出水流量（Km³）统计")
    private StatValue dailyTreatmentVol;

    @Data
    @Schema(description = "指标统计值")
    public static class StatValue {

        @Schema(description = "平均值")
        private BigDecimal avg;

        @Schema(description = "最大值")
        private BigDecimal max;

        @Schema(description = "最小值")
        private BigDecimal min;

        @Schema(description = "累计值（流量类适用）")
        private BigDecimal total;
    }
}