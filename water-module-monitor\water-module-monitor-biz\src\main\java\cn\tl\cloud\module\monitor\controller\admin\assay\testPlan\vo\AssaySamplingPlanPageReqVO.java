package cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo;

import cn.tl.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 采样计划分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AssaySamplingPlanPageReqVO extends PageParam {

    @Schema(description = "水厂ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long factoryId;

    @Schema(description = "计划名称", example = "进水水质日常监测计划")
    private String name;

    @Schema(description = "计划类型", example = "regular")
    private String type;

    @Schema(description = "采样频率", example = "daily")
    private String frequency;

    @Schema(description = "优先级", example = "normal")
    private String priority;

    @Schema(description = "启用状态", example = "true")
    private Boolean isEnabled;

    @Schema(description = "采样人员ID", example = "1001")
    private Long samplerId;

}
