package cn.tl.cloud.module.monitor.mq.conusmer;

import cn.hutool.core.lang.UUID;
import cn.tl.cloud.module.monitor.common.utils.RedisCacheHelper;
import cn.tl.cloud.module.monitor.enums.PointTypeFlagEnum;
import cn.tl.cloud.module.monitor.enums.RabbitConstant;
import cn.tl.cloud.module.monitor.mq.DTO.AlarmPushDTO;
import cn.tl.cloud.module.monitor.sms.SmsSenderService;
import cn.tl.cloud.module.system.api.notify.NotifyMessageSendApi;
import cn.tl.cloud.module.system.api.notify.dto.NotifySendSingleToUserReqDTO;
import cn.tl.cloud.module.system.api.permission.RoleApi;
import cn.tl.cloud.module.system.api.permission.dto.UserByRoleDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.tl.cloud.module.monitor.common.contants.RedisConstants.REDIS_ROLE_USER_PREFIX;

/**
 * <AUTHOR>
 * @projectName water
 * @description
 * @date 2025/6/17 11:15
 */
@Component
@Slf4j
public class AlarmNotifyConsumer {
    @Resource
    private NotifyMessageSendApi notifyMessageSendApi;

    @Resource
    private SmsSenderService smsSenderService;

    @Resource
    private RoleApi roleApi;

    @Resource
    private RedisCacheHelper redisCacheHelper;

    @RabbitListener(queues = RabbitConstant.ALARM_QUEUE, containerFactory = "alarmRabbitListenerContainerFactory")
    public void receive(AlarmPushDTO dto) {
        //获取要发生对象的信息
        //TODO:加入redis缓存
        List<UserByRoleDTO> userDTOs = new ArrayList<>();
        dto.getReceiverRoleIds().forEach(
                roleId -> {
                    userDTOs.addAll(redisCacheHelper.get(
                            REDIS_ROLE_USER_PREFIX + roleId,
                            new TypeReference<List<UserByRoleDTO>>() {
                            },
                            () -> roleApi.getUsersByRuleIds(dto.getReceiverRoleIds()).getData(),
                            30,
                            TimeUnit.MINUTES,
                            5,
                            TimeUnit.MINUTES));

                }
        );



        //List<UserByRoleDTO> userDTOs = roleApi.getUsersByRuleIds(dto.getReceiverRoleIds()).getData();
        if (dto.getSendMethods().contains("sms")) {
            //TODO:发送短信
            try {

                List<String> phoneNumbers = userDTOs.stream()
                        .map(UserByRoleDTO::getMobile)
                        .filter(StringUtils::hasText)
                        .collect(Collectors.toList());


                double factorValue = dto.getEvent().getFactorValue();
                double threshold = dto.getEvent().getThreshold();
                String boolFactor = "";
                String boolThreshold = "";
                boolean isBool = PointTypeFlagEnum.BOOLEAN.getCode().equals(dto.getEvent().getFactorType());
                if (isBool) {
                    boolFactor = toBooleanString(factorValue);
                    boolThreshold = toBooleanString(threshold);
                }


                // 简化短信内容（只保留简明信息）
                String smsContent = String.format("%s%s的%s的%s指标触发%s告警,检测值%s,阈值%s,请及时处理。",
                        dto.getEvent().getAlarmTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                        dto.getEvent().getFactoryName(),
                        dto.getEvent().getDeviceName(),
                        dto.getEvent().getFactorName(),
                        dto.getEvent().getLevel()+"级",
                        isBool ? boolFactor : factorValue,
                        isBool? boolThreshold : threshold);

                String msgId = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 20);

                String result = smsSenderService.sendSameSms(phoneNumbers, smsContent, msgId);


                //TODO:将企信通集成成现有系统,配置新角色测试
                //每发送一条信息都要插入一条记录


                log.info("短信发送结果：{}", result);
            } catch (Exception e) {
                log.error("短信发送失败，dto={}, 异常={}", dto, e.toString());
            }

        }



        String templateCode = "alarmNotify";
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("factoryName", dto.getEvent().getFactoryName());
        templateParams.put("deviceName", dto.getEvent().getDeviceName());
        templateParams.put("pointName", dto.getEvent().getFactorName());
        templateParams.put("time", dto.getEvent().getAlarmTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        templateParams.put("level", dto.getEvent().getLevel() + "级");

        //获取要发生站内信的对象

        userDTOs.forEach(userDTO -> {
            //发送给角色
            notifyMessageSendApi.sendSingleMessageToAdmin(new NotifySendSingleToUserReqDTO().setUserId(userDTO.getUserId()).setTemplateCode(templateCode).setTemplateParams(templateParams));
        });

    }

    // 辅助方法：将整数转换为布尔字符串表示
    private static String toBooleanString(double value) {
        return value == 1 ? "true" : "false";
    }

}
