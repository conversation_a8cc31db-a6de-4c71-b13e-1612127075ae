package cn.tl.cloud.module.report.service.report.prodqualitydata;

import cn.tl.cloud.module.report.controller.admin.report.vo.quality.WaterQualityStatVO;
import cn.tl.cloud.module.report.dal.dataobject.factory.FactoryDO;
import cn.tl.cloud.module.report.dal.mysql.prodqualitydata.WaterQualityMapper;
import cn.tl.cloud.module.report.service.factory.FactoryService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class WaterQualityDataServiceImpl extends ServiceImpl<WaterQualityMapper, WaterQualityStatVO> implements WaterQualityDataService {

    @Autowired
    private WaterQualityMapper waterQualityMapper;

    @Autowired
    private FactoryService factoryService;

    @Override
    public List<WaterQualityStatVO> getMonthlyStat(LocalDate startDate, LocalDate endDate) {

        //如果是24年且整月的查询，就查聚合表
        if(startDate.getYear() == 2024 && endDate.getYear() == 2024 && isFullMonth(startDate, endDate)){
            List<WaterQualityStatVO> waterQualityStatVOS = waterQualityMapper.selectTargetStats(startDate, endDate);
            if (!waterQualityStatVOS.isEmpty()) {
                return waterQualityStatVOS;
            }
        }
        //否则就执行下面的代码进行查询
        List<WaterQualityStatVO> statList = waterQualityMapper.selectTargetStats(startDate, endDate);
        if (statList.isEmpty()) return statList;

        // 计算集团汇总值
        FactoryDO topFactory = factoryService.getOne(new LambdaQueryWrapper<FactoryDO>().eq(FactoryDO::getLevel, 1));
        WaterQualityStatVO groupSummary = calculateSummary(statList, topFactory);

        List<WaterQualityStatVO> result = new ArrayList<>();
        result.add(groupSummary);
        result.addAll(statList);

        // 计算区域汇总值
        List<FactoryDO> regionFactories = factoryService.list(new LambdaQueryWrapper<FactoryDO>().eq(FactoryDO::getLevel, 2));

        regionFactories.forEach((region) -> {
            List<WaterQualityStatVO> collect = statList.stream().filter(vo -> vo.getParentId().equals(region.getId())).collect(Collectors.toList());
            WaterQualityStatVO waterQualityStatVO = calculateSummary(collect, region);
            result.add(waterQualityStatVO);
        });
        // 添加到列表中（一般集团统计显示在首位）

        // 根据orderNum排序
        result.sort(Comparator.comparing(WaterQualityStatVO::getOrderNum));
        return result;
    }

    private WaterQualityStatVO calculateSummary(List<WaterQualityStatVO> statList, FactoryDO factoryDO) {
        WaterQualityStatVO summary = new WaterQualityStatVO();
        summary.setFactoryName(factoryDO.getName());
        summary.setFactoryId(factoryDO.getId());
        summary.setOrderNum(factoryDO.getOrderNum());

        int count = statList.size();
        statList.forEach(vo -> {
            summary.setInPhAvg(add(summary.getInPhAvg(), vo.getInPhAvg()));
            summary.setInTempAvg(add(summary.getInTempAvg(), vo.getInTempAvg()));
            summary.setInCodcrAvg(add(summary.getInCodcrAvg(), vo.getInCodcrAvg()));
            summary.setInBod5Avg(add(summary.getInBod5Avg(), vo.getInBod5Avg()));
            summary.setInSsAvg(add(summary.getInSsAvg(), vo.getInSsAvg()));
            summary.setInNh3nAvg(add(summary.getInNh3nAvg(), vo.getInNh3nAvg()));
            summary.setInTnAvg(add(summary.getInTnAvg(), vo.getInTnAvg()));
            summary.setInTpAvg(add(summary.getInTpAvg(), vo.getInTpAvg()));
            summary.setOutPhAvg(add(summary.getOutPhAvg(), vo.getOutPhAvg()));
            summary.setOutTempAvg(add(summary.getOutTempAvg(), vo.getOutTempAvg()));
            summary.setOutCodcrAvg(add(summary.getOutCodcrAvg(), vo.getOutCodcrAvg()));
            summary.setOutBod5Avg(add(summary.getOutBod5Avg(), vo.getOutBod5Avg()));
            summary.setOutSsAvg(add(summary.getOutSsAvg(), vo.getOutSsAvg()));
            summary.setOutNh3nAvg(add(summary.getOutNh3nAvg(), vo.getOutNh3nAvg()));
            summary.setOutTnAvg(add(summary.getOutTnAvg(), vo.getOutTnAvg()));
            summary.setOutTpAvg(add(summary.getOutTpAvg(), vo.getOutTpAvg()));
            summary.setInFlowWaterVolumeAvg(add(summary.getInFlowWaterVolumeAvg(), vo.getInFlowWaterVolumeAvg()));
            summary.setDailyTreatmentVolAvg(add(summary.getDailyTreatmentVolAvg(), vo.getDailyTreatmentVolAvg()));

            summary.setInPhMax(max(summary.getInPhMax(), vo.getInPhMax()));
            summary.setInTempMax(max(summary.getInTempMax(), vo.getInTempMax()));
            summary.setInCodcrMax(max(summary.getInCodcrMax(), vo.getInCodcrMax()));
            summary.setInBod5Max(max(summary.getInBod5Max(), vo.getInBod5Max()));
            summary.setInSsMax(max(summary.getInSsMax(), vo.getInSsMax()));
            summary.setInNh3nMax(max(summary.getInNh3nMax(), vo.getInNh3nMax()));
            summary.setInTnMax(max(summary.getInTnMax(), vo.getInTnMax()));
            summary.setInTpMax(max(summary.getInTpMax(), vo.getInTpMax()));
            summary.setOutPhMax(max(summary.getOutPhMax(), vo.getOutPhMax()));
            summary.setOutTempMax(max(summary.getOutTempMax(), vo.getOutTempMax()));
            summary.setOutCodcrMax(max(summary.getOutCodcrMax(), vo.getOutCodcrMax()));
            summary.setOutBod5Max(max(summary.getOutBod5Max(), vo.getOutBod5Max()));
            summary.setOutSsMax(max(summary.getOutSsMax(), vo.getOutSsMax()));
            summary.setOutNh3nMax(max(summary.getOutNh3nMax(), vo.getOutNh3nMax()));
            summary.setOutTnMax(max(summary.getOutTnMax(), vo.getOutTnMax()));
            summary.setOutTpMax(max(summary.getOutTpMax(), vo.getOutTpMax()));
            summary.setInFlowWaterVolumeMax(max(summary.getInFlowWaterVolumeMax(), vo.getInFlowWaterVolumeMax()));
            summary.setDailyTreatmentVolMax(max(summary.getDailyTreatmentVolMax(), vo.getDailyTreatmentVolMax()));

            summary.setInPhMin(min(summary.getInPhMin(), vo.getInPhMin()));
            summary.setInTempMin(min(summary.getInTempMin(), vo.getInTempMin()));
            summary.setInCodcrMin(min(summary.getInCodcrMin(), vo.getInCodcrMin()));
            summary.setInBod5Min(min(summary.getInBod5Min(), vo.getInBod5Min()));
            summary.setInSsMin(min(summary.getInSsMin(), vo.getInSsMin()));
            summary.setInNh3nMin(min(summary.getInNh3nMin(), vo.getInNh3nMin()));
            summary.setInTnMin(min(summary.getInTnMin(), vo.getInTnMin()));
            summary.setInTpMin(min(summary.getInTpMin(), vo.getInTpMin()));
            summary.setOutPhMin(min(summary.getOutPhMin(), vo.getOutPhMin()));
            summary.setOutTempMin(min(summary.getOutTempMin(), vo.getOutTempMin()));
            summary.setOutCodcrMin(min(summary.getOutCodcrMin(), vo.getOutCodcrMin()));
            summary.setOutBod5Min(min(summary.getOutBod5Min(), vo.getOutBod5Min()));
            summary.setOutSsMin(min(summary.getOutSsMin(), vo.getOutSsMin()));
            summary.setOutNh3nMin(min(summary.getOutNh3nMin(), vo.getOutNh3nMin()));
            summary.setOutTnMin(min(summary.getOutTnMin(), vo.getOutTnMin()));
            summary.setOutTpMin(min(summary.getOutTpMin(), vo.getOutTpMin()));
            summary.setInFlowWaterVolumeMin(min(summary.getInFlowWaterVolumeMin(), vo.getInFlowWaterVolumeMin()));
            summary.setDailyTreatmentVolMin(min(summary.getDailyTreatmentVolMin(), vo.getDailyTreatmentVolMin()));

            summary.setInPhSum(add(summary.getInPhSum(), vo.getInPhSum()));
            summary.setInTempSum(add(summary.getInTempSum(), vo.getInTempSum()));
            summary.setInCodcrSum(add(summary.getInCodcrSum(), vo.getInCodcrSum()));
            summary.setInBod5Sum(add(summary.getInBod5Sum(), vo.getInBod5Sum()));
            summary.setInSsSum(add(summary.getInSsSum(), vo.getInSsSum()));
            summary.setInNh3nSum(add(summary.getInNh3nSum(), vo.getInNh3nSum()));
            summary.setInTnSum(add(summary.getInTnSum(), vo.getInTnSum()));
            summary.setInTpSum(add(summary.getInTpSum(), vo.getInTpSum()));
            summary.setOutPhSum(add(summary.getOutPhSum(), vo.getOutPhSum()));
            summary.setOutTempSum(add(summary.getOutTempSum(), vo.getOutTempSum()));
            summary.setOutCodcrSum(add(summary.getOutCodcrSum(), vo.getOutCodcrSum()));
            summary.setOutBod5Sum(add(summary.getOutBod5Sum(), vo.getOutBod5Sum()));
            summary.setOutSsSum(add(summary.getOutSsSum(), vo.getOutSsSum()));
            summary.setOutNh3nSum(add(summary.getOutNh3nSum(), vo.getOutNh3nSum()));
            summary.setOutTnSum(add(summary.getOutTnSum(), vo.getOutTnSum()));
            summary.setOutTpSum(add(summary.getOutTpSum(), vo.getOutTpSum()));
            summary.setInFlowWaterVolumeSum(add(summary.getInFlowWaterVolumeSum(), vo.getInFlowWaterVolumeSum()));
            summary.setDailyTreatmentVolSum(add(summary.getDailyTreatmentVolSum(), vo.getDailyTreatmentVolSum()));


            summary.setInPh(add(summary.getInPh(), vo.getInPh()));
            summary.setInTemp(add(summary.getInTemp(), vo.getInTemp()));
            summary.setInCodcr(add(summary.getInCodcr(), vo.getInCodcr()));
            summary.setInBod5(add(summary.getInBod5(), vo.getInBod5()));
            summary.setInSs(add(summary.getInSs(), vo.getInSs()));
            summary.setInNh3n(add(summary.getInNh3n(), vo.getInNh3n()));
            summary.setInTn(add(summary.getInTn(), vo.getInTn()));
            summary.setInTp(add(summary.getInTp(), vo.getInTp()));
            summary.setOutPh(add(summary.getOutPh(), vo.getOutPh()));
            summary.setOutTemp(add(summary.getOutTemp(), vo.getOutTemp()));
            summary.setOutCodcr(add(summary.getOutCodcr(), vo.getOutCodcr()));
            summary.setOutBod5(add(summary.getOutBod5(), vo.getOutBod5()));
            summary.setOutSs(add(summary.getOutSs(), vo.getOutSs()));
            summary.setOutNh3n(add(summary.getOutNh3n(), vo.getOutNh3n()));
            summary.setOutTn(add(summary.getOutTn(), vo.getOutTn()));
            summary.setOutTp(add(summary.getOutTp(), vo.getOutTp()));
            summary.setInFlowWaterVolume(add(summary.getInFlowWaterVolume(), vo.getInFlowWaterVolume()));
            summary.setDailyTreatmentVol(add(summary.getDailyTreatmentVol(), vo.getDailyTreatmentVol()));

        });

        if (count > 0) {
            summary.setInPhAvg(div(summary.getInPhAvg(), count));
            summary.setInTempAvg(div(summary.getInTempAvg(), count));
            summary.setInCodcrAvg(div(summary.getInCodcrAvg(), count));
            summary.setInBod5Avg(div(summary.getInBod5Avg(), count));
            summary.setInSsAvg(div(summary.getInSsAvg(), count));
            summary.setInNh3nAvg(div(summary.getInNh3nAvg(), count));
            summary.setInTnAvg(div(summary.getInTnAvg(), count));
            summary.setInTpAvg(div(summary.getInTpAvg(), count));
            summary.setOutPhAvg(div(summary.getOutPhAvg(), count));
            summary.setOutTempAvg(div(summary.getOutTempAvg(), count));
            summary.setOutCodcrAvg(div(summary.getOutCodcrAvg(), count));
            summary.setOutBod5Avg(div(summary.getOutBod5Avg(), count));
            summary.setOutSsAvg(div(summary.getOutSsAvg(), count));
            summary.setOutNh3nAvg(div(summary.getOutNh3nAvg(), count));
            summary.setOutTnAvg(div(summary.getOutTnAvg(), count));
            summary.setOutTpAvg(div(summary.getOutTpAvg(), count));
            summary.setInFlowWaterVolumeAvg(div(summary.getInFlowWaterVolumeAvg(), count));
            summary.setDailyTreatmentVolAvg(div(summary.getDailyTreatmentVolAvg(), count));
        }

        return summary;
    }

    private BigDecimal add(BigDecimal a, BigDecimal b) {
        if (a == null && b == null) return null;
        if (a == null) return b;
        if (b == null) return a;
        return a.add(b);
    }

    private BigDecimal div(BigDecimal a, int divisor) {
        if (a == null || divisor == 0) return null;
        return a.divide(BigDecimal.valueOf(divisor), 2, BigDecimal.ROUND_HALF_UP);
    }

    private BigDecimal max(BigDecimal a, BigDecimal b) {
        if (a == null && b == null) return null;
        if (a == null) return b;
        if (b == null) return a;
        return a.max(b);
    }

    private BigDecimal min(BigDecimal a, BigDecimal b) {
        if (a == null && b == null) return null;
        if (a == null) return b;
        if (b == null) return a;
        return a.min(b);
    }
    /**
     * 判断查询日期范围是否为整月
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return true表示是整月查询，false表示不是整月查询
     */
    private boolean isFullMonth(LocalDate startDate, LocalDate endDate) {
        // 检查开始日期是否为月初（1号）
        if (startDate.getDayOfMonth() != 1) {
            return false;
        }

        // 检查结束日期是否为月末
        LocalDate lastDayOfMonth = startDate.with(TemporalAdjusters.lastDayOfMonth());
        return endDate.equals(lastDayOfMonth);
    }
}