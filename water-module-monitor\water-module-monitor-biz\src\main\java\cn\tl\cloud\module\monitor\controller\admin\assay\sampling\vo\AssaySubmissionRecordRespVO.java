package cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 送检记录 Response VO")
@Data
public class AssaySubmissionRecordRespVO {

    @Schema(description = "记录ID", example = "1")
    private Long id;

    @Schema(description = "水厂ID", example = "1")
    private Long factoryId;

    @Schema(description = "记录编号", example = "SUB-001")
    private String recordCode;

    @Schema(description = "关联采样执行ID", example = "1")
    private Long executionId;

    @Schema(description = "送检日期", example = "2024-01-15")
    private LocalDate submissionDate;

    @Schema(description = "送检人员ID", example = "1001")
    private Long submissionPersonId;

    @Schema(description = "送检人员姓名", example = "张三")
    private String submissionPersonName;

    @Schema(description = "检测项目ID", example = "1")
    private Long testItem;

    @Schema(description = "检测项目名称", example = "COD")
    private String testItemName;

    @Schema(description = "送检状态", example = "unSubmitted")
    private String status;

    @Schema(description = "送检单文件URL", example = "http://example.com/file.pdf")
    private String fileUrl;

    @Schema(description = "备注信息", example = "正常送检")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
