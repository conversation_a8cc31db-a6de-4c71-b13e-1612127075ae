<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.tl.cloud.module.monitor.dal.mysql.assay.common.AssayOperationLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.tl.cloud.module.monitor.dal.dataobject.assay.common.AssayOperationLogDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="factory_id" property="factoryId" jdbcType="BIGINT"/>
        <result column="table_id" property="tableId" jdbcType="VARCHAR"/>
        <result column="record_id" property="recordId" jdbcType="BIGINT"/>
        <result column="operation_type" property="operationType" jdbcType="VARCHAR"/>
        <result column="operation_desc" property="operationDesc" jdbcType="VARCHAR"/>
        <result column="operator_id" property="operatorId" jdbcType="BIGINT"/>
        <result column="operation_time" property="operationTime" jdbcType="TIMESTAMP"/>
        <result column="before_data" property="beforeData" jdbcType="LONGVARCHAR"/>
        <result column="after_data" property="afterData" jdbcType="LONGVARCHAR"/>
        <result column="extra_field" property="extraField" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="BIT"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, factory_id, table_id, record_id, operation_type, operation_desc,
        operator_id, operation_time, before_data, after_data, extra_field,
        creator, create_time, updater, update_time, deleted
    </sql>

    <!-- 批量插入操作日志 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO assay_operation_log (
            factory_id, table_id, record_id, operation_type, operation_desc,
            operator_id, operation_time, before_data, after_data, extra_field,
            creator, create_time, updater, update_time, deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.factoryId}, #{item.tableId}, #{item.recordId}, #{item.operationType},
                #{item.operationDesc}, #{item.operatorId}, #{item.operationTime},
                #{item.beforeData}, #{item.afterData}, #{item.extraField},
                #{item.creator}, #{item.createTime}, #{item.updater}, #{item.updateTime}, #{item.deleted}
            )
        </foreach>
    </insert>

    <!-- 查询操作日志详情（包含操作人信息） -->
    <select id="selectLogWithOperatorInfo" resultType="java.util.Map">
        SELECT 
            l.*,
            u.nickname as operator_name,
            u.username as operator_username
        FROM assay_operation_log l
        LEFT JOIN system_users u ON l.operator_id = u.id
        WHERE l.factory_id = #{factoryId}
          AND l.table_id = #{tableId}
          AND l.record_id = #{recordId}
          AND l.deleted = 0
        ORDER BY l.operation_time DESC
    </select>

    <!-- 查询状态变更历史 -->
    <select id="selectStatusChangeHistory" resultType="java.util.Map">
        SELECT 
            l.operation_time,
            l.operation_desc,
            l.before_data,
            l.after_data,
            u.nickname as operator_name
        FROM assay_operation_log l
        LEFT JOIN system_users u ON l.operator_id = u.id
        WHERE l.factory_id = #{factoryId}
          AND l.table_id = #{tableId}
          AND l.record_id = #{recordId}
          AND l.operation_type = 'status_change'
          AND l.deleted = 0
        ORDER BY l.operation_time ASC
    </select>

    <!-- 统计操作类型分布 -->
    <select id="selectOperationStatistics" parameterType="java.lang.Long" resultType="java.util.Map">
        SELECT 
            operation_type,
            COUNT(*) as count,
            DATE(operation_time) as operation_date
        FROM assay_operation_log
        WHERE factory_id = #{factoryId}
          AND deleted = 0
        GROUP BY operation_type, DATE(operation_time)
        ORDER BY operation_date DESC, operation_type
    </select>

    <!-- 查询最近的操作日志 -->
    <select id="selectRecentLogs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM assay_operation_log
        WHERE factory_id = #{factoryId}
          AND deleted = 0
        ORDER BY operation_time DESC
        LIMIT #{limit}
    </select>

</mapper>
