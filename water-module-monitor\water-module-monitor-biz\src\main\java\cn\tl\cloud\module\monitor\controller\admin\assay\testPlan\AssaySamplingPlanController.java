package cn.tl.cloud.module.monitor.controller.admin.assay.testPlan;

import cn.tl.cloud.framework.common.pojo.CommonResult;
import cn.tl.cloud.framework.common.pojo.PageParam;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.framework.excel.core.util.ExcelUtils;
import cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo.AssaySamplingPlanPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo.AssaySamplingPlanRespVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo.AssaySamplingPlanSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.testPlan.AssaySamplingPlanDO;
import cn.tl.cloud.module.monitor.service.assay.testPlan.AssaySamplingPlanService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import static cn.tl.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 采样计划")
@RestController
@RequestMapping("/monitor/assay-sampling-plan")
@Validated
public class AssaySamplingPlanController {

    @Resource
    private AssaySamplingPlanService samplingPlanService;

    @PostMapping("/create")
    @Operation(summary = "创建采样计划")
    public CommonResult<Long> createSamplingPlan(@Valid @RequestBody AssaySamplingPlanSaveReqVO createReqVO) {
        return success(samplingPlanService.createSamplingPlan(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新采样计划")
    public CommonResult<Boolean> updateSamplingPlan(@Valid @RequestBody AssaySamplingPlanSaveReqVO updateReqVO) {
        samplingPlanService.updateSamplingPlan(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除采样计划")
    @Parameter(name = "id", description = "编号", required = true)
    @Parameter(name = "factoryId", description = "水厂ID", required = true)
    public CommonResult<Boolean> deleteSamplingPlan(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        samplingPlanService.deleteSamplingPlan(id, factoryId);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得采样计划")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    public CommonResult<AssaySamplingPlanRespVO> getSamplingPlan(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        AssaySamplingPlanDO samplingPlan = samplingPlanService.getSamplingPlan(id, factoryId);
        return success(BeanUtils.toBean(samplingPlan, AssaySamplingPlanRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得采样计划分页")
    public CommonResult<PageResult<AssaySamplingPlanRespVO>> getSamplingPlanPage(@Valid AssaySamplingPlanPageReqVO pageReqVO) {
        PageResult<AssaySamplingPlanDO> pageResult = samplingPlanService.getSamplingPlanPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssaySamplingPlanRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得采样计划列表")
    public CommonResult<List<AssaySamplingPlanRespVO>> getSamplingPlanList(@Valid AssaySamplingPlanPageReqVO reqVO) {
        List<AssaySamplingPlanDO> list = samplingPlanService.getSamplingPlanList(reqVO);
        return success(BeanUtils.toBean(list, AssaySamplingPlanRespVO.class));
    }

    @GetMapping("/enabled-list")
    @Operation(summary = "获得启用的采样计划列表")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    public CommonResult<List<AssaySamplingPlanRespVO>> getEnabledSamplingPlanList(@RequestParam("factoryId") Long factoryId) {
        List<AssaySamplingPlanDO> list = samplingPlanService.getEnabledSamplingPlanList(factoryId);
        return success(BeanUtils.toBean(list, AssaySamplingPlanRespVO.class));
    }

    @PostMapping("/check-conflicts")
    @Operation(summary = "检查计划冲突")
    public CommonResult<Map<String, Object>> checkPlanConflicts(@RequestBody Map<String, Object> reqMap) {
        Long factoryId = Long.valueOf(reqMap.get("factoryId").toString());
        Long samplingPoint = Long.valueOf(reqMap.get("samplingPoint").toString());
        LocalDate startDate = LocalDate.parse(reqMap.get("startDate").toString());
        LocalDate endDate = LocalDate.parse(reqMap.get("endDate").toString());
        Long samplerId = Long.valueOf(reqMap.get("samplerId").toString());
        Long testerId = Long.valueOf(reqMap.get("testerId").toString());
        Long reviewerId = Long.valueOf(reqMap.get("reviewerId").toString());
        Long excludePlanId = reqMap.get("planId") != null ? Long.valueOf(reqMap.get("planId").toString()) : null;
        
        Map<String, Object> result = samplingPlanService.checkPlanConflicts(
            factoryId, samplingPoint, startDate, endDate, samplerId, testerId, reviewerId, excludePlanId);
        return success(result);
    }

    @GetMapping("/calendar")
    @Operation(summary = "获取计划日历视图")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    @Parameter(name = "year", description = "年份", example = "2024")
    @Parameter(name = "month", description = "月份", example = "1")
    @Parameter(name = "type", description = "计划类型", example = "regular")
    public CommonResult<Map<String, Object>> getPlanCalendar(@RequestParam("factoryId") Long factoryId,
                                                             @RequestParam(value = "year", defaultValue = "2024") Integer year,
                                                             @RequestParam(value = "month", defaultValue = "1") Integer month,
                                                             @RequestParam(value = "type", required = false) String type) {
        Map<String, Object> result = samplingPlanService.getPlanCalendar(factoryId, year, month, type);
        return success(result);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出采样计划 Excel")
    public void exportSamplingPlanExcel(@Valid AssaySamplingPlanPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AssaySamplingPlanDO> list = samplingPlanService.getSamplingPlanList(pageReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "采样计划.xls", "数据", AssaySamplingPlanRespVO.class,
                        BeanUtils.toBean(list, AssaySamplingPlanRespVO.class));
    }

}
