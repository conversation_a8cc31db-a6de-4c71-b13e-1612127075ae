package cn.tl.cloud.module.monitor.service.assay.testPlan;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo.AssaySamplingPlanPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo.AssaySamplingPlanSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.testPlan.AssaySamplingPlanDO;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 采样计划 Service 接口
 *
 * <AUTHOR>
 */
public interface AssaySamplingPlanService {

    /**
     * 创建采样计划
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSamplingPlan(@Valid AssaySamplingPlanSaveReqVO createReqVO);

    /**
     * 更新采样计划
     *
     * @param updateReqVO 更新信息
     */
    void updateSamplingPlan(@Valid AssaySamplingPlanSaveReqVO updateReqVO);

    /**
     * 删除采样计划
     *
     * @param id 编号
     * @param factoryId 水厂ID
     */
    void deleteSamplingPlan(Long id, Long factoryId);

    /**
     * 获得采样计划
     *
     * @param id 编号
     * @param factoryId 水厂ID
     * @return 采样计划
     */
    AssaySamplingPlanDO getSamplingPlan(Long id, Long factoryId);

    /**
     * 获得采样计划分页
     *
     * @param pageReqVO 分页查询
     * @return 采样计划分页
     */
    PageResult<AssaySamplingPlanDO> getSamplingPlanPage(AssaySamplingPlanPageReqVO pageReqVO);

    /**
     * 获得采样计划列表
     *
     * @param reqVO 查询条件
     * @return 采样计划列表
     */
    List<AssaySamplingPlanDO> getSamplingPlanList(AssaySamplingPlanPageReqVO reqVO);

    /**
     * 获得启用的采样计划列表
     *
     * @param factoryId 水厂ID
     * @return 启用的采样计划列表
     */
    List<AssaySamplingPlanDO> getEnabledSamplingPlanList(Long factoryId);

    /**
     * 检查计划冲突
     *
     * @param factoryId 水厂ID
     * @param samplingPoint 采样点ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param samplerId 采样人员ID
     * @param testerId 检测人员ID
     * @param reviewerId 审核人员ID
     * @param excludePlanId 排除的计划ID（更新时使用）
     * @return 冲突检查结果
     */
    Map<String, Object> checkPlanConflicts(Long factoryId, Long samplingPoint, 
                                          LocalDate startDate, LocalDate endDate,
                                          Long samplerId, Long testerId, Long reviewerId,
                                          Long excludePlanId);

    /**
     * 获取计划日历视图
     *
     * @param factoryId 水厂ID
     * @param year 年份
     * @param month 月份
     * @param type 计划类型
     * @return 日历视图数据
     */
    Map<String, Object> getPlanCalendar(Long factoryId, Integer year, Integer month, String type);

}
