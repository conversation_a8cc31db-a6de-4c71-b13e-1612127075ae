package cn.tl.cloud.module.report.controller.admin.report;

import cn.tl.cloud.framework.apilog.core.annotation.ApiAccessLog;
import cn.tl.cloud.framework.common.pojo.CommonResult;
import cn.tl.cloud.framework.common.pojo.PageParam;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.framework.excel.core.util.ExcelUtils;
import cn.tl.cloud.framework.idempotent.core.annotation.Idempotent;
import cn.tl.cloud.module.report.controller.admin.report.vo.consumption.MonthDataReqVO;
import cn.tl.cloud.module.report.controller.admin.report.vo.quality.*;
import cn.tl.cloud.module.report.dal.dataobject.stat.ProdQualityDataDO;
import cn.tl.cloud.module.report.service.report.prodqualitydata.ProdQualityDataService;
import cn.tl.cloud.module.report.service.report.prodqualitydata.WaterQualityDataService;
import cn.tl.cloud.module.report.service.report.supplement.DataSupplementConfigService;
import cn.tl.cloud.module.report.utils.WaterQualityConvertor;
import cn.tl.cloud.module.system.api.permission.PermissionApi;
import com.fasterxml.jackson.databind.node.ArrayNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.tl.cloud.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.tl.cloud.framework.common.pojo.CommonResult.error;
import static cn.tl.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量")
@RestController
@RequestMapping("/report/prod-quality-data")
@Validated
public class ProdQualityDataController {

    @Resource
    private ProdQualityDataService prodQualityDataService;

    @Resource
    private WaterQualityDataService waterQualityDataService;

    @Resource
    private DataSupplementConfigService dataSupplementConfigService;

    @Resource
    private PermissionApi permissionApi;

    @PostMapping("/create")
    @Operation(summary = "创建进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量")
    public CommonResult<Long> createProdQualityData(@Valid @RequestBody ProdQualityDataSaveReqVO createReqVO) {
        return success(prodQualityDataService.createProdQualityData(createReqVO));
    }


    @Idempotent(timeout = 5, message = "请勿重复提交")
    @PostMapping("/saveOrUpdateBatch")
    @Operation(summary = "新增或更新进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量")
    public CommonResult<List<Long>> saveOrUpdateProdQualityDataBatch(@Valid @RequestBody List<ProdQualityDataSaveReqVO> createReqVOList) {
        if(CollectionUtils.isEmpty(createReqVOList)){
            return error(500, "操作失败：数据为空");
        }
        return success(prodQualityDataService.saveOrUpdateBatch(createReqVOList));
    }

    @Idempotent(timeout = 5, message = "请勿重复提交")
    @PostMapping("/saveTemporarily")
    @Operation(summary = "暂存水质填报数据")
    public CommonResult<List<Long>> saveTemporarily(@Valid @RequestBody List<ProdQualityDataSaveReqVO> createReqVOList) {
        return success(prodQualityDataService.saveTemporarily(createReqVOList));
    }

    @PutMapping("/update")
    @Operation(summary = "更新进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量")
    public CommonResult<Boolean> updateProdQualityData(@Valid @RequestBody ProdQualityDataSaveReqVO updateReqVO) {
        if(Objects.isNull(updateReqVO) ){
            return error(500, "操作失败：数据为空");
        }
        prodQualityDataService.updateProdQualityData(updateReqVO);
        return success(true);
    }


//
//    @GetMapping("/get")
//    @Operation(summary = "获得进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('report:prod-quality-data:query')")
//    public CommonResult<ProdQualityDataRespVO> getProdQualityData(@RequestParam("id") Long id) {
//        ProdQualityDataDO prodQualityData = prodQualityDataService.getProdQualityData(id);
//        return success(BeanUtils.toBean(prodQualityData, ProdQualityDataRespVO.class));
//    }

    @Idempotent(timeout = 5, message = "请勿重复提交")
    @PostMapping("/additional-recording")
    @Operation(summary = "补录")
    public CommonResult<List<Long>> additionalRecording(@Valid @RequestBody List<ProdQualityDataSaveReqVO> createReqVOList) {
        if(CollectionUtils.isEmpty(createReqVOList) ){
            return error(500, "补录失败：数据为空");
        }
        List<LocalDate> dataDateList = createReqVOList.stream().map(ProdQualityDataSaveReqVO::getDate).collect(Collectors.toList());

        // 校验是否可以进行补录
        if (dataSupplementConfigService.isSupplementAllowed("quality", dataDateList, LocalDateTime.now())) {
            return success(prodQualityDataService.additionalRecording(createReqVOList));
        }

        return error(500, "补录失败：数据日期或补录操作时间不在允许的范围内");
    }

    @GetMapping("/page")
    @Operation(summary = "获得进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量分页")
    @PreAuthorize("@ss.hasPermission('report:prod-quality-data:query')")
    public CommonResult<PageResult<ProdQualityDataRespVO>> getProdQualityDataPage(@Valid ProdQualityDataPageReqVO pageReqVO) {
        PageResult<ProdQualityDataDO> pageResult = prodQualityDataService.getProdQualityDataPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProdQualityDataRespVO.class));
    }

    @Idempotent(timeout = 5, message = "请勿重复提交")
    @GetMapping("/export-excel")
    @Operation(summary = "导出进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量 Excel")
    @PreAuthorize("@ss.hasPermission('report:prod-quality-data:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProdQualityDataExcel(@Valid ProdQualityDataPageReqVO pageReqVO,
                                           HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProdQualityDataDO> list = prodQualityDataService.getProdQualityDataPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量.xls", "数据", ProdQualityDataRespVO.class,
                BeanUtils.toBean(list, ProdQualityDataRespVO.class));
    }

    @PostMapping("/list")
    @Operation(summary = "获得进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量")
    public CommonResult<List<ProdQualityDataReportRespVO>> getProdQualityDataList(@RequestBody MonthDataReqVO monthDataReqVO) {
        List<ProdQualityDataDO> pageResult = prodQualityDataService.getProdQualityDataList(monthDataReqVO);
        return success(BeanUtils.toBean(pageResult, ProdQualityDataReportRespVO.class));
    }

    @PostMapping("/list-by-date-range")
    @Operation(summary = "根据指定日期范围获取进出水水质数据")
    public CommonResult<List<ProdQualityDataReportRespVO>> getProdQualityDataByDateRange(@RequestBody DateRangeReqVO reqVO) {
        List<ProdQualityDataDO> result = prodQualityDataService.getProdQualityDataByDateRange(reqVO);
        return success(BeanUtils.toBean(result, ProdQualityDataReportRespVO.class));
    }

    /**
     * 查询某年某月某日所有厂站的水质当日值及月统计值
     */
    @Operation(summary = "查询所有生产水质的统计/汇总数据")
    @GetMapping("/stat/all")
    public String getWaterQualityStat(@RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate, @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {

        List<WaterQualityStatVO> monthlyStat = waterQualityDataService.getMonthlyStat(startDate, endDate);

        ArrayNode jsonArray = WaterQualityConvertor.convert(monthlyStat);
        return jsonArray.toPrettyString();
    }


    @GetMapping("/quality-stat")
    @Operation(summary = "水质报表统计，查询平均值，最大值，最小值，累计值")
    @PermitAll
    public CommonResult<List<ProdQualityStatRespVO>> getWaterQualityReport(
            @RequestParam("factoryId") Long factoryId,
            @RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        return success(prodQualityDataService.getQualityReport(factoryId, startDate, endDate));
    }

}
