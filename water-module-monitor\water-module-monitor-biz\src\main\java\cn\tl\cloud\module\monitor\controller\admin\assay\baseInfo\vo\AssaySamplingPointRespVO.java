package cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 采样点 Response VO")
@Data
public class AssaySamplingPointRespVO {

    @Schema(description = "采样点ID", example = "1")
    private Long id;

    @Schema(description = "水厂ID", example = "1")
    private Long factoryId;

    @Schema(description = "采样点编码", example = "SP001")
    private String code;

    @Schema(description = "采样点名称", example = "进水口1号")
    private String name;

    @Schema(description = "采样点类型", example = "inlet")
    private String type;

    @Schema(description = "位置描述", example = "厂区东南角进水泵房")
    private String location;

    @Schema(description = "管理人ID", example = "1001")
    private Long managerId;

    @Schema(description = "启用状态", example = "true")
    private Boolean isEnabled;

    @Schema(description = "备注", example = "主要进水口")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
