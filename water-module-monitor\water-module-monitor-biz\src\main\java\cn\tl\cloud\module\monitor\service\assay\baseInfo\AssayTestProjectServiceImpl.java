package cn.tl.cloud.module.monitor.service.assay.baseInfo;

import cn.tl.cloud.framework.common.exception.util.ServiceExceptionUtil;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestProjectPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestProjectSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestCategoryDO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestProjectDO;
import cn.tl.cloud.module.monitor.dal.mysql.assay.baseInfo.AssayTestCategoryMapper;
import cn.tl.cloud.module.monitor.dal.mysql.assay.baseInfo.AssayTestIndicatorMapper;
import cn.tl.cloud.module.monitor.dal.mysql.assay.baseInfo.AssayTestProjectMapper;
import cn.tl.cloud.module.monitor.enums.ErrorCodeConstants;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 检测项目 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssayTestProjectServiceImpl implements AssayTestProjectService {

    @Resource
    private AssayTestProjectMapper testProjectMapper;
    
    @Resource
    private AssayTestCategoryMapper testCategoryMapper;
    
    @Resource
    private AssayTestIndicatorMapper testIndicatorMapper;

    @Override
    public Long createTestProject(@Valid AssayTestProjectSaveReqVO createReqVO) {
        // 校验类型存在
        validateCategoryExists(createReqVO.getFactoryId(), createReqVO.getCategoryId());
        
        // 校验代码唯一性
        validateCodeUnique(createReqVO.getFactoryId(), createReqVO.getCode(), null);
        
        // 插入
        AssayTestProjectDO testProject = BeanUtils.toBean(createReqVO, AssayTestProjectDO.class);
        testProjectMapper.insert(testProject);
        
        // 返回
        return testProject.getId();
    }

    @Override
    public void updateTestProject(@Valid AssayTestProjectSaveReqVO updateReqVO) {
        // 校验存在
        validateTestProjectExists(updateReqVO.getId(), updateReqVO.getFactoryId());
        
        // 校验类型存在
        validateCategoryExists(updateReqVO.getFactoryId(), updateReqVO.getCategoryId());
        
        // 校验代码唯一性
        validateCodeUnique(updateReqVO.getFactoryId(), updateReqVO.getCode(), updateReqVO.getId());
        
        // 更新
        AssayTestProjectDO updateObj = BeanUtils.toBean(updateReqVO, AssayTestProjectDO.class);
        testProjectMapper.updateById(updateObj);
    }

    @Override
    public void deleteTestProject(Long id, Long factoryId) {
        // 校验存在
        validateTestProjectExists(id, factoryId);
        
        // 校验是否存在关联的检测指标
        Long indicatorCount = testIndicatorMapper.selectCountByProjectId(id);
        if (indicatorCount > 0) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.TEST_PROJECT_HAS_INDICATORS);
        }
        
        // 删除
        testProjectMapper.deleteById(id);
    }

    private void validateTestProjectExists(Long id, Long factoryId) {
        AssayTestProjectDO testProject = testProjectMapper.selectById(id);
        if (testProject == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.TEST_PROJECT_NOT_EXISTS);
        }
        if (!testProject.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FACTORY_DATA_ACCESS_DENIED);
        }
    }

    private void validateCategoryExists(Long factoryId, Long categoryId) {
        AssayTestCategoryDO category = testCategoryMapper.selectById(categoryId);
        if (category == null || !category.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.TEST_PROJECT_CATEGORY_NOT_EXISTS);
        }
    }

    private void validateCodeUnique(Long factoryId, String code, Long id) {
        AssayTestProjectDO testProject = testProjectMapper.selectByFactoryIdAndCode(factoryId, code);
        if (testProject == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的记录
        if (id == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.TEST_PROJECT_CODE_DUPLICATE);
        }
        if (!testProject.getId().equals(id)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.TEST_PROJECT_CODE_DUPLICATE);
        }
    }

    @Override
    public AssayTestProjectDO getTestProject(Long id, Long factoryId) {
        AssayTestProjectDO testProject = testProjectMapper.selectById(id);
        if (testProject == null) {
            return null;
        }
        if (!testProject.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FACTORY_DATA_ACCESS_DENIED);
        }
        return testProject;
    }

    @Override
    public PageResult<AssayTestProjectDO> getTestProjectPage(AssayTestProjectPageReqVO pageReqVO) {
        return testProjectMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AssayTestProjectDO> getTestProjectList(AssayTestProjectPageReqVO reqVO) {
        return testProjectMapper.selectList(reqVO);
    }

    @Override
    public List<AssayTestProjectDO> getTestProjectListByCategoryId(Long factoryId, Long categoryId) {
        return testProjectMapper.selectListByCategoryId(factoryId, categoryId);
    }

}
