package cn.tl.cloud.module.monitor.service.assay.sampling;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySampleManagementPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySampleManagementSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySampleManagementDO;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 样品管理 Service 接口
 *
 * <AUTHOR>
 */
public interface AssaySampleManagementService {

    /**
     * 创建样品管理记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSampleManagement(@Valid AssaySampleManagementSaveReqVO createReqVO);

    /**
     * 更新样品管理记录
     *
     * @param updateReqVO 更新信息
     */
    void updateSampleManagement(@Valid AssaySampleManagementSaveReqVO updateReqVO);

    /**
     * 删除样品管理记录
     *
     * @param id 编号
     * @param factoryId 水厂ID
     */
    void deleteSampleManagement(Long id, Long factoryId);

    /**
     * 获得样品管理记录
     *
     * @param id 编号
     * @param factoryId 水厂ID
     * @return 样品管理记录
     */
    AssaySampleManagementDO getSampleManagement(Long id, Long factoryId);

    /**
     * 获得样品管理分页
     *
     * @param pageReqVO 分页查询
     * @return 样品管理分页
     */
    PageResult<AssaySampleManagementDO> getSampleManagementPage(AssaySampleManagementPageReqVO pageReqVO);

    /**
     * 获得样品管理列表
     *
     * @param reqVO 查询条件
     * @return 样品管理列表
     */
    List<AssaySampleManagementDO> getSampleManagementList(AssaySampleManagementPageReqVO reqVO);

    /**
     * 样品入库
     *
     * @param executionId 执行ID
     * @param factoryId 水厂ID
     * @param operatorId 操作人ID
     * @param sampleData 样品数据
     * @return 操作结果
     */
    Map<String, Object> storeSample(Long executionId, Long factoryId, Long operatorId, Map<String, Object> sampleData);

    /**
     * 更新样品状态
     *
     * @param id 样品ID
     * @param factoryId 水厂ID
     * @param status 新状态
     * @param operatorId 操作人ID
     * @param remark 备注
     * @return 操作结果
     */
    Map<String, Object> updateSampleStatus(Long id, Long factoryId, String status, Long operatorId, String remark);

    /**
     * 销毁样品
     *
     * @param id 样品ID
     * @param factoryId 水厂ID
     * @param operatorId 操作人ID
     * @param destroyReason 销毁原因
     * @return 操作结果
     */
    Map<String, Object> destroySample(Long id, Long factoryId, Long operatorId, String destroyReason);

    /**
     * 获取样品详情
     *
     * @param id 样品ID
     * @param factoryId 水厂ID
     * @return 样品详情
     */
    Map<String, Object> getSampleDetail(Long id, Long factoryId);

    /**
     * 获取即将过期的样品
     *
     * @param factoryId 水厂ID
     * @param days 天数
     * @return 即将过期的样品列表
     */
    List<AssaySampleManagementDO> getExpiringSamples(Long factoryId, Integer days);

    /**
     * 获取样品状态统计
     *
     * @param factoryId 水厂ID
     * @return 状态统计
     */
    Map<String, Object> getSampleStatistics(Long factoryId);

}
