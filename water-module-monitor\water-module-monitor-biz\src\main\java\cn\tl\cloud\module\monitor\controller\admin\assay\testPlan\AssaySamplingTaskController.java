package cn.tl.cloud.module.monitor.controller.admin.assay.testPlan;

import cn.tl.cloud.framework.common.pojo.CommonResult;
import cn.tl.cloud.framework.common.pojo.PageParam;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.framework.excel.core.util.ExcelUtils;
import cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo.*;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.testPlan.AssaySamplingTaskDO;
import cn.tl.cloud.module.monitor.service.assay.testPlan.AssaySamplingTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static cn.tl.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 采样任务")
@RestController
@RequestMapping("/monitor/assay-sampling-task")
@Validated
public class AssaySamplingTaskController {

    @Resource
    private AssaySamplingTaskService samplingTaskService;

    @PostMapping("/generate-from-plan")
    @Operation(summary = "根据计划生成任务")
    public CommonResult<Map<String, Object>> generateTasksFromPlan(@Valid @RequestBody AssayTaskGenerateReqVO generateReqVO) {
        Map<String, Object> result = samplingTaskService.generateTasksFromPlan(generateReqVO);
        return success(result);
    }

    @PutMapping("/assign")
    @Operation(summary = "分配任务")
    public CommonResult<Map<String, Object>> assignTask(@Valid @RequestBody AssayTaskAssignReqVO assignReqVO) {
        Map<String, Object> result = samplingTaskService.assignTask(assignReqVO);
        return success(result);
    }

    @GetMapping("/get")
    @Operation(summary = "获得采样任务")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    public CommonResult<AssaySamplingTaskRespVO> getSamplingTask(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        AssaySamplingTaskDO samplingTask = samplingTaskService.getSamplingTask(id, factoryId);
        return success(BeanUtils.toBean(samplingTask, AssaySamplingTaskRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得采样任务分页")
    public CommonResult<PageResult<AssaySamplingTaskRespVO>> getSamplingTaskPage(@Valid AssaySamplingTaskPageReqVO pageReqVO) {
        PageResult<AssaySamplingTaskDO> pageResult = samplingTaskService.getSamplingTaskPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssaySamplingTaskRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得采样任务列表")
    public CommonResult<List<AssaySamplingTaskRespVO>> getSamplingTaskList(@Valid AssaySamplingTaskPageReqVO reqVO) {
        List<AssaySamplingTaskDO> list = samplingTaskService.getSamplingTaskList(reqVO);
        return success(BeanUtils.toBean(list, AssaySamplingTaskRespVO.class));
    }

    @GetMapping("/detail")
    @Operation(summary = "获取任务详情")
    @Parameter(name = "id", description = "任务ID", required = true, example = "1")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    public CommonResult<Map<String, Object>> getTaskDetail(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        Map<String, Object> result = samplingTaskService.getTaskDetail(id, factoryId);
        return success(result);
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新任务状态")
    public CommonResult<Boolean> updateTaskStatus(@RequestParam("taskId") Long taskId,
                                                  @RequestParam("status") String status,
                                                  @RequestParam(value = "remark", required = false) String remark) {
        boolean result = samplingTaskService.updateTaskStatus(taskId, status, remark);
        return success(result);
    }

    @PutMapping("/batch-update-status")
    @Operation(summary = "批量更新任务状态")
    public CommonResult<Integer> batchUpdateTaskStatus(@RequestParam("taskIds") List<Long> taskIds,
                                                       @RequestParam("status") String status,
                                                       @RequestParam(value = "remark", required = false) String remark) {
        int count = samplingTaskService.batchUpdateTaskStatus(taskIds, status, remark);
        return success(count);
    }

    @GetMapping("/by-plan")
    @Operation(summary = "根据计划ID获取任务列表")
    @Parameter(name = "planId", description = "计划ID", required = true, example = "1")
    public CommonResult<List<AssaySamplingTaskRespVO>> getTasksByPlanId(@RequestParam("planId") Long planId) {
        List<AssaySamplingTaskDO> list = samplingTaskService.getTasksByPlanId(planId);
        return success(BeanUtils.toBean(list, AssaySamplingTaskRespVO.class));
    }

    @GetMapping("/by-status")
    @Operation(summary = "根据状态获取任务列表")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    @Parameter(name = "status", description = "状态", required = true, example = "pending")
    public CommonResult<List<AssaySamplingTaskRespVO>> getTasksByStatus(@RequestParam("factoryId") Long factoryId,
                                                                        @RequestParam("status") String status) {
        List<AssaySamplingTaskDO> list = samplingTaskService.getTasksByStatus(factoryId, status);
        return success(BeanUtils.toBean(list, AssaySamplingTaskRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出采样任务 Excel")
    public void exportSamplingTaskExcel(@Valid AssaySamplingTaskPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AssaySamplingTaskDO> list = samplingTaskService.getSamplingTaskList(pageReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "采样任务.xls", "数据", AssaySamplingTaskRespVO.class,
                        BeanUtils.toBean(list, AssaySamplingTaskRespVO.class));
    }

}
