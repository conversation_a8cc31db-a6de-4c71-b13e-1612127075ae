package cn.tl.cloud.module.monitor.service.assay.baseInfo;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestIndicatorPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestIndicatorSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestIndicatorDO;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 检测指标 Service 接口
 *
 * <AUTHOR>
 */
public interface AssayTestIndicatorService {

    /**
     * 创建检测指标
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTestIndicator(@Valid AssayTestIndicatorSaveReqVO createReqVO);

    /**
     * 更新检测指标
     *
     * @param updateReqVO 更新信息
     */
    void updateTestIndicator(@Valid AssayTestIndicatorSaveReqVO updateReqVO);

    /**
     * 删除检测指标
     *
     * @param id 编号
     * @param factoryId 水厂ID
     */
    void deleteTestIndicator(Long id, Long factoryId);

    /**
     * 获得检测指标
     *
     * @param id 编号
     * @param factoryId 水厂ID
     * @return 检测指标
     */
    AssayTestIndicatorDO getTestIndicator(Long id, Long factoryId);

    /**
     * 获得检测指标分页
     *
     * @param pageReqVO 分页查询
     * @return 检测指标分页
     */
    PageResult<AssayTestIndicatorDO> getTestIndicatorPage(AssayTestIndicatorPageReqVO pageReqVO);

    /**
     * 获得检测指标列表
     *
     * @param reqVO 查询条件
     * @return 检测指标列表
     */
    List<AssayTestIndicatorDO> getTestIndicatorList(AssayTestIndicatorPageReqVO reqVO);

    /**
     * 根据项目获取检测指标列表
     *
     * @param factoryId 水厂ID
     * @param projectId 项目ID
     * @return 检测指标列表
     */
    List<AssayTestIndicatorDO> getTestIndicatorListByProjectId(Long factoryId, Long projectId);

    /**
     * 获取检测项目树形结构
     *
     * @param pageReqVO 分页查询条件
     * @return 树形结构数据
     */
    PageResult<Map<String, Object>> getTestIndicatorTree(AssayTestIndicatorPageReqVO pageReqVO);

}
