package cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo;

import cn.tl.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDate;

@Schema(description = "管理后台 - 采样执行分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AssaySamplingExecutionPageReqVO extends PageParam {

    @Schema(description = "水厂ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long factoryId;

    @Schema(description = "任务ID", example = "1")
    private Long taskId;

    @Schema(description = "执行状态", example = "pending")
    private String status;

    @Schema(description = "样品状态", example = "normal")
    private String sampleStatus;

    @Schema(description = "开始日期", example = "2024-01-01")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-01-31")
    private LocalDate endDate;

}
