package cn.tl.cloud.module.monitor.dal.mysql.assay.baseInfo;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tl.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestProjectPageReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestProjectDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检测项目 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssayTestProjectMapper extends BaseMapperX<AssayTestProjectDO> {

    default PageResult<AssayTestProjectDO> selectPage(AssayTestProjectPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AssayTestProjectDO>()
                .eqIfPresent(AssayTestProjectDO::getFactoryId, reqVO.getFactoryId())
                .eqIfPresent(AssayTestProjectDO::getCategoryId, reqVO.getCategoryId())
                .likeIfPresent(AssayTestProjectDO::getName, reqVO.getName())
                .eqIfPresent(AssayTestProjectDO::getIsEnabled, reqVO.getIsEnabled())
                .orderByDesc(AssayTestProjectDO::getId));
    }

    default List<AssayTestProjectDO> selectList(AssayTestProjectPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AssayTestProjectDO>()
                .eqIfPresent(AssayTestProjectDO::getFactoryId, reqVO.getFactoryId())
                .eqIfPresent(AssayTestProjectDO::getCategoryId, reqVO.getCategoryId())
                .likeIfPresent(AssayTestProjectDO::getName, reqVO.getName())
                .eqIfPresent(AssayTestProjectDO::getIsEnabled, reqVO.getIsEnabled())
                .orderByDesc(AssayTestProjectDO::getId));
    }

    default List<AssayTestProjectDO> selectListByCategoryId(Long factoryId, Long categoryId) {
        return selectList(new LambdaQueryWrapperX<AssayTestProjectDO>()
                .eq(AssayTestProjectDO::getFactoryId, factoryId)
                .eq(AssayTestProjectDO::getCategoryId, categoryId)
                .eq(AssayTestProjectDO::getIsEnabled, true)
                .orderByDesc(AssayTestProjectDO::getId));
    }

    default AssayTestProjectDO selectByFactoryIdAndCode(Long factoryId, String code) {
        return selectOne(new LambdaQueryWrapperX<AssayTestProjectDO>()
                .eq(AssayTestProjectDO::getFactoryId, factoryId)
                .eq(AssayTestProjectDO::getCode, code));
    }

    default Long selectCountByCategoryId(Long categoryId) {
        return selectCount(new LambdaQueryWrapperX<AssayTestProjectDO>()
                .eq(AssayTestProjectDO::getCategoryId, categoryId));
    }

    List<AssayTestProjectDO> selectListByCategoryIds(@Param("factoryId") Long factoryId,
                                                     @Param("categoryIds") List<Long> categoryIds);


}
