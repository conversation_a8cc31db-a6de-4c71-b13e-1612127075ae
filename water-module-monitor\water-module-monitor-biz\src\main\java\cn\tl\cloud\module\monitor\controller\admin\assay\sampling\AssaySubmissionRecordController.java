package cn.tl.cloud.module.monitor.controller.admin.assay.sampling;

import cn.tl.cloud.framework.common.pojo.CommonResult;
import cn.tl.cloud.framework.common.pojo.PageParam;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.framework.excel.core.util.ExcelUtils;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySubmissionRecordPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySubmissionRecordRespVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySubmissionRecordSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySubmissionRecordDO;
import cn.tl.cloud.module.monitor.service.assay.sampling.AssaySubmissionRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static cn.tl.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 送检记录")
@RestController
@RequestMapping("/monitor/assay-submission-record")
@Validated
public class AssaySubmissionRecordController {

    @Resource
    private AssaySubmissionRecordService submissionRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建送检记录")
    public CommonResult<Long> createSubmissionRecord(@Valid @RequestBody AssaySubmissionRecordSaveReqVO createReqVO) {
        return success(submissionRecordService.createSubmissionRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新送检记录")
    public CommonResult<Boolean> updateSubmissionRecord(@Valid @RequestBody AssaySubmissionRecordSaveReqVO updateReqVO) {
        submissionRecordService.updateSubmissionRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除送检记录")
    @Parameter(name = "id", description = "编号", required = true)
    @Parameter(name = "factoryId", description = "水厂ID", required = true)
    public CommonResult<Boolean> deleteSubmissionRecord(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        submissionRecordService.deleteSubmissionRecord(id, factoryId);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得送检记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    public CommonResult<AssaySubmissionRecordRespVO> getSubmissionRecord(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        AssaySubmissionRecordDO submissionRecord = submissionRecordService.getSubmissionRecord(id, factoryId);
        return success(BeanUtils.toBean(submissionRecord, AssaySubmissionRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得送检记录分页")
    public CommonResult<PageResult<AssaySubmissionRecordRespVO>> getSubmissionRecordPage(@Valid AssaySubmissionRecordPageReqVO pageReqVO) {
        PageResult<AssaySubmissionRecordDO> pageResult = submissionRecordService.getSubmissionRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssaySubmissionRecordRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得送检记录列表")
    public CommonResult<List<AssaySubmissionRecordRespVO>> getSubmissionRecordList(@Valid AssaySubmissionRecordPageReqVO reqVO) {
        List<AssaySubmissionRecordDO> list = submissionRecordService.getSubmissionRecordList(reqVO);
        return success(BeanUtils.toBean(list, AssaySubmissionRecordRespVO.class));
    }

    @PostMapping("/complete-submission")
    @Operation(summary = "送检完成")
    public CommonResult<Map<String, Object>> completeSubmission(@RequestParam("id") Long id,
                                                               @RequestParam("factoryId") Long factoryId,
                                                               @RequestParam("operatorId") Long operatorId,
                                                               @RequestParam(value = "fileUrl", required = false) String fileUrl) {
        Map<String, Object> result = submissionRecordService.completeSubmission(id, factoryId, operatorId, fileUrl);
        return success(result);
    }

    @GetMapping("/detail")
    @Operation(summary = "获取送检记录详情")
    @Parameter(name = "id", description = "送检记录ID", required = true, example = "1")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    public CommonResult<Map<String, Object>> getSubmissionDetail(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        Map<String, Object> result = submissionRecordService.getSubmissionDetail(id, factoryId);
        return success(result);
    }

    @GetMapping("/pending-submissions")
    @Operation(summary = "获取待送检记录")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    public CommonResult<List<AssaySubmissionRecordRespVO>> getPendingSubmissions(@RequestParam("factoryId") Long factoryId) {
        List<AssaySubmissionRecordDO> list = submissionRecordService.getPendingSubmissions(factoryId);
        return success(BeanUtils.toBean(list, AssaySubmissionRecordRespVO.class));
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取送检统计")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    public CommonResult<Map<String, Object>> getSubmissionStatistics(@RequestParam("factoryId") Long factoryId) {
        Map<String, Object> result = submissionRecordService.getSubmissionStatistics(factoryId);
        return success(result);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出送检记录 Excel")
    public void exportSubmissionRecordExcel(@Valid AssaySubmissionRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AssaySubmissionRecordDO> list = submissionRecordService.getSubmissionRecordList(pageReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "送检记录.xls", "数据", AssaySubmissionRecordRespVO.class,
                        BeanUtils.toBean(list, AssaySubmissionRecordRespVO.class));
    }

}
