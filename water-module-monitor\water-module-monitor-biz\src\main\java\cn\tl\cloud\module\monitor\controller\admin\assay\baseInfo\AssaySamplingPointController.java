package cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo;

import cn.tl.cloud.framework.common.pojo.CommonResult;
import cn.tl.cloud.framework.common.pojo.PageParam;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.framework.excel.core.util.ExcelUtils;

import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssaySamplingPointPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssaySamplingPointRespVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssaySamplingPointSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssaySamplingPointDO;
import cn.tl.cloud.module.monitor.service.assay.baseInfo.AssaySamplingPointService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.tl.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 采样点")
@RestController
@RequestMapping("/monitor/assay-sampling-point")
@Validated
public class AssaySamplingPointController {

    @Resource
    private AssaySamplingPointService samplingPointService;

    @PostMapping("/create")
    @Operation(summary = "创建采样点")
    @PreAuthorize("@ss.hasPermission('monitor:assay-sampling-point:create')")
    public CommonResult<Long> createSamplingPoint(@Valid @RequestBody AssaySamplingPointSaveReqVO createReqVO) {
        return success(samplingPointService.createSamplingPoint(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新采样点")
    @PreAuthorize("@ss.hasPermission('monitor:assay-sampling-point:update')")
    public CommonResult<Boolean> updateSamplingPoint(@Valid @RequestBody AssaySamplingPointSaveReqVO updateReqVO) {
        samplingPointService.updateSamplingPoint(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除采样点")
    @Parameter(name = "id", description = "编号", required = true)
    @Parameter(name = "factoryId", description = "水厂ID", required = true)
    @PreAuthorize("@ss.hasPermission('monitor:assay-sampling-point:delete')")
    public CommonResult<Boolean> deleteSamplingPoint(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        samplingPointService.deleteSamplingPoint(id, factoryId);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得采样点")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('monitor:assay-sampling-point:query')")
    public CommonResult<AssaySamplingPointRespVO> getSamplingPoint(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        AssaySamplingPointDO samplingPoint = samplingPointService.getSamplingPoint(id, factoryId);
        return success(BeanUtils.toBean(samplingPoint, AssaySamplingPointRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得采样点分页")
    @PreAuthorize("@ss.hasPermission('monitor:assay-sampling-point:query')")
    public CommonResult<PageResult<AssaySamplingPointRespVO>> getSamplingPointPage(@Valid AssaySamplingPointPageReqVO pageReqVO) {
        PageResult<AssaySamplingPointDO> pageResult = samplingPointService.getSamplingPointPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssaySamplingPointRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得采样点列表")
    @PreAuthorize("@ss.hasPermission('monitor:assay-sampling-point:query')")
    public CommonResult<List<AssaySamplingPointRespVO>> getSamplingPointList(@Valid AssaySamplingPointPageReqVO reqVO) {
        List<AssaySamplingPointDO> list = samplingPointService.getSamplingPointList(reqVO);
        return success(BeanUtils.toBean(list, AssaySamplingPointRespVO.class));
    }

    @GetMapping("/list-by-type")
    @Operation(summary = "根据类型获取采样点列表")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    @Parameter(name = "type", description = "类型", required = true, example = "inlet")
    @PreAuthorize("@ss.hasPermission('monitor:assay-sampling-point:query')")
    public CommonResult<List<AssaySamplingPointRespVO>> getSamplingPointListByType(@RequestParam("factoryId") Long factoryId, 
                                                                                   @RequestParam("type") String type) {
        List<AssaySamplingPointDO> list = samplingPointService.getSamplingPointListByType(factoryId, type);
        return success(BeanUtils.toBean(list, AssaySamplingPointRespVO.class));
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获得采样点精简列表")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('monitor:assay-sampling-point:query')")
    public CommonResult<List<AssaySamplingPointRespVO>> getSamplingPointSimpleList(@RequestParam("factoryId") Long factoryId) {
        List<AssaySamplingPointDO> list = samplingPointService.getSamplingPointSimpleList(factoryId);
        return success(BeanUtils.toBean(list, AssaySamplingPointRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出采样点 Excel")
    @PreAuthorize("@ss.hasPermission('monitor:assay-sampling-point:export')")

    public void exportSamplingPointExcel(@Valid AssaySamplingPointPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AssaySamplingPointDO> list = samplingPointService.getSamplingPointList(pageReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "采样点.xls", "数据", AssaySamplingPointRespVO.class,
                        BeanUtils.toBean(list, AssaySamplingPointRespVO.class));
    }

}
