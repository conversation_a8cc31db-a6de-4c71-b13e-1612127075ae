package cn.tl.cloud.module.report.dal.mysql.prodconsumptiondata;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tl.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tl.cloud.module.report.controller.admin.report.vo.consumption.ProdConsumptionDataPageReqVO;
import cn.tl.cloud.module.report.controller.admin.report.vo.consumption.WaterProdConsumptionStats;
import cn.tl.cloud.module.report.dal.dataobject.stat.ProdConsumptionDataDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 生产消耗数据表，记录每日的产量、能耗、原材料消耗和污泥处理情况 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProdConsumptionDataMapper extends BaseMapperX<ProdConsumptionDataDO> {

    default PageResult<ProdConsumptionDataDO> selectPage(ProdConsumptionDataPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProdConsumptionDataDO>()
                .betweenIfPresent(ProdConsumptionDataDO::getDate, reqVO.getDate())
                .eqIfPresent(ProdConsumptionDataDO::getFactoryId, reqVO.getFactoryId())
                .eqIfPresent(ProdConsumptionDataDO::getReporterId, reqVO.getReporterId())
                .eqIfPresent(ProdConsumptionDataDO::getReviewerId, reqVO.getReviewerId())
                .eqIfPresent(ProdConsumptionDataDO::getReviewStatus, reqVO.getReviewStatus())
                .eqIfPresent(ProdConsumptionDataDO::getProdVol, reqVO.getProdVol())
                .eqIfPresent(ProdConsumptionDataDO::getInFlowVol, reqVO.getInFlowVol())
                .eqIfPresent(ProdConsumptionDataDO::getTreatVol, reqVO.getTreatVol())
                .eqIfPresent(ProdConsumptionDataDO::getElecCons, reqVO.getElecCons())
                .eqIfPresent(ProdConsumptionDataDO::getElecSingleCons, reqVO.getElecSingleCons())
                .eqIfPresent(ProdConsumptionDataDO::getCarbonUsage, reqVO.getCarbonUsage())
                .eqIfPresent(ProdConsumptionDataDO::getCarbonSingleCons, reqVO.getCarbonSingleCons())
                .eqIfPresent(ProdConsumptionDataDO::getSodiumHypoUsage, reqVO.getSodiumHypoUsage())
                .eqIfPresent(ProdConsumptionDataDO::getSodiumHypoSingleCons, reqVO.getSodiumHypoSingleCons())
                .eqIfPresent(ProdConsumptionDataDO::getPacUsage, reqVO.getPacUsage())
                .eqIfPresent(ProdConsumptionDataDO::getPacSingleCons, reqVO.getPacSingleCons())
                .eqIfPresent(ProdConsumptionDataDO::getFerricSulfUsage, reqVO.getFerricSulfUsage())
                .eqIfPresent(ProdConsumptionDataDO::getFerricSulfSingleCons, reqVO.getFerricSulfSingleCons())
                .eqIfPresent(ProdConsumptionDataDO::getNaohUsage, reqVO.getNaohUsage())
                .eqIfPresent(ProdConsumptionDataDO::getNaohSingleCons, reqVO.getNaohSingleCons())
                .eqIfPresent(ProdConsumptionDataDO::getAnPamUsage, reqVO.getAnPamUsage())
                .eqIfPresent(ProdConsumptionDataDO::getAnPamSingleCons, reqVO.getAnPamSingleCons())
                .eqIfPresent(ProdConsumptionDataDO::getCatPamUsage, reqVO.getCatPamUsage())
                .eqIfPresent(ProdConsumptionDataDO::getCatPamSingleCons, reqVO.getCatPamSingleCons())
                .eqIfPresent(ProdConsumptionDataDO::getSludge60Prod, reqVO.getSludge60Prod())
                .eqIfPresent(ProdConsumptionDataDO::getSludge80Prod, reqVO.getSludge80Prod())
                .eqIfPresent(ProdConsumptionDataDO::getDrySludgeProd, reqVO.getDrySludgeProd())
                .eqIfPresent(ProdConsumptionDataDO::getSludgeRate, reqVO.getSludgeRate())
                .eqIfPresent(ProdConsumptionDataDO::getCatPamSludgeUsage, reqVO.getCatPamSludgeUsage())
                .eqIfPresent(ProdConsumptionDataDO::getCatPamSludgeSingleCons, reqVO.getCatPamSludgeSingleCons())
                .eqIfPresent(ProdConsumptionDataDO::getPacSludgeUsage, reqVO.getPacSludgeUsage())
                .eqIfPresent(ProdConsumptionDataDO::getPacSludgeSingleCons, reqVO.getPacSludgeSingleCons())
                .eqIfPresent(ProdConsumptionDataDO::getLiqIronSaltUsage, reqVO.getLiqIronSaltUsage())
                .eqIfPresent(ProdConsumptionDataDO::getLiqIronSaltSingleCons, reqVO.getLiqIronSaltSingleCons())
                .eqIfPresent(ProdConsumptionDataDO::getLimeUsage, reqVO.getLimeUsage())
                .eqIfPresent(ProdConsumptionDataDO::getLimeSingleCons, reqVO.getLimeSingleCons())
                .betweenIfPresent(ProdConsumptionDataDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProdConsumptionDataDO::getId));
    }
    @Select({
            "SELECT\n" +
                    "  f.id AS factory_id,\n" +
                    "  f.name AS factory_name,\n" +
                    "  f.level,\n" +
                    "  f.parent_id,\n" +
                    "  f.order_num,\n" +
                    "  \n" +
                    "  -- 当日数据\n" +
                    "  d.*,\n" +
                    "  \n" +
                    "  -- 本月累计值（使用聚合）\n" +
                    "  SUM(dm.prod_vol) AS month_prod_vol,\n" +
                    "  SUM(dm.in_flow_vol) AS month_in_flow_vol,\n" +
                    "  SUM(dm.treat_vol) AS month_treat_vol,\n" +
                    "  SUM(dm.elec_cons) AS month_elec_cons,\n" +
                    "  SUM(dm.carbon_usage) AS month_carbon_usage,\n" +
                    "  SUM(dm.sodium_hypo_usage) AS month_sodium_hypo_usage,\n" +
                    "  SUM(dm.pac_usage) AS month_pac_usage,\n" +
                    "  SUM(dm.ferric_sulf_usage) AS month_ferric_sulf_usage,\n" +
                    "  SUM(dm.naoh_usage) AS month_naoh_usage,\n" +
                    "  SUM(dm.an_pam_usage) AS month_an_pam_usage,\n" +
                    "  SUM(dm.cat_pam_usage) AS month_cat_pam_usage,\n" +
                    "  SUM(dm.sludge60_prod) AS month_sludge60_prod,\n" +
                    "  SUM(dm.sludge80_prod) AS month_sludge80_prod,\n" +
                    "  SUM(dm.dry_sludge_prod) AS month_dry_sludge_prod,\n" +
                    "  SUM(dm.sludge_rate) AS month_sludge_rate,\n" +
                    "  SUM(dm.cat_pam_sludge_usage) AS month_cat_pam_sludge_usage,\n" +
                    "  SUM(dm.pac_sludge_usage) AS month_pac_sludge_usage,\n" +
                    "  SUM(dm.liq_iron_salt_usage) AS month_liq_iron_salt_usage,\n" +
                    "  SUM(dm.lime_usage) AS month_lime_usage,\n" +
                    "\t\n" +
                    "\t-- 本月平均值(单耗)\n" +
                    "\t\n" +
                    "\t(CASE WHEN SUM(dm.prod_vol) > 0 THEN ROUND(SUM(dm.elec_cons) / SUM(dm.prod_vol), 4) ELSE NULL END) AS month_elec_single_cons,\n" +
                    "  (CASE WHEN SUM(dm.prod_vol) > 0 THEN ROUND(SUM(dm.carbon_usage) / SUM(dm.prod_vol), 4) ELSE NULL END) AS month_carbon_single_cons,\n" +
                    "  (CASE WHEN SUM(dm.prod_vol) > 0 THEN ROUND(SUM(dm.sodium_hypo_usage) / SUM(dm.prod_vol), 4) ELSE NULL END) AS month_sodium_hypo_single_cons,\n" +
                    "  (CASE WHEN SUM(dm.prod_vol) > 0 THEN ROUND(SUM(dm.pac_usage) / SUM(dm.prod_vol), 4) ELSE NULL END) AS month_pac_single_cons,\n" +
                    "  (CASE WHEN SUM(dm.prod_vol) > 0 THEN ROUND(SUM(dm.ferric_sulf_usage) / SUM(dm.prod_vol), 4) ELSE NULL END) AS month_ferric_sulf_single_cons,\n" +
                    "  (CASE WHEN SUM(dm.prod_vol) > 0 THEN ROUND(SUM(dm.naoh_usage) / SUM(dm.prod_vol), 4) ELSE NULL END) AS month_naoH_single_cons,\n" +
                    "  (CASE WHEN SUM(dm.prod_vol) > 0 THEN ROUND(SUM(dm.an_pam_usage) / SUM(dm.prod_vol), 4) ELSE NULL END) AS month_an_pam_single_cons,\n" +
                    "  (CASE WHEN SUM(dm.prod_vol) > 0 THEN ROUND(SUM(dm.cat_pam_usage) / SUM(dm.prod_vol), 4) ELSE NULL END) AS month_cat_pam_single_cons,\n" +
                    "\t(CASE WHEN SUM(dm.prod_vol) > 0 THEN ROUND(SUM(dm.cat_pam_sludge_usage) / SUM(dm.prod_vol), 4) ELSE NULL END) AS month_cat_pam_sludge_single_cons,\n" +
                    "\t(CASE WHEN SUM(dm.prod_vol) > 0 THEN ROUND(SUM(dm.pac_sludge_usage) / SUM(dm.prod_vol), 4) ELSE NULL END) AS month_pac_sludge_single_cons,\n" +
                    "\t(CASE WHEN SUM(dm.prod_vol) > 0 THEN ROUND(SUM(dm.liq_iron_salt_usage) / SUM(dm.prod_vol), 4) ELSE NULL END) AS month_liq_iron_salt_single_cons,\n" +
                    "\t(CASE WHEN SUM(dm.prod_vol) > 0 THEN ROUND(SUM(dm.lime_usage) / SUM(dm.prod_vol), 4) ELSE NULL END) AS month_lime_single_cons\n" +
                    "\n" +
                    "FROM\n" +
                    "  factory f\n" +
                    "\n" +
                    "-- 关联当日数据\n" +
                    "LEFT JOIN water_prod_consumption_data d\n" +
                    "  ON f.id = d.factory_id\n" +
                    "  AND d.date = #{endDate} " +
                    "  AND d.deleted = 0\n" +
                    "  AND d.review_status in (1,2)\n" +
                    "  AND f.deleted = 0\n" +
                    "\n" +
                    "-- 关联本月数据，用于累计\n" +
                    "LEFT JOIN water_prod_consumption_data dm\n" +
                    "  ON f.id = dm.factory_id\n" +
                    "  AND dm.date >= #{startDate} AND dm.date <= #{endDate} " +
                    "  AND f.deleted = 0 \n" +
                    "  AND dm.deleted = 0 \n" +
                    "  AND dm.review_status in (1,2)\n" +
                    "  where f.level = 3\n" +
                    "GROUP BY \n" +
                    "  f.id, \n" +
                    "  d.id  -- 确保group by字段唯一（避免只拿到一个d的字段）\n" +
                    "\n" +
                    "ORDER BY\n" +
                    "  f.order_num;\n"
    })
    List<WaterProdConsumptionStats> getAllStats(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    // 查询聚合数据表（2024年专用）- 返回Map便于处理复杂嵌套对象
    Map<String, Object> selectStatFromStatTable(@Param("factoryId") Long factoryId,
                                               @Param("startDate") LocalDate startDate,
                                               @Param("endDate") LocalDate endDate);

    // 查询原始数据表进行统计计算
    List<ProdConsumptionDataDO> selectDataFromDataTable(@Param("factoryId") Long factoryId,
                                                        @Param("startDate") LocalDate startDate,
                                                        @Param("endDate") LocalDate endDate);
}