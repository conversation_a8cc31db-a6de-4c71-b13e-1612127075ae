package cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 样品管理新增/修改 Request VO")
@Data
public class AssaySampleManagementSaveReqVO {

    @Schema(description = "样品ID", example = "1")
    private Long id;

    @Schema(description = "水厂ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "水厂ID不能为空")
    private Long factoryId;

    @Schema(description = "样品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "SAMPLE-001")
    @NotBlank(message = "样品编号不能为空")
    @Size(max = 50, message = "样品编号长度不能超过50个字符")
    private String sampleCode;

    @Schema(description = "关联采样执行ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "关联采样执行ID不能为空")
    private Long executionId;

    @Schema(description = "样品类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "water")
    @NotBlank(message = "样品类型不能为空")
    @Pattern(regexp = "^(water|sludge|gas)$", message = "样品类型只能是water、sludge或gas")
    private String sampleType;

    @Schema(description = "样品体积(mL)", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @NotNull(message = "样品体积不能为空")
    @Min(value = 1, message = "样品体积必须大于0")
    private Integer volume;

    @Schema(description = "样品外观", example = "微黄色，略浑浊")
    @Size(max = 500, message = "样品外观长度不能超过500个字符")
    private String appearance;

    @Schema(description = "保存方法", requiredMode = Schema.RequiredMode.REQUIRED, example = "refrigerated")
    @NotBlank(message = "保存方法不能为空")
    @Pattern(regexp = "^(normal|refrigerated|frozen)$", message = "保存方法只能是normal、refrigerated或frozen")
    private String preservationMethod;

    @Schema(description = "存储位置", requiredMode = Schema.RequiredMode.REQUIRED, example = "冷藏室A区")
    @NotBlank(message = "存储位置不能为空")
    @Size(max = 100, message = "存储位置长度不能超过100个字符")
    private String storageLocation;

    @Schema(description = "存储温度(℃)", example = "4")
    private Integer storageTemperature;

    @Schema(description = "有效期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-01-22")
    @NotNull(message = "有效期不能为空")
    private LocalDate expiryDate;

    @Schema(description = "检测项目ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "检测项目ID不能为空")
    private Long testItem;

    @Schema(description = "采样人员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    @NotNull(message = "采样人员ID不能为空")
    private Long samplingPersonId;

    @Schema(description = "采样日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-01-15")
    @NotNull(message = "采样日期不能为空")
    private LocalDate samplingDate;

    @Schema(description = "销毁原因", example = "超过有效期")
    @Size(max = 500, message = "销毁原因长度不能超过500个字符")
    private String destroyReason;

    @Schema(description = "销毁时间", example = "2024-01-30T10:00:00")
    private LocalDateTime destroyTime;

    @Schema(description = "销毁操作人ID", example = "1002")
    private Long destroyOperatorId;

    @Schema(description = "备注", example = "正常样品")
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String remark;

}
