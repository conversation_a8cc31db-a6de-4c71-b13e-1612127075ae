package cn.tl.cloud.module.monitor.dal.mysql.acqconf;

import cn.tl.cloud.module.monitor.dal.dataobject.acqconf.IotPoint;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 *
 */
@Mapper
@DS("iot")
public interface IotPointMapper {

    /**
     * 查询指标个数
     * @return
     */
    @Select("select count(*) from iot_point ip left join dc3_device dd on ip.device_id = dd.id left join dc3_group dg on dd.group_id = dg.id \n" +
            " where ip.deleted = 0 and dd.deleted = 0 and dg.deleted = 0 and dg.enable_flag = 1  ")
    Long count();

    /**
     * 分页查询点位列表
     * @param page
     * @param queryWrapper
     * @return
     */
    @Select("select id,point_code as pointCode,point_name as pointName,enable_flag as enableFlag,unit as pointUnit,remark from iot_point  ${ew.customSqlSegment} ")
    Page<IotPoint> queryPage(@Param("page") Page<IotPoint> page,@Param(Constants.WRAPPER) QueryWrapper<IotPoint> queryWrapper);

}
