package cn.tl.cloud.module.monitor.enums;

import cn.tl.cloud.framework.common.exception.ErrorCode;

/**
 * <AUTHOR>
 * @projectName water
 * @description
 * @date 2025/6/5 16:57
 */
public interface ErrorCodeConstants {


        ErrorCode EVENT_NOT_EXISTS = new ErrorCode(1_050_001_000, "告警事件不存在");
        ErrorCode RULE_FACTOR_NOT_EXISTS = new ErrorCode(1_050_001_001, "告警规则因子表（组合表达式变量配置/监测因子）不存在");
        ErrorCode RULE_NOT_EXISTS = new ErrorCode(1_050_001_002, "告警规则不存在");
        ErrorCode RULE_IS_EXISTS = new ErrorCode(1_050_001_007, "告警规则已存在");
        ErrorCode NOTIFY_CONFIG_NOT_EXISTS = new ErrorCode(1_050_001_003, "通知配置不存在");
        ErrorCode ACTION_LOG_NOT_EXISTS = new ErrorCode(1_050_001_005, "处理日志不存在");
        //转换异常
        ErrorCode POINT_VALUE_NOT_NUMBER  = new ErrorCode(1_050_001_004, "指标值非数字类型");
        ErrorCode NOTIFY_TYPE_CONVERT_ERROR  = new ErrorCode(1_050_001_006, "通知方式类型转换异常");


        ErrorCode PROCESS_SCREEN_NOT_EXISTS = new ErrorCode(1_060_001_000, "工艺段信息不存在");
        ErrorCode PROCESS_SCREEN_IS_EXISTS = new ErrorCode(1_060_001_003, "工艺段信息不存在");
        ErrorCode INDICATOR_NOT_EXISTS = new ErrorCode(1_060_001_001, "监测指标信息不存在");

        ErrorCode DEVICE_NOT_EXISTS = new ErrorCode(1_060_001_002, "设备信息不存在");
        ErrorCode DEVICE_CODE_EXISTS = new ErrorCode(1_060_001_004, "设备编码已经存在");

        // ========== 基础信息管理模块错误码 (1070-1079) ==========
        ErrorCode TEST_CATEGORY_NOT_EXISTS = new ErrorCode(1_070_001_000, "检测项目类型不存在");
        ErrorCode TEST_CATEGORY_CODE_DUPLICATE = new ErrorCode(1_070_001_001, "检测项目类型代码在该水厂已存在");
        ErrorCode TEST_CATEGORY_HAS_PROJECTS = new ErrorCode(1_070_001_002, "类型下存在项目，无法删除");

        ErrorCode TEST_PROJECT_NOT_EXISTS = new ErrorCode(1_070_002_000, "检测项目不存在");
        ErrorCode TEST_PROJECT_CODE_DUPLICATE = new ErrorCode(1_070_002_001, "检测项目代码在该水厂已存在");
        ErrorCode TEST_PROJECT_HAS_INDICATORS = new ErrorCode(1_070_002_002, "项目下存在指标，无法删除");
        ErrorCode TEST_PROJECT_CATEGORY_NOT_EXISTS = new ErrorCode(1_070_002_003, "所属类型不存在或不属于该水厂");

        ErrorCode TEST_INDICATOR_NOT_EXISTS = new ErrorCode(1_070_003_000, "检测指标不存在");
        ErrorCode TEST_INDICATOR_CODE_DUPLICATE = new ErrorCode(1_070_003_001, "检测指标代码在该水厂已存在");
        ErrorCode TEST_INDICATOR_PROJECT_NOT_EXISTS = new ErrorCode(1_070_003_002, "所属项目不存在或不属于该水厂");
        ErrorCode TEST_INDICATOR_STANDARD_RANGE_ERROR = new ErrorCode(1_070_003_003, "标准值范围错误，上限必须大于下限");

        ErrorCode SAMPLING_POINT_NOT_EXISTS = new ErrorCode(1_070_004_000, "采样点不存在");
        ErrorCode SAMPLING_POINT_CODE_DUPLICATE = new ErrorCode(1_070_004_001, "采样点编码在该水厂已存在");
        ErrorCode SAMPLING_POINT_IN_USE = new ErrorCode(1_070_004_002, "采样点正在使用中，无法删除");
        ErrorCode SAMPLING_POINT_TYPE_INVALID = new ErrorCode(1_070_004_003, "采样点类型枚举值无效");

        ErrorCode FACTORY_DATA_ACCESS_DENIED = new ErrorCode(1_070_005_000, "水厂ID无效或无权限");

        // ========== 采样计划管理模块错误码 (1071-1072) ==========
        ErrorCode SAMPLING_PLAN_NOT_EXISTS = new ErrorCode(1_071_001_000, "采样计划不存在");
        ErrorCode SAMPLING_PLAN_CODE_DUPLICATE = new ErrorCode(1_071_001_001, "计划编号在该水厂已存在");
        ErrorCode SAMPLING_PLAN_NAME_DUPLICATE = new ErrorCode(1_071_001_002, "计划名称在该水厂已存在");
        ErrorCode SAMPLING_PLAN_HAS_TASKS = new ErrorCode(1_071_001_003, "计划下存在任务，无法删除");
        ErrorCode SAMPLING_PLAN_DATE_RANGE_ERROR = new ErrorCode(1_071_001_004, "开始日期不能晚于结束日期");
        ErrorCode SAMPLING_PLAN_SAME_PERSON_ERROR = new ErrorCode(1_071_001_005, "采样人员、检测人员、审核人员不能是同一人");
        ErrorCode SAMPLING_PLAN_TEST_ITEM_NOT_EXISTS = new ErrorCode(1_071_001_006, "检测项目不存在或不属于该水厂");
        ErrorCode SAMPLING_PLAN_SAMPLING_POINT_NOT_EXISTS = new ErrorCode(1_071_001_007, "采样点不存在或不属于该水厂");
        ErrorCode SAMPLING_PLAN_TEMPORARY_REASON_REQUIRED = new ErrorCode(1_071_001_008, "临时计划创建原因不能为空");
        ErrorCode SAMPLING_PLAN_REGULAR_FIELDS_REQUIRED = new ErrorCode(1_071_001_009, "常规计划的频率、开始日期、结束日期不能为空");
        ErrorCode SAMPLING_PLAN_TEMPORARY_DATETIME_REQUIRED = new ErrorCode(1_071_001_010, "临时计划的执行时间不能为空");
        ErrorCode SAMPLING_PLAN_CONFLICT_EXISTS = new ErrorCode(1_071_001_011, "计划存在时间冲突");

        ErrorCode SAMPLING_TASK_NOT_EXISTS = new ErrorCode(1_071_002_000, "采样任务不存在");
        ErrorCode SAMPLING_TASK_CODE_DUPLICATE = new ErrorCode(1_071_002_001, "任务编号在该水厂已存在");
        ErrorCode SAMPLING_TASK_STATUS_ERROR = new ErrorCode(1_071_002_002, "任务状态不允许此操作");
        ErrorCode SAMPLING_TASK_PLAN_NOT_EXISTS = new ErrorCode(1_071_002_003, "关联的采样计划不存在");
        ErrorCode SAMPLING_TASK_ASSIGN_SAME_PERSON_ERROR = new ErrorCode(1_071_002_004, "分配的人员角色不能相同");
        ErrorCode SAMPLING_TASK_GENERATE_ERROR = new ErrorCode(1_071_002_005, "任务生成失败");

        // ========== 采样执行管理模块错误码 (1072-1074) ==========
        ErrorCode SAMPLING_EXECUTION_NOT_EXISTS = new ErrorCode(1_072_001_000, "采样执行记录不存在");
        ErrorCode SAMPLING_EXECUTION_TASK_NOT_EXISTS = new ErrorCode(1_072_001_001, "关联的采样任务不存在或不属于该水厂");
        ErrorCode SAMPLING_EXECUTION_STATUS_ERROR = new ErrorCode(1_072_001_002, "采样执行状态不允许此操作");
        ErrorCode SAMPLING_EXECUTION_ALREADY_EXISTS = new ErrorCode(1_072_001_003, "该任务已存在采样执行记录");

        ErrorCode SAMPLE_MANAGEMENT_NOT_EXISTS = new ErrorCode(1_072_002_000, "样品管理记录不存在");
        ErrorCode SAMPLE_MANAGEMENT_CODE_DUPLICATE = new ErrorCode(1_072_002_001, "样品编号在该水厂已存在");
        ErrorCode SAMPLE_MANAGEMENT_EXECUTION_NOT_EXISTS = new ErrorCode(1_072_002_002, "关联的采样执行记录不存在或不属于该水厂");
        ErrorCode SAMPLE_MANAGEMENT_STATUS_ERROR = new ErrorCode(1_072_002_003, "样品状态不允许此操作");
        ErrorCode SAMPLE_MANAGEMENT_EXPIRED = new ErrorCode(1_072_002_004, "样品已过期");

        ErrorCode SUBMISSION_RECORD_NOT_EXISTS = new ErrorCode(1_072_003_000, "送检记录不存在");
        ErrorCode SUBMISSION_RECORD_CODE_DUPLICATE = new ErrorCode(1_072_003_001, "送检记录编号在该水厂已存在");
        ErrorCode SUBMISSION_RECORD_EXECUTION_NOT_EXISTS = new ErrorCode(1_072_003_002, "关联的采样执行记录不存在或不属于该水厂");
        ErrorCode SUBMISSION_RECORD_STATUS_ERROR = new ErrorCode(1_072_003_003, "送检记录状态不允许此操作");
        ErrorCode SUBMISSION_RECORD_ALREADY_EXISTS = new ErrorCode(1_072_003_004, "该执行记录已存在送检记录");


}
