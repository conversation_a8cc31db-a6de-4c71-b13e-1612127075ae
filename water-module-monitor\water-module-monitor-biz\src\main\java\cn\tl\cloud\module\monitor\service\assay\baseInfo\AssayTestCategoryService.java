package cn.tl.cloud.module.monitor.service.assay.baseInfo;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestCategoryPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestCategorySaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestCategoryDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 检测项目类型 Service 接口
 *
 * <AUTHOR>
 */
public interface AssayTestCategoryService {

    /**
     * 创建检测项目类型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTestCategory(@Valid AssayTestCategorySaveReqVO createReqVO);

    /**
     * 更新检测项目类型
     *
     * @param updateReqVO 更新信息
     */
    void updateTestCategory(@Valid AssayTestCategorySaveReqVO updateReqVO);

    /**
     * 删除检测项目类型
     *
     * @param id 编号
     * @param factoryId 水厂ID
     */
    void deleteTestCategory(Long id, Long factoryId);

    /**
     * 获得检测项目类型
     *
     * @param id 编号
     * @param factoryId 水厂ID
     * @return 检测项目类型
     */
    AssayTestCategoryDO getTestCategory(Long id, Long factoryId);

    /**
     * 获得检测项目类型分页
     *
     * @param pageReqVO 分页查询
     * @return 检测项目类型分页
     */
    PageResult<AssayTestCategoryDO> getTestCategoryPage(AssayTestCategoryPageReqVO pageReqVO);

    /**
     * 获得检测项目类型列表
     *
     * @param reqVO 查询条件
     * @return 检测项目类型列表
     */
    List<AssayTestCategoryDO> getTestCategoryList(AssayTestCategoryPageReqVO reqVO);

    /**
     * 获得检测项目类型精简列表
     *
     * @param factoryId 水厂ID
     * @return 检测项目类型精简列表
     */
    List<AssayTestCategoryDO> getTestCategorySimpleList(Long factoryId);

}
