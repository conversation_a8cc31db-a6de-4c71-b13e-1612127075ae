package cn.tl.cloud.module.report.dal.mysql.prodqualitydata;

import cn.tl.cloud.module.report.controller.admin.report.vo.quality.WaterQualityStatVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface WaterQualityMapper extends BaseMapper<WaterQualityStatVO> {

    /**
     * 查询当天数据和当月统计数据（Java 8兼容版）
     * @return 包含当天数据和当月统计的结果
     */
    @Select(
            "SELECT " +
                    "    f.id AS factory_id, " +
                    "    f.name AS factory_name, " +
                    "    f.parent_id, " +
                    "    f.order_num, " +
                    "    d.*, " +
                    "    m.in_ph_max, m.in_ph_min, m.in_ph_avg,m.in_ph_sum, " +
                    "    m.out_ph_max, m.out_ph_min, m.out_ph_avg,m.out_ph_sum, " +
                    "    m.in_temp_max, m.in_temp_min, m.in_temp_avg,m.in_temp_sum, " +
                    "    m.out_temp_max, m.out_temp_min, m.out_temp_avg,m.out_temp_sum, " +
                    "    m.in_codcr_max, m.in_codcr_min, m.in_codcr_avg,m.in_codcr_sum, " +
                    "    m.out_codcr_max, m.out_codcr_min, m.out_codcr_avg,m.out_codcr_sum, " +
                    "    m.in_bod5_max, m.in_bod5_min, m.in_bod5_avg,m.in_bod5_sum, " +
                    "    m.out_bod5_max, m.out_bod5_min, m.out_bod5_avg,m.out_bod5_sum, " +
                    "    m.in_ss_max, m.in_ss_min, m.in_ss_avg,m.in_ss_sum, " +
                    "    m.out_ss_max, m.out_ss_min, m.out_ss_avg,m.out_ss_sum, " +
                    "    m.in_nh3n_max, m.in_nh3n_min, m.in_nh3n_avg,m.in_nh3n_sum, " +
                    "    m.out_nh3n_max, m.out_nh3n_min, m.out_nh3n_avg,m.out_nh3n_sum, " +
                    "    m.in_tn_max, m.in_tn_min, m.in_tn_avg,m.in_tn_sum, " +
                    "    m.out_tn_max, m.out_tn_min, m.out_tn_avg,m.out_tn_sum, " +
                    "    m.in_tp_max, m.in_tp_min, m.in_tp_avg,m.in_tp_sum, " +
                    "    m.out_tp_max, m.out_tp_min, m.out_tp_avg,m.out_tp_sum, " +
                    "    m.in_flow_water_volume_max, m.in_flow_water_volume_min, m.in_flow_water_volume_avg,m.in_flow_water_volume_sum, " +
                    "    m.daily_treatment_vol_max, m.daily_treatment_vol_min, m.daily_treatment_vol_avg,m.daily_treatment_vol_sum " +
                    "FROM " +
                    "    factory f " +
                    "LEFT JOIN water_prod_quality_data d " +
                    "    ON f.id = d.factory_id and d.review_status in (1,2)  " +
                    "    AND d.date = #{endDate} " +
                    "LEFT JOIN ( " +
                    "    SELECT " +
                    "        factory_id, " +
                    "        MAX(in_ph) AS in_ph_max, MIN(in_ph) AS in_ph_min, AVG(in_ph) AS in_ph_avg,SUM(in_ph) AS in_ph_sum, " +
                    "        MAX(out_ph) AS out_ph_max, MIN(out_ph) AS out_ph_min, AVG(out_ph) AS out_ph_avg,SUM(out_ph) AS out_ph_sum, " +
                    "        MAX(in_temp) AS in_temp_max, MIN(in_temp) AS in_temp_min, AVG(in_temp) AS in_temp_avg,SUM(in_temp) AS in_temp_sum, " +
                    "        MAX(out_temp) AS out_temp_max, MIN(out_temp) AS out_temp_min, AVG(out_temp) AS out_temp_avg,SUM(out_temp) AS out_temp_sum, " +
                    "        MAX(in_codcr) AS in_codcr_max, MIN(in_codcr) AS in_codcr_min, AVG(in_codcr) AS in_codcr_avg,SUM(in_codcr) AS in_codcr_sum, " +
                    "        MAX(out_codcr) AS out_codcr_max, MIN(out_codcr) AS out_codcr_min, AVG(out_codcr) AS out_codcr_avg,SUM(out_codcr) AS out_codcr_sum, " +
                    "        MAX(in_bod5) AS in_bod5_max, MIN(in_bod5) AS in_bod5_min, AVG(in_bod5) AS in_bod5_avg,SUM(in_bod5) AS in_bod5_sum, " +
                    "        MAX(out_bod5) AS out_bod5_max, MIN(out_bod5) AS out_bod5_min, AVG(out_bod5) AS out_bod5_avg,SUM(out_bod5) AS out_bod5_sum, " +
                    "        MAX(in_ss) AS in_ss_max, MIN(in_ss) AS in_ss_min, AVG(in_ss) AS in_ss_avg,SUM(in_ss) AS in_ss_sum, " +
                    "        MAX(out_ss) AS out_ss_max, MIN(out_ss) AS out_ss_min, AVG(out_ss) AS out_ss_avg,SUM(out_ss) AS out_ss_sum, " +
                    "        MAX(in_nh3n) AS in_nh3n_max, MIN(in_nh3n) AS in_nh3n_min, AVG(in_nh3n) AS in_nh3n_avg,SUM(in_nh3n) AS in_nh3n_sum, " +
                    "        MAX(out_nh3n) AS out_nh3n_max, MIN(out_nh3n) AS out_nh3n_min, AVG(out_nh3n) AS out_nh3n_avg,SUM(out_nh3n) AS out_nh3n_sum, " +
                    "        MAX(in_tn) AS in_tn_max, MIN(in_tn) AS in_tn_min, AVG(in_tn) AS in_tn_avg,SUM(in_tn) AS in_tn_sum, " +
                    "        MAX(out_tn) AS out_tn_max, MIN(out_tn) AS out_tn_min, AVG(out_tn) AS out_tn_avg,SUM(out_tn) AS out_tn_sum, " +
                    "        MAX(in_tp) AS in_tp_max, MIN(in_tp) AS in_tp_min, AVG(in_tp) AS in_tp_avg,SUM(in_tp) AS in_tp_sum, " +
                    "        MAX(out_tp) AS out_tp_max, MIN(out_tp) AS out_tp_min, AVG(out_tp) AS out_tp_avg,SUM(out_tp) AS out_tp_sum, " +
                    "        MAX(in_flow_water_volume) AS in_flow_water_volume_max, MIN(in_flow_water_volume) AS in_flow_water_volume_min, AVG(in_flow_water_volume) AS in_flow_water_volume_avg,SUM(in_flow_water_volume) AS in_flow_water_volume_sum, " +
                    "        MAX(daily_treatment_vol) AS daily_treatment_vol_max, MIN(daily_treatment_vol) AS daily_treatment_vol_min, AVG(daily_treatment_vol) AS daily_treatment_vol_avg,SUM(daily_treatment_vol) AS daily_treatment_vol_sum " +
                    "    FROM water_prod_quality_data " +
                    "    WHERE " +
                    "        date >= #{startDate} " +
                    "        AND date <= #{endDate} " +
                    "        AND review_status in (1,2) " +
                    "    GROUP BY factory_id " +
                    ") m ON f.id = m.factory_id  " +
                    "where f.deleted = 0 and f.level = 3 " +
                    "order by f.order_num"
    )
    List<WaterQualityStatVO> selectTargetStats(@Param("startDate")LocalDate startDate, @Param("endDate")LocalDate endDate);

    /**
     * 查询聚合表数据（2024年专用）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 聚合统计数据列表
     */
    @Select(
            "SELECT " +
                    "    f.id AS factory_id, " +
                    "    f.name AS factory_name, " +
                    "    f.parent_id, " +
                    "    f.level, " +
                    "    f.order_num, " +
                    "    s.stat_date AS date, " +
                    "    s.in_ph_sum, s.in_ph_avg, s.in_ph_max, s.in_ph_min, " +
                    "    s.out_ph_sum, s.out_ph_avg, s.out_ph_max, s.out_ph_min, " +
                    "    s.in_temp_sum, s.in_temp_avg, s.in_temp_max, s.in_temp_min, " +
                    "    s.out_temp_sum, s.out_temp_avg, s.out_temp_max, s.out_temp_min, " +
                    "    s.in_codcr_sum, s.in_codcr_avg, s.in_codcr_max, s.in_codcr_min, " +
                    "    s.out_codcr_sum, s.out_codcr_avg, s.out_codcr_max, s.out_codcr_min, " +
                    "    s.in_bod5_sum, s.in_bod5_avg, s.in_bod5_max, s.in_bod5_min, " +
                    "    s.out_bod5_sum, s.out_bod5_avg, s.out_bod5_max, s.out_bod5_min, " +
                    "    s.in_ss_sum, s.in_ss_avg, s.in_ss_max, s.in_ss_min, " +
                    "    s.out_ss_sum, s.out_ss_avg, s.out_ss_max, s.out_ss_min, " +
                    "    s.in_nh3n_sum, s.in_nh3n_avg, s.in_nh3n_max, s.in_nh3n_min, " +
                    "    s.out_nh3n_sum, s.out_nh3n_avg, s.out_nh3n_max, s.out_nh3n_min, " +
                    "    s.in_tn_sum, s.in_tn_avg, s.in_tn_max, s.in_tn_min, " +
                    "    s.out_tn_sum, s.out_tn_avg, s.out_tn_max, s.out_tn_min, " +
                    "    s.in_tp_sum, s.in_tp_avg, s.in_tp_max, s.in_tp_min, " +
                    "    s.out_tp_sum, s.out_tp_avg, s.out_tp_max, s.out_tp_min, " +
                    "    s.in_flow_water_volume_sum, s.in_flow_water_volume_avg, s.in_flow_water_volume_max, s.in_flow_water_volume_min, " +
                    "    s.daily_treatment_vol_sum, s.daily_treatment_vol_avg, s.daily_treatment_vol_max, s.daily_treatment_vol_min " +
                    "FROM " +
                    "    factory f " +
                    "INNER JOIN water_prod_quality_stat s " +
                    "    ON f.id = s.factory_id " +
                    "WHERE " +
                    "    f.deleted = 0 " +
                    "    AND f.level = 3 " +
                    "    AND s.stat_type = 3 " +
                    "    AND s.status = 1 " +
                    "    AND s.stat_date >= #{startDate} " +
                    "    AND s.stat_date <= #{endDate} " +
                    "ORDER BY f.order_num"
    )
    List<WaterQualityStatVO> selectTargetStatsFromAggTable(@Param("startDate")LocalDate startDate, @Param("endDate")LocalDate endDate);

}
