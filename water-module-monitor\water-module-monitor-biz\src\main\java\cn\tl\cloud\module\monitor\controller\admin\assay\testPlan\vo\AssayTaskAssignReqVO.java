package cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Schema(description = "管理后台 - 任务分配 Request VO")
@Data
public class AssayTaskAssignReqVO {

    @Schema(description = "任务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "任务ID不能为空")
    private Long taskId;

    @Schema(description = "采样人员ID", example = "1001")
    private Long samplerId;

    @Schema(description = "检测人员ID", example = "1002")
    private Long testerId;

    @Schema(description = "审核人员ID", example = "1003")
    private Long reviewerId;

    @Schema(description = "分配原因", example = "正常分配执行")
    @Size(max = 500, message = "分配原因长度不能超过500个字符")
    private String assignReason;

}
