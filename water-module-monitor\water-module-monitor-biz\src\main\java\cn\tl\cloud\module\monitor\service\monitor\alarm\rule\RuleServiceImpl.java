package cn.tl.cloud.module.monitor.service.monitor.alarm.rule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.module.monitor.common.utils.AlarmExpressionEvaluator;
import cn.tl.cloud.module.monitor.common.utils.RedisCacheHelper;
import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.rule.vo.RulePageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.rule.vo.RuleSaveReqVO;
import cn.tl.cloud.module.monitor.dal.clickhouse.FactoryDevicePointMappingMapper;
import cn.tl.cloud.module.monitor.dal.dataobject.monitor.FactoryDevicePointMappingDO;
import cn.tl.cloud.module.monitor.dal.dataobject.monitor.alarm.event.EventDO;
import cn.tl.cloud.module.monitor.dal.dataobject.monitor.alarm.notifyconfig.NotifyConfigDO;
import cn.tl.cloud.module.monitor.dal.dataobject.monitor.alarm.rule.RuleDO;
import cn.tl.cloud.module.monitor.dal.dataobject.monitor.alarm.rulefactor.RuleFactorDO;
import cn.tl.cloud.module.monitor.dal.dataobject.monitor.ods.OdsIotDataDO;
import cn.tl.cloud.module.monitor.dal.mysql.monitor.alarm.event.EventMapper;
import cn.tl.cloud.module.monitor.dal.mysql.monitor.alarm.notifyconfig.NotifyConfigMapper;
import cn.tl.cloud.module.monitor.dal.mysql.monitor.alarm.rule.RuleMapper;
import cn.tl.cloud.module.monitor.dal.mysql.monitor.alarm.rulefactor.RuleFactorMapper;
import cn.tl.cloud.module.monitor.enums.PointTypeFlagEnum;
import cn.tl.cloud.module.monitor.mq.DTO.AlarmPushDTO;
import cn.tl.cloud.module.monitor.mq.producer.AlarmNotifyProducer;
import cn.tl.cloud.module.monitor.service.monitor.ods.OdsIotDataService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.googlecode.aviator.AviatorEvaluator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.tl.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.tl.cloud.module.monitor.common.contants.RedisConstants.*;
import static cn.tl.cloud.module.monitor.enums.ErrorCodeConstants.*;


/**
 * 告警规则 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class RuleServiceImpl implements RuleService {

    @Resource
    private RuleMapper ruleMapper;
    @Resource
    private FactoryDevicePointMappingMapper mappingMapper;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private NotifyConfigMapper notifyConfigMapper;

    @Resource
    private EventMapper eventMapper;

    @Resource
    private OdsIotDataService odsIotDataService;

    @Resource
    private AlarmNotifyProducer alarmNotifyProducer;

    private static LocalDateTime lastCheckTime = LocalDateTime.now().minusMinutes(3); // 初始化拉取窗口


    @Resource
    private RuleFactorMapper ruleFactorMapper;

    @Resource
    private RedisCacheHelper redisCacheHelper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createRule(RuleSaveReqVO createReqVO) {
        // 插入
        //插入前确保规则名不重复
        validateRuleNameUnique(createReqVO.getName());
        RuleDO rule = BeanUtils.toBean(createReqVO, RuleDO.class);
        ruleMapper.insert(rule);

        // 插入子表
        createRuleFactorList(rule.getId(), createReqVO.getRuleFactors());
        //删除缓存
        redisTemplate.delete(REDIS_RULE_PREFIX + createReqVO.getFactoryId());
        redisTemplate.delete(REDIS_RULE_FACTOR_PREFIX + rule.getId());
        // 返回
        return rule.getId();
    }

    private void validateRuleNameUnique(String name) {
        if (ruleMapper.selectCount(new LambdaQueryWrapper<RuleDO>().eq(RuleDO::getName, name)) > 0) {
            throw exception(RULE_IS_EXISTS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRule(RuleSaveReqVO updateReqVO) {
        // 校验存在
        validateRuleExists(updateReqVO.getId());
        // 更新
        RuleDO updateObj = BeanUtils.toBean(updateReqVO, RuleDO.class);
        ruleMapper.updateById(updateObj);

        if (updateReqVO.getRuleFactors() != null) {
            // 更新子表
            updateRuleFactorList(updateReqVO.getId(), updateReqVO.getRuleFactors());
        }
        //删除缓存
        redisTemplate.delete(REDIS_RULE_PREFIX + updateReqVO.getFactoryId());
        redisTemplate.delete(REDIS_RULE_FACTOR_PREFIX + updateObj.getId());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRule(Long id) {
        // 校验存在
        validateRuleExists(id);
        // 删除
        ruleMapper.deleteById(id);

        // 删除子表
        deleteRuleFactorByRuleId(id);
        //删除缓存
        redisTemplate.delete(REDIS_RULE_PREFIX + id);
        redisTemplate.delete(REDIS_RULE_FACTOR_PREFIX + id);
    }

    private void validateRuleExists(Long id) {
        if (ruleMapper.selectById(id) == null) {
            throw exception(RULE_NOT_EXISTS);
        }
    }

    @Override
    public RuleDO getRule(Long id) {
        return ruleMapper.selectById(id);
    }

    @Override
    public RuleSaveReqVO getRuleAndRuleFactors(Long id) {
        RuleDO ruleDO = ruleMapper.selectById(id);
        List<RuleFactorDO> ruleFactorList = getRuleFactorList(id);
        RuleSaveReqVO ruleSaveReqVO = BeanUtils.toBean(ruleDO, RuleSaveReqVO.class);
        ruleSaveReqVO.setRuleFactors(ruleFactorList);
        return ruleSaveReqVO;
    }

    @Override
    public PageResult<RuleDO> getRulePage(RulePageReqVO pageReqVO) {
        return ruleMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（告警规则因子表（组合表达式变量配置/监测因子）） ====================

    @Override
    public List<RuleFactorDO> getRuleFactorListByRuleId(Long ruleId) {
        return ruleFactorMapper.selectListByRuleId(ruleId);
    }

    private void createRuleFactorList(Long ruleId, List<RuleFactorDO> list) {
        list.forEach(o -> o.setRuleId(ruleId));
        ruleFactorMapper.insertBatch(list);
    }

    private void updateRuleFactorList(Long ruleId, List<RuleFactorDO> list) {
        deleteRuleFactorByRuleId(ruleId);
        list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createRuleFactorList(ruleId, list);
    }

    private void deleteRuleFactorByRuleId(Long ruleId) {
        ruleFactorMapper.deleteByRuleId(ruleId);
    }






    public List<FactoryDevicePointMappingDO> getMappingList(String factoryId){
        List<FactoryDevicePointMappingDO> mappingListByFactory = redisCacheHelper.get(
                REDIS_MAPPING_PREFIX + factoryId,
                new TypeReference<List<FactoryDevicePointMappingDO>>() {
                },
                () -> mappingMapper.getAllMappingByFactoryId(factoryId),
                30,
                TimeUnit.MINUTES,
                5,
                TimeUnit.MINUTES);

        return mappingListByFactory;
    }

    public List<RuleDO> getRuleList(String factoryId) {
        List<RuleDO> ruleList = redisCacheHelper.get(
                REDIS_RULE_PREFIX + factoryId,
                new TypeReference<List<RuleDO>>() {
                },
                () -> ruleMapper.selectEnabledRulesByFactoryId(factoryId),
                30,
                TimeUnit.MINUTES,
                5,
                TimeUnit.MINUTES);

        return ruleList;
    }

    public List<RuleFactorDO> getRuleFactorList(Long ruleId) {
        List<RuleFactorDO> ruleFactorList = redisCacheHelper.get(
                REDIS_RULE_FACTOR_PREFIX + ruleId,
                new TypeReference<List<RuleFactorDO>>() {
                },
                () -> ruleFactorMapper.selectListByRuleId(ruleId),
                30,
                TimeUnit.MINUTES,
                5,
                TimeUnit.MINUTES);

        return ruleFactorList;
    }

    // 简单的 Redis Key 校验方法
    private boolean isValidRedisKey(String key) {
        return key != null && !key.contains(" ") && !key.contains("\u0000");
    }

    /**
     * 从mq中获取数据进行告警规则计算
     * @param factoryId
     * @param odsIotDataDOList
     */
    @Override
    public void evaluateAlarmRulesByFactory(String factoryId, List<OdsIotDataDO> odsIotDataDOList) {
        LocalDateTime now = LocalDateTime.now();
        List<RuleDO> rules = getRuleList(factoryId);
        /**
         * 将所有 mapping（FactoryDevicePointMappingDO）数据根据 factoryId 存入 Redis 中，
         * 以 factoryId 为 key，value 为factoryDevicePointMappingDO，
         * 然后在规则计算中直接从 Redis 中获取映射，提高性能并避免多次数据库查询。
         */

        List<FactoryDevicePointMappingDO> mappingListByFactory = getMappingList(factoryId);

        Map<String, FactoryDevicePointMappingDO> mappingMap = mappingListByFactory.stream()
                .collect(Collectors.toMap(
                        // 使用 deviceId + pointId 作为拼接 key
                        item -> item.getDeviceId() + "_" + item.getPointId(),
                        Function.identity(),
                        (existing, replacement) -> existing // 如果有重复 key，保留第一个
                ));

        //存放告警数据，后续批量插入
        List<EventDO> eventList = new ArrayList<>();
        for (RuleDO rule : rules) {
            List<OdsIotDataDO> filteredDataList = filterDataByRuleFactor(rule, odsIotDataDOList, mappingMap);
            //不合法的数据为空，跳过这次规则校验
            if (CollUtil.isEmpty(filteredDataList)) {
                return;
            }

            if ("single".equals(rule.getRuleType())) {
                //单因子逻辑
                handleSingleRule(rule, filteredDataList, mappingMap, eventList);
            } else {
                handleCombinationRule(rule, filteredDataList,  mappingMap, eventList);
            }

        }
        // 批量插入事件
        if (!eventList.isEmpty()) {
            eventMapper.insertBatch(eventList);
        }
        lastCheckTime = now;
    }


    /**
     * 每次进入因子规则判断前先先筛选指标项
     * @param rule
     * @param odsIotDataDOList
     * @param mappingMap
     * @return
     */
    private List<OdsIotDataDO> filterDataByRuleFactor(RuleDO rule, List<OdsIotDataDO> odsIotDataDOList, Map<String,
            FactoryDevicePointMappingDO> mappingMap){

        Long id = rule.getId();
        List<RuleFactorDO> ruleFactorList = getRuleFactorList(id);
        //通过ruleFactor集合获取指标唯一id集合
        Set<String> indicatorIds = ruleFactorList.stream()
                .map(RuleFactorDO::getIndicatorId)
                .collect(Collectors.toSet());
        /**
         * 过滤odsIotDataDOList,只匹配规则表中有的指标项数据
         */
        List<OdsIotDataDO> filteredDataList = odsIotDataDOList.stream()
                .filter(data -> {
                    FactoryDevicePointMappingDO mappingDO =
                            mappingMap.get(data.getDeviceId() + "_" + data.getPointId());
                    return mappingDO != null && indicatorIds.contains(mappingDO.getIndicatorId());
                })
                .collect(Collectors.toList());


        return filteredDataList;

    }



    //TODO:从数据库拿数据测试
    @Override
    public void evaluateAlarmRulesByFactory(String factoryId) {


        List<RuleDO> rules = getRuleList(factoryId);



        /**
         * 将所有 mapping（FactoryDevicePointMappingDO）数据根据 factoryId 存入 Redis 中，
         * 以 factoryId 为 key，value 为factoryDevicePointMappingDO，
         * 然后在规则计算中直接从 Redis 中获取映射，提高性能并避免多次数据库查询。
         */

        List<FactoryDevicePointMappingDO> mappingListByFactory = getMappingList(factoryId);

        Map<String, FactoryDevicePointMappingDO> mappingMap = mappingListByFactory.stream()
                .collect(Collectors.toMap(
                        // 使用 deviceId + pointId 作为拼接 key
                        item -> item.getDeviceId() + "_" + item.getPointId(),
                        Function.identity(),
                        (existing, replacement) -> existing // 如果有重复 key，保留第一个
                ));
        Set<String> allIndicatorIds = mappingListByFactory.stream()
                .map(FactoryDevicePointMappingDO::getIndicatorId)
                .collect(Collectors.toSet());

        List<OdsIotDataDO> odsIotDataDOList = odsIotDataService.getByTimeRangeByIndicatorIds(factoryId, allIndicatorIds,
                LocalDateTime.parse("2025/07/26 19:33:00",
                        DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")), LocalDateTime.parse("2025/07/26 21:33:00",
                        DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));




        //存放告警数据，后续批量插入
        List<EventDO> eventList = new ArrayList<>();
        for (RuleDO rule : rules) {

            List<OdsIotDataDO> filteredDataList = filterDataByRuleFactor(rule, odsIotDataDOList, mappingMap);
            //不合法的数据为空，跳过这次规则校验
            if (CollUtil.isEmpty(filteredDataList)) return;
            if ("single".equals(rule.getRuleType())) {
                //单因子逻辑
                handleSingleRule(rule, filteredDataList, mappingMap, eventList);
            } else {
                handleCombinationRule(rule, filteredDataList, mappingMap, eventList);
            }

        }
        // 批量插入事件
        if (!eventList.isEmpty()) {
            eventMapper.insertBatch(eventList);
        }
    }



    /**
     * 处理单个规则
     *
     * @param rule       规则信息
     * @param dataDOList 物联网数据列表
     * @param mappingMap 设备点位映射
     * @param eventList  事件列表
     */
    private void handleSingleRule(RuleDO rule, List<OdsIotDataDO> dataDOList,
                                  Map<String, FactoryDevicePointMappingDO> mappingMap, List<EventDO> eventList) {
        // 遍历物联网数据列表
        for (OdsIotDataDO row : dataDOList) {


            // 获取当前数据项对应设备点位的映射信息
            String key = row.getDeviceId() + "_" + row.getPointId();
            FactoryDevicePointMappingDO mapping = mappingMap.get(key);
            // 如果映射信息为空，则跳过当前数据项
            if (mapping == null) continue;


            //已经分组，point_code唯一
            //String indicatorId = mapping.getIndicatorId();
            String pointCode = mapping.getPointCode();
            String pointTypeCode = mapping.getPointType();  // 例如 "7" 表示 BOOLEAN

            Object valObj = tryParse(row.getValue(), pointTypeCode);


            // 如果转换结果为空，则跳过当前数据项
            if (valObj == null) continue;

            // 构建 Aviator 表达式上下文，变量名为 pointCode
            Map<String, Object> context = new HashMap<>();
            context.put(pointCode, valObj);

            boolean isAlarm = false;
            try {
                // 表达式示例： A > 10 或 B == true
                Object result = AviatorEvaluator.execute(rule.getExpression(), context);
                isAlarm = Boolean.TRUE.equals(result);
            } catch (Exception e) {
                log.error("单因子表达式执行异常：表达式={}, context={}, 异常={}",
                        rule.getExpression(), context, e.toString());
                continue;
            }

            if (isAlarm) {
                Double value = (valObj instanceof Boolean)
                        ? ((Boolean) valObj ? 1.0 : 0.0)
                        : (Double) valObj;
                List<EventDO> eventDOS = buildAlarmEvents(rule, value, row, mapping);
                //将事件列表添加到待插入列表中
                eventList.addAll(eventDOS);
            }
        }
    }


    /**
     * 处理多因子规则
     *
     * @param rule
     * @param dataDOList
     * @param pointIdMappingMap
     * @param eventList
     */

    private void handleCombinationRule(RuleDO rule,
                                       List<OdsIotDataDO> dataDOList,
                                       Map<String, FactoryDevicePointMappingDO> pointIdMappingMap,
                                       List<EventDO> eventList) {

        // 2. 将数据按时间点分组（组合判断通常基于同一时间点的多个因子）
        Map<LocalDateTime, List<OdsIotDataDO>> groupedByTs =
                dataDOList.stream().collect(Collectors.groupingBy(OdsIotDataDO::getBatchTime));

        // 遍历每一个时间点分组的数据（组合因子判断通常基于同一时间点多个点位值）
        for (Map.Entry<LocalDateTime, List<OdsIotDataDO>> entry : groupedByTs.entrySet()) {
            // Aviator 表达式上下文：指标编码 => 实际数值（Double）
            Map<String, Object> context = new HashMap<>();

            // 指标编码 => 原始数据对象，用于后续生成告警事件引用原始数据
            Map<String, OdsIotDataDO> pointCodeToData = new HashMap<>();

            // 遍历当前时间点的所有指标数据
            for (OdsIotDataDO row : entry.getValue()) {
                // 从映射表中获取当前指标的映射信息（通过 deviceId_pointId 构建 key）
                FactoryDevicePointMappingDO mapping = pointIdMappingMap.get(row.getDeviceId() + "_" + row.getPointId());
                if (mapping == null) continue; // 映射不存在，跳过该数据

                // 获取指标编码（作为表达式中的变量名）
                String pointCode = mapping.getPointCode();
                //获取指标类型
                String pointType = mapping.getPointType();

                //尝试将指标值转换为 Double 类型，如果失败则跳过
                Object value = tryParse(row.getValue(),pointType);
                if (value == null) continue;

                // 添加表达式变量上下文，用于 Aviator 表达式执行
                context.put(pointCode, value);

                // 记录该指标编码对应的原始数据
                pointCodeToData.put(pointCode, row);
            }

            try {
                // 编译并执行表达式，传入构造好的变量上下文
                Set<String> strings = AlarmExpressionEvaluator.evaluateAndFindTriggeredFactors(rule.getExpression(),
                        context);
                Object result = AviatorEvaluator.compile(rule.getExpression(), true).execute(context);

                // 如果表达式结果为 true，表示符合告警条件
                if (Boolean.TRUE.equals(result)) {
                    // 遍历当前组合规则中涉及的所有因子编码
                    //for (String pointCode : factorCodes) {
                    for(String pointCode : strings){
                        // 获取该指标编码对应的数据
                        OdsIotDataDO data = pointCodeToData.get(pointCode);
                        if (data == null) continue;

                        // 获取该指标在上下文中的数值
                        //TODO:boolean类型的数据在event中以1.0  表示true，0.0表示false，前端由factor_value和factor_type结合回显
                        //TODO:在告警设置中，点击新增告警规则的弹窗中，会发出请求monitor/ods/pointName?factoryId=获取水厂对应的所有指标名称，
                        // 返回值中含有PointCode、PointType字段（均字符串），目前在规则中比较运算符是写死的，仅针对数字的比较；需要增加校验：
                        //1.当PointType = "7"时，比较运算符下拉框中仅可选：=TRUE和=FALSE ；且阈值的填写栏会禁止填写
                        //2.当PointType != "7" 时, 运算符在原本基础上增加：不等于!=
                        //3.在点击确认发送新增告警的请求/monitor/alarm/rules/create时，先前pointType为TRUE或FALSE的会分别转换1.0或0.0再发送请求
                        //4.在组合因子判断弹窗中，要求满足以上1，2，3；并且必须当监测因子、比较运算符、阈值不为空才能点击添加条件；同理所有条件的的监测因子、比较运算符、阈值不为空，才能点击确认发送新增组合因子判断的请求
                        Object valObj = context.get(pointCode);
                        Double val = (valObj instanceof Boolean)
                                ? ((Boolean) valObj ? 1.0 : 0.0)
                                : (Double) valObj;

                        // 获取该数据的映射信息，用于构造告警事件
                        FactoryDevicePointMappingDO mapping = pointIdMappingMap.get(data.getDeviceId() + "_" + data.getPointId());
                        if (mapping == null) continue;



                        List<EventDO> eventDOS = buildAlarmEvents(rule, val, data, mapping);
                        //将事件列表添加到待插入列表中
                        eventList.addAll(eventDOS);
                    }
                }
            } catch (Exception e) {
                // 表达式执行异常时记录错误日志
                log.error("组合表达式计算异常: {}", rule.getExpression(), e);
            }
        }


    }




    private List<EventDO> buildAlarmEvents(RuleDO rule, Double value,
                                           OdsIotDataDO data, FactoryDevicePointMappingDO mapping) {
        List<EventDO> eventDOS = new ArrayList<>();
        List<RuleFactorDO> ruleFactorList = getRuleFactorList(rule.getId());
        ruleFactorList.forEach(ruleFactor -> {
            EventDO event = new EventDO();
            event.setRuleId(rule.getId());
            event.setRuleName(rule.getName());
            event.setAlarmTime(data.getBatchTime());
            event.setRuleType(rule.getRuleType());
            event.setFactoryId(mapping.getFactoryId());
            event.setFactoryName(mapping.getFactoryName());
            event.setDeviceId(mapping.getDeviceId());
            event.setDeviceName(mapping.getDeviceName());
            event.setFactorCode(mapping.getPointCode());
            event.setFactorName(mapping.getPointName());
            event.setFactorType(mapping.getPointType());
            event.setUnit(mapping.getUnit());
            event.setOperator(ruleFactor.getOperator());
            event.setFactorValue(value);
            event.setThreshold(ruleFactor.getThreshold());
            event.setOperator(ruleFactor.getOperator());
            event.setExpression(rule.getExpression());
            event.setLevel(rule.getLevel());
            event.setStatus("active");
            event.setNotifyStatus("pending");

            eventDOS.add( event);

            //后续是告警通知逻辑
            AlarmPushDTO dto = new AlarmPushDTO();
            dto.setEvent(event);
            //查询当前规则的告警级别
            LambdaQueryWrapper<NotifyConfigDO> lambdaQueryWrapper = new LambdaQueryWrapper<NotifyConfigDO>().eq(rule.getLevel() != null, NotifyConfigDO::getLevel,
                    rule.getLevel());
            NotifyConfigDO config = notifyConfigMapper.selectOne(lambdaQueryWrapper);
            //存的是角色类型的id数组
            //将字符串数组转成Long集合
            List<String> RoleStringIdList = Arrays.asList(config.getReceiverRoles().split(","));
            List<Long> roleIds = RoleStringIdList.stream().map(Long::parseLong).collect(Collectors.toList());
            dto.setReceiverRoleIds(roleIds);
            //将{"sms":true,"internal":true}格式转成list
            Map<String, Boolean> result = null;
            try {
                result = objectMapper.readValue(config.getNotifyMethods(), Map.class);
            } catch (JsonProcessingException e) {
                throw exception(NOTIFY_TYPE_CONVERT_ERROR);
            }
            dto.setSendMethods(result.entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList()));

            alarmNotifyProducer.send(dto);

            log.info("{}水厂的{}设备的{}指标触发告警事件", mapping.getFactoryName(), mapping.getDeviceName(), mapping.getPointName());
        });


        return eventDOS;
    }




    /**
     * 尝试将字符串值根据点位类型转换为对应的 Java 类型（用于 Aviator 表达式）
     *
     * @param rawValue      原始值字符串
     * @param pointTypeCode 点位类型的 code（如 "6" 表示 DOUBLE，"7" 表示 BOOLEAN）
     * @return 转换后的对象（可能为 Double、Boolean 或 null）
     */
    public static Object tryParse(String rawValue, String pointTypeCode) {
        if (StrUtil.isBlank(rawValue)) return null;

        PointTypeFlagEnum typeEnum = PointTypeFlagEnum.ofCode(pointTypeCode);
        if (typeEnum == null) return null;

        try {
            switch (typeEnum) {
                case BOOLEAN:
                    String lower = rawValue.trim().toLowerCase();
                    if ("true".equals(lower)) return Boolean.TRUE;
                    if ("false".equals(lower)) return Boolean.FALSE;
                    return null;

                case BYTE:
                    return Byte.parseByte(rawValue);
                case SHORT:
                    return Short.parseShort(rawValue);
                case INT:
                    return Integer.parseInt(rawValue);
                case LONG:
                    return Long.parseLong(rawValue);
                case FLOAT:
                    return Float.parseFloat(rawValue);
                case DOUBLE:
                    return Double.parseDouble(rawValue);

                // String直接赋值DOUBLE
                case STRING:
                    return Double.parseDouble(rawValue);

                default:
                    return null;
            }
        } catch (Exception e) {
            log.warn("无法解析点位值: value={}, typeCode={}", rawValue, pointTypeCode);
            return null;
        }
    }


}
