package cn.tl.cloud.module.monitor.service.assay.baseInfo;

import cn.tl.cloud.framework.common.exception.util.ServiceExceptionUtil;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssaySamplingPointPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssaySamplingPointSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssaySamplingPointDO;
import cn.tl.cloud.module.monitor.dal.mysql.assay.baseInfo.AssaySamplingPointMapper;
import cn.tl.cloud.module.monitor.enums.ErrorCodeConstants;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 采样点 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssaySamplingPointServiceImpl implements AssaySamplingPointService {

    @Resource
    private AssaySamplingPointMapper samplingPointMapper;

    @Override
    public Long createSamplingPoint(@Valid AssaySamplingPointSaveReqVO createReqVO) {
        // 校验代码唯一性
        validateCodeUnique(createReqVO.getFactoryId(), createReqVO.getCode(), null);
        
        // 校验类型枚举值
        validateType(createReqVO.getType());
        
        // 插入
        AssaySamplingPointDO samplingPoint = BeanUtils.toBean(createReqVO, AssaySamplingPointDO.class);
        samplingPointMapper.insert(samplingPoint);
        
        // 返回
        return samplingPoint.getId();
    }

    @Override
    public void updateSamplingPoint(@Valid AssaySamplingPointSaveReqVO updateReqVO) {
        // 校验存在
        validateSamplingPointExists(updateReqVO.getId(), updateReqVO.getFactoryId());
        
        // 校验代码唯一性
        validateCodeUnique(updateReqVO.getFactoryId(), updateReqVO.getCode(), updateReqVO.getId());
        
        // 校验类型枚举值
        validateType(updateReqVO.getType());
        
        // 更新
        AssaySamplingPointDO updateObj = BeanUtils.toBean(updateReqVO, AssaySamplingPointDO.class);
        samplingPointMapper.updateById(updateObj);
    }

    @Override
    public void deleteSamplingPoint(Long id, Long factoryId) {
        // 校验存在
        validateSamplingPointExists(id, factoryId);
        
        // TODO: 校验是否被采样计划引用
        
        // 删除
        samplingPointMapper.deleteById(id);
    }

    private void validateSamplingPointExists(Long id, Long factoryId) {
        AssaySamplingPointDO samplingPoint = samplingPointMapper.selectById(id);
        if (samplingPoint == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_POINT_NOT_EXISTS);
        }
        if (!samplingPoint.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FACTORY_DATA_ACCESS_DENIED);
        }
    }

    private void validateCodeUnique(Long factoryId, String code, Long id) {
        AssaySamplingPointDO samplingPoint = samplingPointMapper.selectByFactoryIdAndCode(factoryId, code);
        if (samplingPoint == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的记录
        if (id == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_POINT_CODE_DUPLICATE);
        }
        if (!samplingPoint.getId().equals(id)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_POINT_CODE_DUPLICATE);
        }
    }

    private void validateType(String type) {
        if (!"inlet".equals(type) && !"outlet".equals(type)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_POINT_TYPE_INVALID);
        }
    }

    @Override
    public AssaySamplingPointDO getSamplingPoint(Long id, Long factoryId) {
        AssaySamplingPointDO samplingPoint = samplingPointMapper.selectById(id);
        if (samplingPoint == null) {
            return null;
        }
        if (!samplingPoint.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FACTORY_DATA_ACCESS_DENIED);
        }
        return samplingPoint;
    }

    @Override
    public PageResult<AssaySamplingPointDO> getSamplingPointPage(AssaySamplingPointPageReqVO pageReqVO) {
        return samplingPointMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AssaySamplingPointDO> getSamplingPointList(AssaySamplingPointPageReqVO reqVO) {
        return samplingPointMapper.selectList(reqVO);
    }

    @Override
    public List<AssaySamplingPointDO> getSamplingPointListByType(Long factoryId, String type) {
        return samplingPointMapper.selectListByType(factoryId, type);
    }

    @Override
    public List<AssaySamplingPointDO> getSamplingPointSimpleList(Long factoryId) {
        return samplingPointMapper.selectSimpleList(factoryId);
    }

}
