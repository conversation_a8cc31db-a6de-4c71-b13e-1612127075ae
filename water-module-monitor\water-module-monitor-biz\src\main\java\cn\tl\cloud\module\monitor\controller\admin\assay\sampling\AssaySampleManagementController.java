package cn.tl.cloud.module.monitor.controller.admin.assay.sampling;

import cn.tl.cloud.framework.common.pojo.CommonResult;
import cn.tl.cloud.framework.common.pojo.PageParam;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.framework.excel.core.util.ExcelUtils;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySampleManagementPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySampleManagementRespVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySampleManagementSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySampleManagementDO;
import cn.tl.cloud.module.monitor.service.assay.sampling.AssaySampleManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static cn.tl.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 样品管理")
@RestController
@RequestMapping("/monitor/assay-sample-management")
@Validated
public class AssaySampleManagementController {

    @Resource
    private AssaySampleManagementService sampleManagementService;

    @PostMapping("/create")
    @Operation(summary = "创建样品管理记录")
    public CommonResult<Long> createSampleManagement(@Valid @RequestBody AssaySampleManagementSaveReqVO createReqVO) {
        return success(sampleManagementService.createSampleManagement(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新样品管理记录")
    public CommonResult<Boolean> updateSampleManagement(@Valid @RequestBody AssaySampleManagementSaveReqVO updateReqVO) {
        sampleManagementService.updateSampleManagement(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除样品管理记录")
    @Parameter(name = "id", description = "编号", required = true)
    @Parameter(name = "factoryId", description = "水厂ID", required = true)
    public CommonResult<Boolean> deleteSampleManagement(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        sampleManagementService.deleteSampleManagement(id, factoryId);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得样品管理记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    public CommonResult<AssaySampleManagementRespVO> getSampleManagement(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        AssaySampleManagementDO sampleManagement = sampleManagementService.getSampleManagement(id, factoryId);
        return success(BeanUtils.toBean(sampleManagement, AssaySampleManagementRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得样品管理分页")
    public CommonResult<PageResult<AssaySampleManagementRespVO>> getSampleManagementPage(@Valid AssaySampleManagementPageReqVO pageReqVO) {
        PageResult<AssaySampleManagementDO> pageResult = sampleManagementService.getSampleManagementPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssaySampleManagementRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得样品管理列表")
    public CommonResult<List<AssaySampleManagementRespVO>> getSampleManagementList(@Valid AssaySampleManagementPageReqVO reqVO) {
        List<AssaySampleManagementDO> list = sampleManagementService.getSampleManagementList(reqVO);
        return success(BeanUtils.toBean(list, AssaySampleManagementRespVO.class));
    }

    @PostMapping("/store-sample")
    @Operation(summary = "样品入库")
    public CommonResult<Map<String, Object>> storeSample(@RequestParam("executionId") Long executionId,
                                                        @RequestParam("factoryId") Long factoryId,
                                                        @RequestParam("operatorId") Long operatorId,
                                                        @RequestBody(required = false) Map<String, Object> sampleData) {
        Map<String, Object> result = sampleManagementService.storeSample(executionId, factoryId, operatorId, sampleData);
        return success(result);
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新样品状态")
    public CommonResult<Map<String, Object>> updateSampleStatus(@RequestParam("id") Long id,
                                                               @RequestParam("factoryId") Long factoryId,
                                                               @RequestParam("status") String status,
                                                               @RequestParam("operatorId") Long operatorId,
                                                               @RequestParam(value = "remark", required = false) String remark) {
        Map<String, Object> result = sampleManagementService.updateSampleStatus(id, factoryId, status, operatorId, remark);
        return success(result);
    }

    @PostMapping("/destroy-sample")
    @Operation(summary = "销毁样品")
    public CommonResult<Map<String, Object>> destroySample(@RequestParam("id") Long id,
                                                          @RequestParam("factoryId") Long factoryId,
                                                          @RequestParam("operatorId") Long operatorId,
                                                          @RequestParam("destroyReason") String destroyReason) {
        Map<String, Object> result = sampleManagementService.destroySample(id, factoryId, operatorId, destroyReason);
        return success(result);
    }

    @GetMapping("/detail")
    @Operation(summary = "获取样品详情")
    @Parameter(name = "id", description = "样品ID", required = true, example = "1")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    public CommonResult<Map<String, Object>> getSampleDetail(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        Map<String, Object> result = sampleManagementService.getSampleDetail(id, factoryId);
        return success(result);
    }

    @GetMapping("/expiring-samples")
    @Operation(summary = "获取即将过期的样品")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    @Parameter(name = "days", description = "天数", example = "7")
    public CommonResult<List<AssaySampleManagementRespVO>> getExpiringSamples(@RequestParam("factoryId") Long factoryId,
                                                                             @RequestParam(value = "days", defaultValue = "7") Integer days) {
        List<AssaySampleManagementDO> list = sampleManagementService.getExpiringSamples(factoryId, days);
        return success(BeanUtils.toBean(list, AssaySampleManagementRespVO.class));
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取样品状态统计")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    public CommonResult<Map<String, Object>> getSampleStatistics(@RequestParam("factoryId") Long factoryId) {
        Map<String, Object> result = sampleManagementService.getSampleStatistics(factoryId);
        return success(result);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出样品管理 Excel")
    public void exportSampleManagementExcel(@Valid AssaySampleManagementPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AssaySampleManagementDO> list = sampleManagementService.getSampleManagementList(pageReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "样品管理.xls", "数据", AssaySampleManagementRespVO.class,
                        BeanUtils.toBean(list, AssaySampleManagementRespVO.class));
    }

}
