package cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo;

import cn.tl.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 采样点 DO
 *
 * <AUTHOR>
 */
@TableName("assay_sampling_point")
@KeySequence("assay_sampling_point_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssaySamplingPointDO extends BaseDO {

    /**
     * 采样点ID
     */
    @TableId
    private Long id;
    
    /**
     * 水厂ID
     */
    private Long factoryId;
    
    /**
     * 采样点编码
     */
    private String code;
    
    /**
     * 采样点名称
     */
    private String name;
    
    /**
     * 类型(inlet-进水,outlet-出水)
     */
    private String type;
    
    /**
     * 位置描述
     */
    private String location;
    
    /**
     * 负责人ID
     */
    private Long managerId;
    
    /**
     * 是否启用
     */
    private Boolean isEnabled;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 扩展字段，存储额外的业务数据
     */
    private String extraField;

}
