package cn.tl.cloud.module.monitor.dal.dataobject.acqconf;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 *
 */
@Data
public class IotPoint {

    private Long id;

    private String pointCode;

    private String pointName;

    private String pointUnit;

    private String enableFlag;

    private String latestValue;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date latestTime;

    private String remark;

}
