package cn.tl.cloud.module.monitor.dal.dataobject.assay.testPlan;

import cn.tl.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 采样计划 DO
 *
 * <AUTHOR>
 */
@TableName("assay_sampling_plan")
@KeySequence("assay_sampling_plan_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssaySamplingPlanDO extends BaseDO {

    /**
     * 计划ID
     */
    @TableId
    private Long id;
    
    /**
     * 水厂ID
     */
    private Long factoryId;
    
    /**
     * 计划编号
     */
    private String planCode;
    
    /**
     * 计划名称
     */
    private String name;
    
    /**
     * 计划类型(regular-常规,temporary-临时)
     */
    private String type;
    
    /**
     * 计划描述
     */
    private String description;
    
    /**
     * 采样频率(仅常规计划)
     */
    private String frequency;
    
    /**
     * 开始日期(仅常规计划)
     */
    private LocalDate startDate;
    
    /**
     * 结束日期(仅常规计划)
     */
    private LocalDate endDate;
    
    /**
     * 计划时间(仅临时计划)
     */
    private LocalDateTime planDatetime;
    
    /**
     * 创建原因(仅临时计划)
     */
    private String reason;
    
    /**
     * 优先级(normal-普通,high-高,urgent-紧急)
     */
    private String priority;
    
    /**
     * 检测项目ID
     */
    private Long testItem;
    
    /**
     * 采样点ID
     */
    private Long samplingPoint;
    
    /**
     * 采样人员ID
     */
    private Long samplerId;
    
    /**
     * 检测人员ID
     */
    private Long testerId;
    
    /**
     * 审核人员ID
     */
    private Long reviewerId;
    
    /**
     * 预期样品量(mL)
     */
    private Integer expectedSampleQuantity;
    
    /**
     * 预期样品性质
     */
    private String expectedSampleNature;
    
    /**
     * 预期样品外观
     */
    private String expectedSampleAppearance;
    
    /**
     * 预期上清液情况
     */
    private String expectedSupernatant;
    
    /**
     * 采样说明
     */
    private String samplingInstructions;
    
    /**
     * 是否启用
     */
    private Boolean isEnabled;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 扩展字段，存储额外的业务数据
     */
    private String extraField;

}
