package cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 采样计划新增/修改 Request VO")
@Data
public class AssaySamplingPlanSaveReqVO {

    @Schema(description = "计划ID", example = "1")
    private Long id;

    @Schema(description = "水厂ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "水厂ID不能为空")
    private Long factoryId;

    @Schema(description = "计划编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "PLAN-2024-001")
    @NotBlank(message = "计划编号不能为空")
    @Size(max = 50, message = "计划编号长度不能超过50个字符")
    private String planCode;

    @Schema(description = "计划名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "进水水质日常监测计划")
    @NotBlank(message = "计划名称不能为空")
    @Size(max = 200, message = "计划名称长度不能超过200个字符")
    private String name;

    @Schema(description = "计划类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "regular")
    @NotBlank(message = "计划类型不能为空")
    @Pattern(regexp = "^(regular|temporary)$", message = "计划类型只能是regular或temporary")
    private String type;

    @Schema(description = "计划描述", example = "对进水口水质进行日常监测")
    @Size(max = 2000, message = "计划描述长度不能超过2000个字符")
    private String description;

    @Schema(description = "采样频率(仅常规计划)", example = "daily")
    @Pattern(regexp = "^(daily|weekly|monthly|quarterly)$", message = "采样频率只能是daily、weekly、monthly或quarterly")
    private String frequency;

    @Schema(description = "开始日期(仅常规计划)", example = "2024-01-01")
    private LocalDate startDate;

    @Schema(description = "结束日期(仅常规计划)", example = "2024-12-31")
    private LocalDate endDate;

    @Schema(description = "计划时间(仅临时计划)", example = "2024-01-15T08:00:00")
    private LocalDateTime planDatetime;

    @Schema(description = "创建原因(仅临时计划)", example = "接到环保部门通知")
    @Size(max = 1000, message = "创建原因长度不能超过1000个字符")
    private String reason;

    @Schema(description = "优先级", requiredMode = Schema.RequiredMode.REQUIRED, example = "normal")
    @NotBlank(message = "优先级不能为空")
    @Pattern(regexp = "^(normal|high|urgent)$", message = "优先级只能是normal、high或urgent")
    private String priority;

    @Schema(description = "检测项目ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "检测项目ID不能为空")
    private Long testItem;

    @Schema(description = "采样点ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "采样点ID不能为空")
    private Long samplingPoint;

    @Schema(description = "采样人员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    @NotNull(message = "采样人员ID不能为空")
    private Long samplerId;

    @Schema(description = "检测人员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1002")
    @NotNull(message = "检测人员ID不能为空")
    private Long testerId;

    @Schema(description = "审核人员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1003")
    @NotNull(message = "审核人员ID不能为空")
    private Long reviewerId;

    @Schema(description = "预期样品量(mL)", example = "100")
    @Min(value = 1, message = "预期样品量必须大于0")
    @Max(value = 10000, message = "预期样品量不能超过10000mL")
    private Integer expectedSampleQuantity;

    @Schema(description = "预期样品性质", example = "liquid")
    @Pattern(regexp = "^(liquid|solid|semi-solid|gas)$", message = "预期样品性质只能是liquid、solid、semi-solid或gas")
    private String expectedSampleNature;

    @Schema(description = "预期样品外观", example = "微黄色，略浑浊")
    @Size(max = 500, message = "预期样品外观长度不能超过500个字符")
    private String expectedSampleAppearance;

    @Schema(description = "预期上清液情况", example = "清澈")
    @Size(max = 200, message = "预期上清液情况长度不能超过200个字符")
    private String expectedSupernatant;

    @Schema(description = "采样说明", example = "采样前需要冲洗采样瓶3次")
    @Size(max = 2000, message = "采样说明长度不能超过2000个字符")
    private String samplingInstructions;

    @Schema(description = "启用状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "启用状态不能为空")
    private Boolean isEnabled;

    @Schema(description = "备注", example = "重要的日常监测计划")
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String remark;

}
