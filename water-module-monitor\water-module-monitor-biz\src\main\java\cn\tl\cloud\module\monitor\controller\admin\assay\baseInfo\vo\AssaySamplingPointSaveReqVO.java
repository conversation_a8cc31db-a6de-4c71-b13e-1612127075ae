package cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Schema(description = "管理后台 - 采样点新增/修改 Request VO")
@Data
public class AssaySamplingPointSaveReqVO {

    @Schema(description = "采样点ID", example = "1")
    private Long id;

    @Schema(description = "水厂ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "水厂ID不能为空")
    private Long factoryId;

    @Schema(description = "采样点编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "SP001")
    @NotBlank(message = "采样点编码不能为空")
    @Size(max = 50, message = "采样点编码长度不能超过50个字符")
    private String code;

    @Schema(description = "采样点名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "进水口1号")
    @NotBlank(message = "采样点名称不能为空")
    @Size(max = 100, message = "采样点名称长度不能超过100个字符")
    private String name;

    @Schema(description = "采样点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "inlet")
    @NotBlank(message = "采样点类型不能为空")
    @Pattern(regexp = "^(inlet|outlet)$", message = "采样点类型只能是inlet或outlet")
    private String type;

    @Schema(description = "位置描述", example = "厂区东南角进水泵房")
    @Size(max = 200, message = "位置描述长度不能超过200个字符")
    private String location;

    @Schema(description = "管理人ID", example = "1001")
    private Long managerId;

    @Schema(description = "启用状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "启用状态不能为空")
    private Boolean isEnabled;

    @Schema(description = "备注", example = "主要进水口")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

}
