package cn.tl.cloud.module.monitor.service.assay.testPlan;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo.AssayTaskAssignReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo.AssayTaskGenerateReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo.AssaySamplingTaskPageReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.testPlan.AssaySamplingTaskDO;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 采样任务 Service 接口
 *
 * <AUTHOR>
 */
public interface AssaySamplingTaskService {

    /**
     * 根据计划生成任务
     *
     * @param generateReqVO 生成信息
     * @return 生成结果
     */
    Map<String, Object> generateTasksFromPlan(@Valid AssayTaskGenerateReqVO generateReqVO);

    /**
     * 分配任务
     *
     * @param assignReqVO 分配信息
     * @return 分配结果
     */
    Map<String, Object> assignTask(@Valid AssayTaskAssignReqVO assignReqVO);

    /**
     * 获得采样任务
     *
     * @param id 编号
     * @param factoryId 水厂ID
     * @return 采样任务
     */
    AssaySamplingTaskDO getSamplingTask(Long id, Long factoryId);

    /**
     * 获得采样任务分页
     *
     * @param pageReqVO 分页查询
     * @return 采样任务分页
     */
    PageResult<AssaySamplingTaskDO> getSamplingTaskPage(AssaySamplingTaskPageReqVO pageReqVO);

    /**
     * 获得采样任务列表
     *
     * @param reqVO 查询条件
     * @return 采样任务列表
     */
    List<AssaySamplingTaskDO> getSamplingTaskList(AssaySamplingTaskPageReqVO reqVO);

    /**
     * 获取任务详情（包含关联信息）
     *
     * @param id 任务ID
     * @param factoryId 水厂ID
     * @return 任务详情
     */
    Map<String, Object> getTaskDetail(Long id, Long factoryId);

    /**
     * 更新任务状态（供其他模块调用）
     *
     * @param taskId 任务ID
     * @param status 新状态
     * @param remark 备注
     * @return 是否成功
     */
    boolean updateTaskStatus(Long taskId, String status, String remark);

    /**
     * 批量更新任务状态（供其他模块调用）
     *
     * @param taskIds 任务ID列表
     * @param status 新状态
     * @param remark 备注
     * @return 成功更新的数量
     */
    int batchUpdateTaskStatus(List<Long> taskIds, String status, String remark);

    /**
     * 根据计划ID获取任务列表
     *
     * @param planId 计划ID
     * @return 任务列表
     */
    List<AssaySamplingTaskDO> getTasksByPlanId(Long planId);

    /**
     * 根据状态获取任务列表
     *
     * @param factoryId 水厂ID
     * @param status 状态
     * @return 任务列表
     */
    List<AssaySamplingTaskDO> getTasksByStatus(Long factoryId, String status);

}
