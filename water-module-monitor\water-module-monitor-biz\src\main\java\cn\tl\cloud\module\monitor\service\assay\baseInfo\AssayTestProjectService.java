package cn.tl.cloud.module.monitor.service.assay.baseInfo;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestProjectPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestProjectSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestProjectDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 检测项目 Service 接口
 *
 * <AUTHOR>
 */
public interface AssayTestProjectService {

    /**
     * 创建检测项目
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTestProject(@Valid AssayTestProjectSaveReqVO createReqVO);

    /**
     * 更新检测项目
     *
     * @param updateReqVO 更新信息
     */
    void updateTestProject(@Valid AssayTestProjectSaveReqVO updateReqVO);

    /**
     * 删除检测项目
     *
     * @param id 编号
     * @param factoryId 水厂ID
     */
    void deleteTestProject(Long id, Long factoryId);

    /**
     * 获得检测项目
     *
     * @param id 编号
     * @param factoryId 水厂ID
     * @return 检测项目
     */
    AssayTestProjectDO getTestProject(Long id, Long factoryId);

    /**
     * 获得检测项目分页
     *
     * @param pageReqVO 分页查询
     * @return 检测项目分页
     */
    PageResult<AssayTestProjectDO> getTestProjectPage(AssayTestProjectPageReqVO pageReqVO);

    /**
     * 获得检测项目列表
     *
     * @param reqVO 查询条件
     * @return 检测项目列表
     */
    List<AssayTestProjectDO> getTestProjectList(AssayTestProjectPageReqVO reqVO);

    /**
     * 根据类型获取检测项目列表
     *
     * @param factoryId 水厂ID
     * @param categoryId 类型ID
     * @return 检测项目列表
     */
    List<AssayTestProjectDO> getTestProjectListByCategoryId(Long factoryId, Long categoryId);

}
