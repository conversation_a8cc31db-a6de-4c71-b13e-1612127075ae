package cn.tl.cloud.module.monitor.service.assay.sampling;

import cn.tl.cloud.framework.common.exception.util.ServiceExceptionUtil;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySamplingExecutionPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySamplingExecutionSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySamplingExecutionDO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySubmissionRecordDO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.testPlan.AssaySamplingTaskDO;
import cn.tl.cloud.module.monitor.dal.mysql.assay.sampling.AssaySamplingExecutionMapper;
import cn.tl.cloud.module.monitor.dal.mysql.assay.sampling.AssaySubmissionRecordMapper;
import cn.tl.cloud.module.monitor.dal.mysql.assay.testPlan.AssaySamplingTaskMapper;
import cn.tl.cloud.module.monitor.enums.ErrorCodeConstants;
import cn.tl.cloud.module.monitor.service.assay.common.AssayOperationLogService;
import cn.tl.cloud.module.monitor.service.assay.testPlan.AssaySamplingTaskService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 采样执行 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssaySamplingExecutionServiceImpl implements AssaySamplingExecutionService {

    @Resource
    private AssaySamplingExecutionMapper samplingExecutionMapper;
    
    @Resource
    private AssaySamplingTaskMapper samplingTaskMapper;
    
    @Resource
    private AssaySubmissionRecordMapper submissionRecordMapper;
    
    @Resource
    private AssayOperationLogService operationLogService;
    
    @Resource
    private AssaySamplingTaskService samplingTaskService;
    @Resource
    private ObjectMapper objectMapper;

    @Override
    public Long createSamplingExecution(@Valid AssaySamplingExecutionSaveReqVO createReqVO) {
        // 校验任务存在
        validateTaskExists(createReqVO.getTaskId(), createReqVO.getFactoryId());
        
        // 校验该任务是否已存在执行记录
        AssaySamplingExecutionDO existingExecution = samplingExecutionMapper
                .selectByFactoryIdAndTaskId(createReqVO.getFactoryId(), createReqVO.getTaskId());
        if (existingExecution != null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_EXECUTION_ALREADY_EXISTS);
        }
        
        // 插入
        AssaySamplingExecutionDO samplingExecution = BeanUtils.toBean(createReqVO, AssaySamplingExecutionDO.class);
        samplingExecution.setStatus("pending"); // 默认状态为待采样
        samplingExecutionMapper.insert(samplingExecution);
        
        // 记录操作日志
        try {
            operationLogService.recordLog(createReqVO.getFactoryId(), "assay_sampling_execution",
                    samplingExecution.getId(), "create", "创建采样执行记录",
                    null, null, objectMapper.writeValueAsString(samplingExecution));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        return samplingExecution.getId();
    }

    @Override
    public void updateSamplingExecution(@Valid AssaySamplingExecutionSaveReqVO updateReqVO) {
        // 校验存在
        AssaySamplingExecutionDO oldExecution = validateSamplingExecutionExists(updateReqVO.getId(), updateReqVO.getFactoryId());
        
        // 校验任务存在
        validateTaskExists(updateReqVO.getTaskId(), updateReqVO.getFactoryId());
        
        // 更新
        AssaySamplingExecutionDO updateObj = BeanUtils.toBean(updateReqVO, AssaySamplingExecutionDO.class);
        samplingExecutionMapper.updateById(updateObj);
        
        // 记录操作日志
        AssaySamplingExecutionDO newExecution = samplingExecutionMapper.selectById(updateReqVO.getId());
        try {
            operationLogService.recordLog(updateReqVO.getFactoryId(), "assay_sampling_execution",
                    updateReqVO.getId(), "update", "更新采样执行记录",
                    null, objectMapper.writeValueAsString(oldExecution), objectMapper.writeValueAsString(newExecution));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void deleteSamplingExecution(Long id, Long factoryId) {
        // 校验存在
        AssaySamplingExecutionDO execution = validateSamplingExecutionExists(id, factoryId);
        
        // 删除
        samplingExecutionMapper.deleteById(id);
        
        // 记录操作日志
        try {
            operationLogService.recordLog(factoryId, "assay_sampling_execution",
                    id, "delete", "删除采样执行记录",
                    null, objectMapper.writeValueAsString(execution), null);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> confirmSampling(Long id, Long factoryId, Long operatorId) {
        // 校验执行记录存在且状态为pending
        AssaySamplingExecutionDO execution = validateSamplingExecutionExists(id, factoryId);
        if (!"pending".equals(execution.getStatus())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_EXECUTION_STATUS_ERROR);
        }
        
        String oldStatus = execution.getStatus();
        
        // 更新执行状态为processing
        AssaySamplingExecutionDO updateObj = new AssaySamplingExecutionDO();
        updateObj.setId(id);
        updateObj.setStatus("processing");
        updateObj.setActualSamplingTime(LocalDateTime.now());
        samplingExecutionMapper.updateById(updateObj);
        
        // 记录状态变更日志
        operationLogService.recordStatusChange(factoryId, "assay_sampling_execution", 
                id, oldStatus, "processing", operatorId, "确认开始采样");
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("executionId", id);
        result.put("status", "processing");
        result.put("startTime", LocalDateTime.now());
        result.put("message", "采样确认成功，开始执行");
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> completeSampling(Long id, Long factoryId, Long operatorId, Map<String, Object> executionData) {
        // 校验执行记录存在且状态为processing
        AssaySamplingExecutionDO execution = validateSamplingExecutionExists(id, factoryId);
        if (!"processing".equals(execution.getStatus())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_EXECUTION_STATUS_ERROR);
        }
        
        String oldStatus = execution.getStatus();
        
        // 更新执行记录
        AssaySamplingExecutionDO updateObj = new AssaySamplingExecutionDO();
        updateObj.setId(id);
        updateObj.setStatus("completed");
        updateObj.setCompleteTime(LocalDateTime.now());
        
        // 设置执行数据
        if (executionData != null) {
            if (executionData.containsKey("samplingLocation")) {
                updateObj.setSamplingLocation((String) executionData.get("samplingLocation"));
            }
            if (executionData.containsKey("samplingCondition")) {
                updateObj.setSamplingCondition((String) executionData.get("samplingCondition"));
            }
            if (executionData.containsKey("actualSampleQuantity")) {
                updateObj.setActualSampleQuantity((Integer) executionData.get("actualSampleQuantity"));
            }
            if (executionData.containsKey("actualSampleAppearance")) {
                updateObj.setActualSampleAppearance((String) executionData.get("actualSampleAppearance"));
            }
            if (executionData.containsKey("sampleStatus")) {
                updateObj.setSampleStatus((String) executionData.get("sampleStatus"));
            }
            if (executionData.containsKey("remark")) {
                updateObj.setRemark((String) executionData.get("remark"));
            }
        }
        
        samplingExecutionMapper.updateById(updateObj);
        
        // 1. 更新关联任务状态：sampling -> submitted
        AssaySamplingTaskDO task = samplingTaskMapper.selectById(execution.getTaskId());
        if (task != null) {
            samplingTaskService.updateTaskStatus(task.getId(), "submitted", "采样完成，等待送检");
            
            // 记录任务状态变更日志
            operationLogService.recordStatusChange(factoryId, "assay_sampling_task", 
                    task.getId(), "sampling", "submitted", operatorId, "采样完成，等待送检");
        }
        
        // 2. 创建送检记录（状态为unSubmitted）
        AssaySubmissionRecordDO submissionRecord = new AssaySubmissionRecordDO();
        submissionRecord.setFactoryId(factoryId);
        submissionRecord.setRecordCode(generateSubmissionRecordCode(factoryId, execution.getTaskId()));
        submissionRecord.setExecutionId(id);
        submissionRecord.setSubmissionDate(LocalDate.now());
        submissionRecord.setSubmissionPersonId(operatorId);
        submissionRecord.setTestItem(task != null ? task.getTestItem() : null);
        submissionRecord.setStatus("unSubmitted");
        submissionRecord.setRemark("采样完成自动生成");
        submissionRecordMapper.insert(submissionRecord);
        
        // 记录送检记录创建日志
        try {
            operationLogService.recordLog(factoryId, "assay_submission_record",
                    submissionRecord.getId(), "create", "采样完成自动创建送检记录",
                    operatorId, null, objectMapper.writeValueAsString(submissionRecord));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        // 3. 记录执行状态变更日志
        operationLogService.recordStatusChange(factoryId, "assay_sampling_execution", 
                id, oldStatus, "completed", operatorId, "完成采样");
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("executionId", id);
        result.put("status", "completed");
        result.put("completeTime", LocalDateTime.now());
        result.put("submissionRecordId", submissionRecord.getId());
        result.put("message", "采样完成，已自动创建送检记录");
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> recordAbnormal(Long id, Long factoryId, Long operatorId, 
                                             String abnormalReason, String handleMeasures) {
        // 校验执行记录存在
        AssaySamplingExecutionDO execution = validateSamplingExecutionExists(id, factoryId);
        
        String oldStatus = execution.getStatus();
        
        // 更新执行记录
        AssaySamplingExecutionDO updateObj = new AssaySamplingExecutionDO();
        updateObj.setId(id);
        updateObj.setStatus("abnormal");
        updateObj.setSampleStatus("abnormal");
        updateObj.setAbnormalReason(abnormalReason);
        updateObj.setHandleMeasures(handleMeasures);
        updateObj.setCompleteTime(LocalDateTime.now());
        samplingExecutionMapper.updateById(updateObj);
        
        // 记录状态变更日志
        operationLogService.recordStatusChange(factoryId, "assay_sampling_execution", 
                id, oldStatus, "abnormal", operatorId, "记录异常：" + abnormalReason);
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("executionId", id);
        result.put("status", "abnormal");
        result.put("abnormalReason", abnormalReason);
        result.put("handleMeasures", handleMeasures);
        result.put("message", "异常记录成功");
        
        return result;
    }

    private AssaySamplingExecutionDO validateSamplingExecutionExists(Long id, Long factoryId) {
        AssaySamplingExecutionDO execution = samplingExecutionMapper.selectById(id);
        if (execution == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_EXECUTION_NOT_EXISTS);
        }
        if (!execution.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FACTORY_DATA_ACCESS_DENIED);
        }
        return execution;
    }

    private void validateTaskExists(Long taskId, Long factoryId) {
        AssaySamplingTaskDO task = samplingTaskMapper.selectById(taskId);
        if (task == null || !task.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_EXECUTION_TASK_NOT_EXISTS);
        }
    }

    private String generateSubmissionRecordCode(Long factoryId, Long taskId) {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return String.format("SUB-%d-%s-%03d", factoryId, dateStr, 
            (int)(System.currentTimeMillis() % 1000));
    }

    @Override
    public AssaySamplingExecutionDO getSamplingExecution(Long id, Long factoryId) {
        return validateSamplingExecutionExists(id, factoryId);
    }

    @Override
    public PageResult<AssaySamplingExecutionDO> getSamplingExecutionPage(AssaySamplingExecutionPageReqVO pageReqVO) {
        return samplingExecutionMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AssaySamplingExecutionDO> getSamplingExecutionList(AssaySamplingExecutionPageReqVO reqVO) {
        return samplingExecutionMapper.selectList(reqVO);
    }

    @Override
    public Map<String, Object> getExecutionDetail(Long id, Long factoryId) {
        // 校验执行记录存在
        AssaySamplingExecutionDO execution = validateSamplingExecutionExists(id, factoryId);
        
        // TODO: 查询详细信息（包含任务信息、状态历史等）
        Map<String, Object> result = new HashMap<>();
        result.put("executionInfo", execution);
        result.put("statusHistory", operationLogService.getStatusChangeHistory(factoryId, "assay_sampling_execution", id));
        
        return result;
    }

    @Override
    public AssaySamplingExecutionDO getExecutionByTaskId(Long taskId, Long factoryId) {
        return samplingExecutionMapper.selectByFactoryIdAndTaskId(factoryId, taskId);
    }

    @Override
    public Map<String, Object> getExecutionStatistics(Long factoryId) {
        // TODO: 实现执行状态统计
        Map<String, Object> result = new HashMap<>();
        result.put("pendingCount", samplingExecutionMapper.selectCountByStatus(factoryId, "pending"));
        result.put("processingCount", samplingExecutionMapper.selectCountByStatus(factoryId, "processing"));
        result.put("completedCount", samplingExecutionMapper.selectCountByStatus(factoryId, "completed"));
        result.put("abnormalCount", samplingExecutionMapper.selectCountByStatus(factoryId, "abnormal"));
        
        return result;
    }

}
