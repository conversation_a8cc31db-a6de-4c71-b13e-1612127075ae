package cn.tl.cloud.module.monitor.service.monitor.alarm.event;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.event.vo.EventPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.event.vo.EventRespVO;
import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.event.vo.EventSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.monitor.alarm.event.EventDO;


import javax.validation.Valid;
import java.util.List;

/**
 * 告警事件 Service 接口
 *
 * <AUTHOR>
 */
public interface EventService {

    /**
     * 创建告警事件
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEvent(@Valid EventSaveReqVO createReqVO);

    /**
     * 更新告警事件
     *
     * @param updateReqVO 更新信息
     */
    void updateEvent(@Valid EventSaveReqVO updateReqVO);

    /**
     * 删除告警事件
     *
     * @param id 编号
     */
    void deleteEvent(Long id);

    /**
     * 获得告警事件
     *
     * @param id 编号
     * @return 告警事件
     */
    EventDO getEvent(Long id);

    /**
     * 获得告警事件分页
     *
     * @param pageReqVO 分页查询
     * @return 告警事件分页
     */
    PageResult<EventDO> getEventPage(EventPageReqVO pageReqVO);

    List<EventRespVO> getEventList(EventPageReqVO reqVO);
}
