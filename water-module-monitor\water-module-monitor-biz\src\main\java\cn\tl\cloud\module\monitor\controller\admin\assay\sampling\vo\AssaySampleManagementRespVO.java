package cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 样品管理 Response VO")
@Data
public class AssaySampleManagementRespVO {

    @Schema(description = "样品ID", example = "1")
    private Long id;

    @Schema(description = "水厂ID", example = "1")
    private Long factoryId;

    @Schema(description = "样品编号", example = "SAMPLE-001")
    private String sampleCode;

    @Schema(description = "关联采样执行ID", example = "1")
    private Long executionId;

    @Schema(description = "样品类型", example = "water")
    private String sampleType;

    @Schema(description = "样品体积(mL)", example = "100")
    private Integer volume;

    @Schema(description = "样品外观", example = "微黄色，略浑浊")
    private String appearance;

    @Schema(description = "保存方法", example = "refrigerated")
    private String preservationMethod;

    @Schema(description = "存储位置", example = "冷藏室A区")
    private String storageLocation;

    @Schema(description = "存储温度(℃)", example = "4")
    private Integer storageTemperature;

    @Schema(description = "有效期", example = "2024-01-22")
    private LocalDate expiryDate;

    @Schema(description = "检测项目ID", example = "1")
    private Long testItem;

    @Schema(description = "检测项目名称", example = "COD")
    private String testItemName;

    @Schema(description = "采样人员ID", example = "1001")
    private Long samplingPersonId;

    @Schema(description = "采样人员姓名", example = "张三")
    private String samplingPersonName;

    @Schema(description = "采样日期", example = "2024-01-15")
    private LocalDate samplingDate;

    @Schema(description = "样品状态", example = "stored")
    private String status;

    @Schema(description = "销毁原因", example = "超过有效期")
    private String destroyReason;

    @Schema(description = "销毁时间", example = "2024-01-30T10:00:00")
    private LocalDateTime destroyTime;

    @Schema(description = "销毁操作人ID", example = "1002")
    private Long destroyOperatorId;

    @Schema(description = "销毁操作人姓名", example = "李四")
    private String destroyOperatorName;

    @Schema(description = "备注", example = "正常样品")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
