package cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo;

import cn.tl.cloud.framework.common.pojo.CommonResult;
import cn.tl.cloud.framework.common.pojo.PageParam;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.framework.excel.core.util.ExcelUtils;

import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestIndicatorPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestIndicatorRespVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestIndicatorSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestIndicatorDO;
import cn.tl.cloud.module.monitor.service.assay.baseInfo.AssayTestIndicatorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static cn.tl.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 检测指标")
@RestController
@RequestMapping("/monitor/assay-test-indicator")
@Validated
public class AssayTestIndicatorController {

    @Resource
    private AssayTestIndicatorService testIndicatorService;

    @PostMapping("/create")
    @Operation(summary = "创建检测指标")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-indicator:create')")
    public CommonResult<Long> createTestIndicator(@Valid @RequestBody AssayTestIndicatorSaveReqVO createReqVO) {
        return success(testIndicatorService.createTestIndicator(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新检测指标")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-indicator:update')")
    public CommonResult<Boolean> updateTestIndicator(@Valid @RequestBody AssayTestIndicatorSaveReqVO updateReqVO) {
        testIndicatorService.updateTestIndicator(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除检测指标")
    @Parameter(name = "id", description = "编号", required = true)
    @Parameter(name = "factoryId", description = "水厂ID", required = true)
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-indicator:delete')")
    public CommonResult<Boolean> deleteTestIndicator(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        testIndicatorService.deleteTestIndicator(id, factoryId);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得检测指标")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-indicator:query')")
    public CommonResult<AssayTestIndicatorRespVO> getTestIndicator(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        AssayTestIndicatorDO testIndicator = testIndicatorService.getTestIndicator(id, factoryId);
        return success(BeanUtils.toBean(testIndicator, AssayTestIndicatorRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得检测指标分页")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-indicator:query')")
    public CommonResult<PageResult<AssayTestIndicatorRespVO>> getTestIndicatorPage(@Valid AssayTestIndicatorPageReqVO pageReqVO) {
        PageResult<AssayTestIndicatorDO> pageResult = testIndicatorService.getTestIndicatorPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssayTestIndicatorRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得检测指标列表")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-indicator:query')")
    public CommonResult<List<AssayTestIndicatorRespVO>> getTestIndicatorList(@Valid AssayTestIndicatorPageReqVO reqVO) {
        List<AssayTestIndicatorDO> list = testIndicatorService.getTestIndicatorList(reqVO);
        return success(BeanUtils.toBean(list, AssayTestIndicatorRespVO.class));
    }

    @GetMapping("/list-by-project")
    @Operation(summary = "根据项目获取检测指标列表")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    @Parameter(name = "projectId", description = "项目ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-indicator:query')")
    public CommonResult<List<AssayTestIndicatorRespVO>> getTestIndicatorListByProjectId(@RequestParam("factoryId") Long factoryId, 
                                                                                        @RequestParam("projectId") Long projectId) {
        List<AssayTestIndicatorDO> list = testIndicatorService.getTestIndicatorListByProjectId(factoryId, projectId);
        return success(BeanUtils.toBean(list, AssayTestIndicatorRespVO.class));
    }

    @GetMapping("/tree")
    @Operation(summary = "获取检测项目树形结构")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-indicator:query')")
    public CommonResult<PageResult<Map<String, Object>>> getTestIndicatorTree(@Valid AssayTestIndicatorPageReqVO pageReqVO) {
        PageResult<Map<String, Object>> pageResult = testIndicatorService.getTestIndicatorTree(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出检测指标 Excel")
    @PreAuthorize("@ss.hasPermission('monitor:assay-test-indicator:export')")

    public void exportTestIndicatorExcel(@Valid AssayTestIndicatorPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AssayTestIndicatorDO> list = testIndicatorService.getTestIndicatorList(pageReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "检测指标.xls", "数据", AssayTestIndicatorRespVO.class,
                        BeanUtils.toBean(list, AssayTestIndicatorRespVO.class));
    }

}
