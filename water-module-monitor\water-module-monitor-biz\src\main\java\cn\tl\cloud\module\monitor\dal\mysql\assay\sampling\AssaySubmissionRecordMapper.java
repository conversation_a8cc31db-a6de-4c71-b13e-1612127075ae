package cn.tl.cloud.module.monitor.dal.mysql.assay.sampling;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tl.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySubmissionRecordPageReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySubmissionRecordDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.util.List;

/**
 * 送检记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssaySubmissionRecordMapper extends BaseMapperX<AssaySubmissionRecordDO> {

    default PageResult<AssaySubmissionRecordDO> selectPage(AssaySubmissionRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AssaySubmissionRecordDO>()
                .eqIfPresent(AssaySubmissionRecordDO::getFactoryId, reqVO.getFactoryId())
                .likeIfPresent(AssaySubmissionRecordDO::getRecordCode, reqVO.getRecordCode())
                .eqIfPresent(AssaySubmissionRecordDO::getExecutionId, reqVO.getExecutionId())
                .eqIfPresent(AssaySubmissionRecordDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AssaySubmissionRecordDO::getSubmissionPersonId, reqVO.getSubmissionPersonId())
                .betweenIfPresent(AssaySubmissionRecordDO::getSubmissionDate, reqVO.getStartDate(), reqVO.getEndDate())
                .orderByDesc(AssaySubmissionRecordDO::getId));
    }

    default List<AssaySubmissionRecordDO> selectList(AssaySubmissionRecordPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AssaySubmissionRecordDO>()
                .eqIfPresent(AssaySubmissionRecordDO::getFactoryId, reqVO.getFactoryId())
                .likeIfPresent(AssaySubmissionRecordDO::getRecordCode, reqVO.getRecordCode())
                .eqIfPresent(AssaySubmissionRecordDO::getExecutionId, reqVO.getExecutionId())
                .eqIfPresent(AssaySubmissionRecordDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AssaySubmissionRecordDO::getSubmissionPersonId, reqVO.getSubmissionPersonId())
                .betweenIfPresent(AssaySubmissionRecordDO::getSubmissionDate, reqVO.getStartDate(), reqVO.getEndDate())
                .orderByDesc(AssaySubmissionRecordDO::getId));
    }

    default AssaySubmissionRecordDO selectByFactoryIdAndRecordCode(Long factoryId, String recordCode) {
        return selectOne(new LambdaQueryWrapperX<AssaySubmissionRecordDO>()
                .eq(AssaySubmissionRecordDO::getFactoryId, factoryId)
                .eq(AssaySubmissionRecordDO::getRecordCode, recordCode));
    }

    default AssaySubmissionRecordDO selectByExecutionId(Long executionId) {
        return selectOne(new LambdaQueryWrapperX<AssaySubmissionRecordDO>()
                .eq(AssaySubmissionRecordDO::getExecutionId, executionId));
    }

    default List<AssaySubmissionRecordDO> selectByStatus(Long factoryId, String status) {
        return selectList(new LambdaQueryWrapperX<AssaySubmissionRecordDO>()
                .eq(AssaySubmissionRecordDO::getFactoryId, factoryId)
                .eq(AssaySubmissionRecordDO::getStatus, status)
                .orderByDesc(AssaySubmissionRecordDO::getId));
    }

    default List<AssaySubmissionRecordDO> selectByDateRange(Long factoryId, LocalDate startDate, LocalDate endDate) {
        return selectList(new LambdaQueryWrapperX<AssaySubmissionRecordDO>()
                .eq(AssaySubmissionRecordDO::getFactoryId, factoryId)
                .between(AssaySubmissionRecordDO::getSubmissionDate, startDate, endDate)
                .orderByDesc(AssaySubmissionRecordDO::getSubmissionDate));
    }

    default Long selectCountByStatus(Long factoryId, String status) {
        return selectCount(new LambdaQueryWrapperX<AssaySubmissionRecordDO>()
                .eq(AssaySubmissionRecordDO::getFactoryId, factoryId)
                .eq(AssaySubmissionRecordDO::getStatus, status));
    }

}
