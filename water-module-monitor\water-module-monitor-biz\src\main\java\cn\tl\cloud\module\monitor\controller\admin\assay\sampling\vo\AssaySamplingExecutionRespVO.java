package cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 采样执行 Response VO")
@Data
public class AssaySamplingExecutionRespVO {

    @Schema(description = "执行ID", example = "1")
    private Long id;

    @Schema(description = "水厂ID", example = "1")
    private Long factoryId;

    @Schema(description = "关联任务ID", example = "1")
    private Long taskId;

    @Schema(description = "任务编号", example = "TASK-1-20240115-001")
    private String taskCode;

    @Schema(description = "具体采样位置", example = "进水口东侧2米处")
    private String samplingLocation;

    @Schema(description = "实际采样时间", example = "2024-01-15T08:30:00")
    private LocalDateTime actualSamplingTime;

    @Schema(description = "现场采样情况", example = "天气晴朗，水质清澈")
    private String samplingCondition;

    @Schema(description = "实际样品量(mL)", example = "100")
    private Integer actualSampleQuantity;

    @Schema(description = "实际样品外观", example = "微黄色，略浑浊")
    private String actualSampleAppearance;

    @Schema(description = "样品状态", example = "normal")
    private String sampleStatus;

    @Schema(description = "异常原因", example = "样品颜色异常")
    private String abnormalReason;

    @Schema(description = "处理措施", example = "重新采样")
    private String handleMeasures;

    @Schema(description = "执行状态", example = "pending")
    private String status;

    @Schema(description = "完成时间", example = "2024-01-15T10:00:00")
    private LocalDateTime completeTime;

    @Schema(description = "备注", example = "正常采样")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
