package cn.tl.cloud.module.monitor.service.assay.sampling;

import cn.tl.cloud.framework.common.exception.util.ServiceExceptionUtil;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySampleManagementPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySampleManagementSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySampleManagementDO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySamplingExecutionDO;
import cn.tl.cloud.module.monitor.dal.mysql.assay.sampling.AssaySampleManagementMapper;
import cn.tl.cloud.module.monitor.dal.mysql.assay.sampling.AssaySamplingExecutionMapper;
import cn.tl.cloud.module.monitor.enums.ErrorCodeConstants;
import cn.tl.cloud.module.monitor.service.assay.common.AssayOperationLogService;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 样品管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssaySampleManagementServiceImpl implements AssaySampleManagementService {

    @Resource
    private AssaySampleManagementMapper sampleManagementMapper;
    
    @Resource
    private AssaySamplingExecutionMapper samplingExecutionMapper;
    
    @Resource
    private AssayOperationLogService operationLogService;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    public Long createSampleManagement(@Valid AssaySampleManagementSaveReqVO createReqVO) {
        // 校验执行记录存在
        validateExecutionExists(createReqVO.getExecutionId(), createReqVO.getFactoryId());
        
        // 校验样品编号唯一性
        validateSampleCodeUnique(createReqVO.getFactoryId(), createReqVO.getSampleCode(), null);
        
        // 插入
        AssaySampleManagementDO sampleManagement = BeanUtils.toBean(createReqVO, AssaySampleManagementDO.class);
        sampleManagement.setStatus("stored"); // 默认状态为已入库
        sampleManagementMapper.insert(sampleManagement);
        
        // 记录操作日志
        try {
            operationLogService.recordLog(createReqVO.getFactoryId(), "assay_sample_management",
                    sampleManagement.getId(), "create", "创建样品管理记录",
                    null, null, objectMapper.writeValueAsString(sampleManagement));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        return sampleManagement.getId();
    }

    @Override
    public void updateSampleManagement(@Valid AssaySampleManagementSaveReqVO updateReqVO) {
        // 校验存在
        AssaySampleManagementDO oldSample = validateSampleManagementExists(updateReqVO.getId(), updateReqVO.getFactoryId());
        
        // 校验执行记录存在
        validateExecutionExists(updateReqVO.getExecutionId(), updateReqVO.getFactoryId());
        
        // 校验样品编号唯一性
        validateSampleCodeUnique(updateReqVO.getFactoryId(), updateReqVO.getSampleCode(), updateReqVO.getId());
        
        // 更新
        AssaySampleManagementDO updateObj = BeanUtils.toBean(updateReqVO, AssaySampleManagementDO.class);
        sampleManagementMapper.updateById(updateObj);
        
        // 记录操作日志
        AssaySampleManagementDO newSample = sampleManagementMapper.selectById(updateReqVO.getId());
        try {
            operationLogService.recordLog(updateReqVO.getFactoryId(), "assay_sample_management",
                    updateReqVO.getId(), "update", "更新样品管理记录",
                    null, objectMapper.writeValueAsString(oldSample), objectMapper.writeValueAsString(newSample));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void deleteSampleManagement(Long id, Long factoryId) {
        // 校验存在
        AssaySampleManagementDO sample = validateSampleManagementExists(id, factoryId);
        
        // 删除
        sampleManagementMapper.deleteById(id);
        
        // 记录操作日志
        try {
            operationLogService.recordLog(factoryId, "assay_sample_management",
                    id, "delete", "删除样品管理记录",
                    null, objectMapper.writeValueAsString(sample), null);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> storeSample(Long executionId, Long factoryId, Long operatorId, Map<String, Object> sampleData) {
        // 校验执行记录存在
        AssaySamplingExecutionDO execution = samplingExecutionMapper.selectById(executionId);
        if (execution == null || !execution.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLE_MANAGEMENT_EXECUTION_NOT_EXISTS);
        }
        
        // 生成样品编号
        String sampleCode = generateSampleCode(factoryId, executionId);
        
        // 创建样品记录
        AssaySampleManagementDO sample = new AssaySampleManagementDO();
        sample.setFactoryId(factoryId);
        sample.setSampleCode(sampleCode);
        sample.setExecutionId(executionId);
        sample.setStatus("stored");
        
        // 设置样品数据
        if (sampleData != null) {
            if (sampleData.containsKey("sampleType")) {
                sample.setSampleType((String) sampleData.get("sampleType"));
            }
            if (sampleData.containsKey("volume")) {
                sample.setVolume((Integer) sampleData.get("volume"));
            }
            if (sampleData.containsKey("appearance")) {
                sample.setAppearance((String) sampleData.get("appearance"));
            }
            if (sampleData.containsKey("preservationMethod")) {
                sample.setPreservationMethod((String) sampleData.get("preservationMethod"));
            }
            if (sampleData.containsKey("storageLocation")) {
                sample.setStorageLocation((String) sampleData.get("storageLocation"));
            }
            if (sampleData.containsKey("storageTemperature")) {
                sample.setStorageTemperature((Integer) sampleData.get("storageTemperature"));
            }
            if (sampleData.containsKey("expiryDate")) {
                sample.setExpiryDate(LocalDate.parse((String) sampleData.get("expiryDate")));
            }
            if (sampleData.containsKey("testItem")) {
                sample.setTestItem(Long.valueOf(sampleData.get("testItem").toString()));
            }
            if (sampleData.containsKey("samplingPersonId")) {
                sample.setSamplingPersonId(Long.valueOf(sampleData.get("samplingPersonId").toString()));
            }
            if (sampleData.containsKey("samplingDate")) {
                sample.setSamplingDate(LocalDate.parse((String) sampleData.get("samplingDate")));
            }
        }
        
        sampleManagementMapper.insert(sample);
        
        // 记录操作日志
        try {
            operationLogService.recordLog(factoryId, "assay_sample_management",
                    sample.getId(), "create", "样品入库",
                    operatorId, null, objectMapper.writeValueAsString(sample));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("sampleId", sample.getId());
        result.put("sampleCode", sampleCode);
        result.put("status", "stored");
        result.put("message", "样品入库成功");
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> updateSampleStatus(Long id, Long factoryId, String status, Long operatorId, String remark) {
        // 校验样品存在
        AssaySampleManagementDO sample = validateSampleManagementExists(id, factoryId);
        
        String oldStatus = sample.getStatus();
        
        // 更新状态
        AssaySampleManagementDO updateObj = new AssaySampleManagementDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        if (remark != null) {
            updateObj.setRemark(remark);
        }
        sampleManagementMapper.updateById(updateObj);
        
        // 记录状态变更日志
        operationLogService.recordStatusChange(factoryId, "assay_sample_management", 
                id, oldStatus, status, operatorId, remark);
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("sampleId", id);
        result.put("oldStatus", oldStatus);
        result.put("newStatus", status);
        result.put("message", "样品状态更新成功");
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> destroySample(Long id, Long factoryId, Long operatorId, String destroyReason) {
        // 校验样品存在
        AssaySampleManagementDO sample = validateSampleManagementExists(id, factoryId);
        
        String oldStatus = sample.getStatus();
        
        // 更新为销毁状态
        AssaySampleManagementDO updateObj = new AssaySampleManagementDO();
        updateObj.setId(id);
        updateObj.setStatus("destroyed");
        updateObj.setDestroyReason(destroyReason);
        updateObj.setDestroyTime(LocalDateTime.now());
        updateObj.setDestroyOperatorId(operatorId);
        sampleManagementMapper.updateById(updateObj);
        
        // 记录状态变更日志
        operationLogService.recordStatusChange(factoryId, "assay_sample_management", 
                id, oldStatus, "destroyed", operatorId, "销毁样品：" + destroyReason);
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("sampleId", id);
        result.put("destroyTime", LocalDateTime.now());
        result.put("destroyReason", destroyReason);
        result.put("message", "样品销毁成功");
        
        return result;
    }

    private AssaySampleManagementDO validateSampleManagementExists(Long id, Long factoryId) {
        AssaySampleManagementDO sample = sampleManagementMapper.selectById(id);
        if (sample == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLE_MANAGEMENT_NOT_EXISTS);
        }
        if (!sample.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FACTORY_DATA_ACCESS_DENIED);
        }
        return sample;
    }

    private void validateExecutionExists(Long executionId, Long factoryId) {
        AssaySamplingExecutionDO execution = samplingExecutionMapper.selectById(executionId);
        if (execution == null || !execution.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLE_MANAGEMENT_EXECUTION_NOT_EXISTS);
        }
    }

    private void validateSampleCodeUnique(Long factoryId, String sampleCode, Long id) {
        AssaySampleManagementDO sample = sampleManagementMapper.selectByFactoryIdAndSampleCode(factoryId, sampleCode);
        if (sample == null) {
            return;
        }
        if (id == null || !sample.getId().equals(id)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLE_MANAGEMENT_CODE_DUPLICATE);
        }
    }

    private String generateSampleCode(Long factoryId, Long executionId) {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return String.format("SAMPLE-%d-%s-%03d", factoryId, dateStr, 
            (int)(System.currentTimeMillis() % 1000));
    }

    @Override
    public AssaySampleManagementDO getSampleManagement(Long id, Long factoryId) {
        return validateSampleManagementExists(id, factoryId);
    }

    @Override
    public PageResult<AssaySampleManagementDO> getSampleManagementPage(AssaySampleManagementPageReqVO pageReqVO) {
        return sampleManagementMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AssaySampleManagementDO> getSampleManagementList(AssaySampleManagementPageReqVO reqVO) {
        return sampleManagementMapper.selectList(reqVO);
    }

    @Override
    public Map<String, Object> getSampleDetail(Long id, Long factoryId) {
        // 校验样品存在
        AssaySampleManagementDO sample = validateSampleManagementExists(id, factoryId);
        
        // TODO: 查询详细信息（包含执行信息、状态历史等）
        Map<String, Object> result = new HashMap<>();
        result.put("sampleInfo", sample);
        result.put("statusHistory", operationLogService.getStatusChangeHistory(factoryId, "assay_sample_management", id));
        
        return result;
    }

    @Override
    public List<AssaySampleManagementDO> getExpiringSamples(Long factoryId, Integer days) {
        LocalDate endDate = LocalDate.now().plusDays(days != null ? days : 7);
        return sampleManagementMapper.selectExpiredSamples(factoryId, endDate);
    }

    @Override
    public Map<String, Object> getSampleStatistics(Long factoryId) {
        Map<String, Object> result = new HashMap<>();
        result.put("storedCount", sampleManagementMapper.selectCountByStatus(factoryId, "stored"));
        result.put("testingCount", sampleManagementMapper.selectCountByStatus(factoryId, "testing"));
        result.put("completedCount", sampleManagementMapper.selectCountByStatus(factoryId, "completed"));
        result.put("destroyedCount", sampleManagementMapper.selectCountByStatus(factoryId, "destroyed"));
        
        return result;
    }

}
