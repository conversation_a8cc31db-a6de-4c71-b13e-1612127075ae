<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.tl.cloud.module.monitor.dal.mysql.assay.sampling.AssaySubmissionRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySubmissionRecordDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="factory_id" property="factoryId" jdbcType="BIGINT"/>
        <result column="record_code" property="recordCode" jdbcType="VARCHAR"/>
        <result column="execution_id" property="executionId" jdbcType="BIGINT"/>
        <result column="submission_date" property="submissionDate" jdbcType="DATE"/>
        <result column="submission_person_id" property="submissionPersonId" jdbcType="BIGINT"/>
        <result column="test_item" property="testItem" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="file_url" property="fileUrl" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="extra_field" property="extraField" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="BIT"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, factory_id, record_code, execution_id, submission_date, submission_person_id,
        test_item, status, file_url, remark, extra_field,
        creator, create_time, updater, update_time, deleted
    </sql>

    <!-- 查询送检记录详情（包含关联信息） -->
    <select id="selectSubmissionWithDetails" parameterType="java.lang.Long" resultType="java.util.Map">
        SELECT 
            sr.*,
            e.task_id,
            t.task_code,
            t.task_datetime,
            ti.name as test_item_name,
            sp.name as sampling_point_name,
            u.nickname as submission_person_name
        FROM assay_submission_record sr
        LEFT JOIN assay_sampling_execution e ON sr.execution_id = e.id
        LEFT JOIN assay_sampling_task t ON e.task_id = t.id
        LEFT JOIN assay_test_indicator ti ON sr.test_item = ti.id
        LEFT JOIN assay_sampling_point sp ON t.sampling_point = sp.id
        LEFT JOIN system_users u ON sr.submission_person_id = u.id
        WHERE sr.id = #{id} AND sr.deleted = 0
    </select>

    <!-- 批量更新送检记录状态 -->
    <update id="batchUpdateStatus">
        UPDATE assay_submission_record 
        SET status = #{status}, updater = #{updater}, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 查询待送检记录 -->
    <select id="selectPendingSubmissions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM assay_submission_record
        WHERE factory_id = #{factoryId}
          AND status = 'unSubmitted'
          AND deleted = 0
        ORDER BY submission_date ASC, create_time ASC
    </select>

    <!-- 统计送检状态分布 -->
    <select id="selectStatusStatistics" parameterType="java.lang.Long" resultType="java.util.Map">
        SELECT 
            status,
            COUNT(*) as count,
            DATE(submission_date) as submission_date
        FROM assay_submission_record
        WHERE factory_id = #{factoryId}
          AND deleted = 0
        GROUP BY status, DATE(submission_date)
        ORDER BY submission_date DESC
    </select>

    <!-- 查询指定日期范围内的送检统计 -->
    <select id="selectSubmissionStatistics" resultType="java.util.Map">
        SELECT 
            DATE(submission_date) as date,
            COUNT(*) as total_count,
            SUM(CASE WHEN status = 'submitted' THEN 1 ELSE 0 END) as submitted_count,
            SUM(CASE WHEN status = 'unSubmitted' THEN 1 ELSE 0 END) as unsubmitted_count
        FROM assay_submission_record
        WHERE factory_id = #{factoryId}
          AND submission_date BETWEEN #{startDate} AND #{endDate}
          AND deleted = 0
        GROUP BY DATE(submission_date)
        ORDER BY date DESC
    </select>

</mapper>
