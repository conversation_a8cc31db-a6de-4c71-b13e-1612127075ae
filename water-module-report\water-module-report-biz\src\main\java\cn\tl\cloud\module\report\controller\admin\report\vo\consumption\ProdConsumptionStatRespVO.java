package cn.tl.cloud.module.report.controller.admin.report.vo.consumption;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2025/7/29 20:49
 */
@Data
@Schema(description = "生产消耗指标统计结果")
public class ProdConsumptionStatRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1123")
    private Long id;

    @Schema(description = "水厂ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16724")
    private Long factoryId;

    @Schema(description = "产量（万m³）统计")
    private StatValue prodVol;

    @Schema(description = "进水水量（万m³）统计")
    private StatValue inFlowVol;

    @Schema(description = "处理水量（万m³）统计")
    private StatValue treatVol;

    @Schema(description = "电量消耗（KWh）统计")
    private StatValue elecCons;

    @Schema(description = "电单耗（KWh/Km³）统计")
    private StatValue elecSingleCons;

    @Schema(description = "碳源用量（Kg）统计")
    private StatValue carbonUsage;

    @Schema(description = "碳源单耗（Kg/Km³）统计")
    private StatValue carbonSingleCons;

    @Schema(description = "次氯酸钠用量（Kg）统计")
    private StatValue sodiumHypoUsage;

    @Schema(description = "次氯酸钠单耗（Kg/Km³）统计")
    private StatValue sodiumHypoSingleCons;

    @Schema(description = "聚合氯化铝（PAC）用量（Kg）统计")
    private StatValue pacUsage;

    @Schema(description = "聚合氯化铝（PAC）单耗（Kg/Km³）统计")
    private StatValue pacSingleCons;

    @Schema(description = "聚合硫酸铁用量（Kg）统计")
    private StatValue ferricSulfUsage;

    @Schema(description = "聚合硫酸铁单耗（Kg/Km³）统计")
    private StatValue ferricSulfSingleCons;

    @Schema(description = "氢氧化钠用量（Kg）统计")
    private StatValue naohUsage;

    @Schema(description = "氢氧化钠单耗（Kg/Km³）统计")
    private StatValue naohSingleCons;

    @Schema(description = "固体聚丙烯酰胺（阴离子）用量（Kg）统计")
    private StatValue anPamUsage;

    @Schema(description = "固体聚丙烯酰胺（阴离子）单耗（Kg/Km³）统计")
    private StatValue anPamSingleCons;

    @Schema(description = "固体聚丙烯酰胺（阳离子）用量（Kg）统计")
    private StatValue catPamUsage;

    @Schema(description = "固体聚丙烯酰胺（阳离子）单耗（Kg/Km³）统计")
    private StatValue catPamSingleCons;

    @Schema(description = "60%污泥产量（Kg）统计")
    private StatValue sludge60Prod;

    @Schema(description = "80%污泥产量（Kg）统计")
    private StatValue sludge80Prod;

    @Schema(description = "绝干污泥产量（Kg）统计")
    private StatValue drySludgeProd;

    @Schema(description = "产泥率（t/万m³）（湿泥）统计")
    private StatValue sludgeRate;

    @Schema(description = "污泥处理中固体聚丙烯酰胺（阳离子）用量（Kg）统计")
    private StatValue catPamSludgeUsage;

    @Schema(description = "污泥处理中固体聚丙烯酰胺（阳离子）单耗（Kg/Km³）统计")
    private StatValue catPamSludgeSingleCons;

    @Schema(description = "污泥处理中聚合氯化铝（PAC）用量（Kg）统计")
    private StatValue pacSludgeUsage;

    @Schema(description = "污泥处理中聚合氯化铝（PAC）单耗（Kg/Km³）统计")
    private StatValue pacSludgeSingleCons;

    @Schema(description = "液体铁盐用量（Kg）统计")
    private StatValue liqIronSaltUsage;

    @Schema(description = "液体铁盐单耗（Kg/Km³）统计")
    private StatValue liqIronSaltSingleCons;

    @Schema(description = "石灰用量（Kg）统计")
    private StatValue limeUsage;

    @Schema(description = "石灰单耗（Kg/Km³）统计")
    private StatValue limeSingleCons;

    @Data
    @Schema(description = "指标统计值")
    public static class StatValue {

        @Schema(description = "平均值（单耗类适用）")
        private BigDecimal avg;

        @Schema(description = "最大值")
        private BigDecimal max;

        @Schema(description = "最小值")
        private BigDecimal min;

        @Schema(description = "累计值（用量类适用）")
        private BigDecimal total;
    }
}
