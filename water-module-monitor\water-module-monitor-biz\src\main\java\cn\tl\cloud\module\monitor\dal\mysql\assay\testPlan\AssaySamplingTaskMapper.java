package cn.tl.cloud.module.monitor.dal.mysql.assay.testPlan;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tl.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo.AssaySamplingTaskPageReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.testPlan.AssaySamplingTaskDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 采样任务 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssaySamplingTaskMapper extends BaseMapperX<AssaySamplingTaskDO> {

    default PageResult<AssaySamplingTaskDO> selectPage(AssaySamplingTaskPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AssaySamplingTaskDO>()
                .eqIfPresent(AssaySamplingTaskDO::getFactoryId, reqVO.getFactoryId())
                .eqIfPresent(AssaySamplingTaskDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(AssaySamplingTaskDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AssaySamplingTaskDO::getSamplerId, reqVO.getSamplerId())
                .betweenIfPresent(AssaySamplingTaskDO::getTaskDatetime, 
                    reqVO.getStartDate() != null ? reqVO.getStartDate().atStartOfDay() : null,
                    reqVO.getEndDate() != null ? reqVO.getEndDate().atTime(23, 59, 59) : null)
                .orderByDesc(AssaySamplingTaskDO::getId));
    }

    default List<AssaySamplingTaskDO> selectList(AssaySamplingTaskPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AssaySamplingTaskDO>()
                .eqIfPresent(AssaySamplingTaskDO::getFactoryId, reqVO.getFactoryId())
                .eqIfPresent(AssaySamplingTaskDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(AssaySamplingTaskDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AssaySamplingTaskDO::getSamplerId, reqVO.getSamplerId())
                .betweenIfPresent(AssaySamplingTaskDO::getTaskDatetime, 
                    reqVO.getStartDate() != null ? reqVO.getStartDate().atStartOfDay() : null,
                    reqVO.getEndDate() != null ? reqVO.getEndDate().atTime(23, 59, 59) : null)
                .orderByDesc(AssaySamplingTaskDO::getId));
    }

    default AssaySamplingTaskDO selectByFactoryIdAndTaskCode(Long factoryId, String taskCode) {
        return selectOne(new LambdaQueryWrapperX<AssaySamplingTaskDO>()
                .eq(AssaySamplingTaskDO::getFactoryId, factoryId)
                .eq(AssaySamplingTaskDO::getTaskCode, taskCode));
    }

    default Long selectCountByPlanId(Long planId) {
        return selectCount(new LambdaQueryWrapperX<AssaySamplingTaskDO>()
                .eq(AssaySamplingTaskDO::getPlanId, planId));
    }

    default List<AssaySamplingTaskDO> selectByPlanId(Long planId) {
        return selectList(new LambdaQueryWrapperX<AssaySamplingTaskDO>()
                .eq(AssaySamplingTaskDO::getPlanId, planId)
                .orderByDesc(AssaySamplingTaskDO::getId));
    }

    default List<AssaySamplingTaskDO> selectByStatus(Long factoryId, String status) {
        return selectList(new LambdaQueryWrapperX<AssaySamplingTaskDO>()
                .eq(AssaySamplingTaskDO::getFactoryId, factoryId)
                .eq(AssaySamplingTaskDO::getStatus, status)
                .orderByDesc(AssaySamplingTaskDO::getId));
    }

    default List<AssaySamplingTaskDO> selectConflictTasks(Long factoryId, Long samplingPoint, 
                                                          LocalDateTime startTime, LocalDateTime endTime,
                                                          Long excludeTaskId) {
        LambdaQueryWrapper<AssaySamplingTaskDO> query = new LambdaQueryWrapper<AssaySamplingTaskDO>()
                .eq(AssaySamplingTaskDO::getFactoryId, factoryId)
                .eq(AssaySamplingTaskDO::getSamplingPoint, samplingPoint)
                .between(AssaySamplingTaskDO::getTaskDatetime, startTime, endTime);
        if (excludeTaskId != null) {
            query.ne(AssaySamplingTaskDO::getId, excludeTaskId);
        }
        return selectList(query);

    }

    default List<AssaySamplingTaskDO> selectPersonConflictTasks(Long factoryId, Long personId, 
                                                                LocalDateTime startTime, LocalDateTime endTime,
                                                                Long excludeTaskId) {
        LambdaQueryWrapper<AssaySamplingTaskDO> query = new LambdaQueryWrapper<AssaySamplingTaskDO>()
                .eq(AssaySamplingTaskDO::getFactoryId, factoryId)
                .and(wrapper -> wrapper
                        .eq(AssaySamplingTaskDO::getSamplerId, personId)
                        .or().eq(AssaySamplingTaskDO::getTesterId, personId)
                        .or().eq(AssaySamplingTaskDO::getReviewerId, personId))
                .between(AssaySamplingTaskDO::getTaskDatetime, startTime, endTime);
        if (excludeTaskId != null) {
            query.ne(AssaySamplingTaskDO::getId, excludeTaskId);
        }
        return selectList(query);

    }

    default List<AssaySamplingTaskDO> selectCalendarTasks(Long factoryId, LocalDate startDate, LocalDate endDate) {
        return selectList(new LambdaQueryWrapperX<AssaySamplingTaskDO>()
                .eq(AssaySamplingTaskDO::getFactoryId, factoryId)
                .between(AssaySamplingTaskDO::getTaskDatetime, 
                    startDate.atStartOfDay(), endDate.atTime(23, 59, 59))
                .orderByAsc(AssaySamplingTaskDO::getTaskDatetime));
    }

}
