package cn.tl.cloud.module.monitor.dal.mysql.assay.baseInfo;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tl.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestIndicatorPageReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestCategoryDO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestIndicatorDO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestProjectDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 检测指标 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssayTestIndicatorMapper extends BaseMapperX<AssayTestIndicatorDO> {

    default PageResult<AssayTestIndicatorDO> selectPage(AssayTestIndicatorPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AssayTestIndicatorDO>()
                .eqIfPresent(AssayTestIndicatorDO::getFactoryId, reqVO.getFactoryId())
                .eqIfPresent(AssayTestIndicatorDO::getProjectId, reqVO.getProjectId())
                .likeIfPresent(AssayTestIndicatorDO::getName, reqVO.getName())
                .eqIfPresent(AssayTestIndicatorDO::getIsEnabled, reqVO.getIsEnabled())
                .orderByDesc(AssayTestIndicatorDO::getId));
    }

    default List<AssayTestIndicatorDO> selectList(AssayTestIndicatorPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AssayTestIndicatorDO>()
                .eqIfPresent(AssayTestIndicatorDO::getFactoryId, reqVO.getFactoryId())
                .eqIfPresent(AssayTestIndicatorDO::getProjectId, reqVO.getProjectId())
                .likeIfPresent(AssayTestIndicatorDO::getName, reqVO.getName())
                .eqIfPresent(AssayTestIndicatorDO::getIsEnabled, reqVO.getIsEnabled())
                .orderByDesc(AssayTestIndicatorDO::getId));
    }

    default List<AssayTestIndicatorDO> selectListByProjectId(Long factoryId, Long projectId) {
        return selectList(new LambdaQueryWrapperX<AssayTestIndicatorDO>()
                .eq(AssayTestIndicatorDO::getFactoryId, factoryId)
                .eq(AssayTestIndicatorDO::getProjectId, projectId)
                .eq(AssayTestIndicatorDO::getIsEnabled, true)
                .orderByDesc(AssayTestIndicatorDO::getId));
    }

    default AssayTestIndicatorDO selectByFactoryIdAndCode(Long factoryId, String code) {
        return selectOne(new LambdaQueryWrapperX<AssayTestIndicatorDO>()
                .eq(AssayTestIndicatorDO::getFactoryId, factoryId)
                .eq(AssayTestIndicatorDO::getCode, code));
    }

    default Long selectCountByProjectId(Long projectId) {
        return selectCount(new LambdaQueryWrapperX<AssayTestIndicatorDO>()
                .eq(AssayTestIndicatorDO::getProjectId, projectId));
    }

    List<AssayTestIndicatorDO> selectListByProjectIds(@Param("factoryId") Long factoryId,
                                                      @Param("projectIds") List<Long> projectIds);






}
