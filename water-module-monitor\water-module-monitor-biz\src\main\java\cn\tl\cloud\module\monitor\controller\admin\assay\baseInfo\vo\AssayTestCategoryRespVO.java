package cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 检测项目类型 Response VO")
@Data
public class AssayTestCategoryRespVO {

    @Schema(description = "类型ID", example = "1")
    private Long id;

    @Schema(description = "水厂ID", example = "1")
    private Long factoryId;

    @Schema(description = "类型代码", example = "WATER")
    private String code;

    @Schema(description = "类型名称", example = "水质检测")
    private String name;

    @Schema(description = "类型描述", example = "污水处理厂水质检测项目")
    private String description;

    @Schema(description = "启用状态", example = "true")
    private Boolean isEnabled;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
