package cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 检测指标新增/修改 Request VO")
@Data
public class AssayTestIndicatorSaveReqVO {

    @Schema(description = "指标ID", example = "1")
    private Long id;

    @Schema(description = "水厂ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "水厂ID不能为空")
    private Long factoryId;

    @Schema(description = "指标代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "COD001")
    @NotBlank(message = "指标代码不能为空")
    @Size(max = 50, message = "指标代码长度不能超过50个字符")
    private String code;

    @Schema(description = "指标名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "COD")
    @NotBlank(message = "指标名称不能为空")
    @Size(max = 100, message = "指标名称长度不能超过100个字符")
    private String name;

    @Schema(description = "所属项目ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "所属项目ID不能为空")
    private Long projectId;

    @Schema(description = "检测方法", requiredMode = Schema.RequiredMode.REQUIRED, example = "重铬酸钾法")
    @NotBlank(message = "检测方法不能为空")
    @Size(max = 200, message = "检测方法长度不能超过200个字符")
    private String method;

    @Schema(description = "数据单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "mg/L")
    @NotBlank(message = "数据单位不能为空")
    @Size(max = 20, message = "数据单位长度不能超过20个字符")
    private String unit;

    @Schema(description = "标准值下限", example = "0.0")
    @DecimalMin(value = "0", message = "标准值下限不能小于0")
    private BigDecimal standardMin;

    @Schema(description = "标准值上限", example = "50.0")
    @DecimalMin(value = "0", message = "标准值上限不能小于0")
    private BigDecimal standardMax;

    @Schema(description = "检测仪器", requiredMode = Schema.RequiredMode.REQUIRED, example = "COD分析仪")
    @NotBlank(message = "检测仪器不能为空")
    @Size(max = 100, message = "检测仪器长度不能超过100个字符")
    private String equipment;

    @Schema(description = "精度要求", example = "±5%")
    @Size(max = 50, message = "精度要求长度不能超过50个字符")
    private String precisionLimit;

    @Schema(description = "样品量(mL)", example = "100")
    @Min(value = 1, message = "样品量必须大于0")
    private Integer sampleVolume;

    @Schema(description = "检测耗时(分钟)", example = "60")
    @Min(value = 1, message = "检测耗时必须大于0")
    private Integer detectionTimeMinutes;

    @Schema(description = "启用状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "启用状态不能为空")
    private Boolean isEnabled;

}
