package cn.tl.cloud.module.monitor.service.assay.sampling;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySubmissionRecordPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySubmissionRecordSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySubmissionRecordDO;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 送检记录 Service 接口
 *
 * <AUTHOR>
 */
public interface AssaySubmissionRecordService {

    /**
     * 创建送检记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSubmissionRecord(@Valid AssaySubmissionRecordSaveReqVO createReqVO);

    /**
     * 更新送检记录
     *
     * @param updateReqVO 更新信息
     */
    void updateSubmissionRecord(@Valid AssaySubmissionRecordSaveReqVO updateReqVO);

    /**
     * 删除送检记录
     *
     * @param id 编号
     * @param factoryId 水厂ID
     */
    void deleteSubmissionRecord(Long id, Long factoryId);

    /**
     * 获得送检记录
     *
     * @param id 编号
     * @param factoryId 水厂ID
     * @return 送检记录
     */
    AssaySubmissionRecordDO getSubmissionRecord(Long id, Long factoryId);

    /**
     * 获得送检记录分页
     *
     * @param pageReqVO 分页查询
     * @return 送检记录分页
     */
    PageResult<AssaySubmissionRecordDO> getSubmissionRecordPage(AssaySubmissionRecordPageReqVO pageReqVO);

    /**
     * 获得送检记录列表
     *
     * @param reqVO 查询条件
     * @return 送检记录列表
     */
    List<AssaySubmissionRecordDO> getSubmissionRecordList(AssaySubmissionRecordPageReqVO reqVO);

    /**
     * 送检完成
     *
     * @param id 送检记录ID
     * @param factoryId 水厂ID
     * @param operatorId 操作人ID
     * @param fileUrl 送检单文件URL
     * @return 操作结果
     */
    Map<String, Object> completeSubmission(Long id, Long factoryId, Long operatorId, String fileUrl);

    /**
     * 获取送检记录详情
     *
     * @param id 送检记录ID
     * @param factoryId 水厂ID
     * @return 送检记录详情
     */
    Map<String, Object> getSubmissionDetail(Long id, Long factoryId);

    /**
     * 获取待送检记录
     *
     * @param factoryId 水厂ID
     * @return 待送检记录列表
     */
    List<AssaySubmissionRecordDO> getPendingSubmissions(Long factoryId);

    /**
     * 获取送检统计
     *
     * @param factoryId 水厂ID
     * @return 送检统计
     */
    Map<String, Object> getSubmissionStatistics(Long factoryId);

}
