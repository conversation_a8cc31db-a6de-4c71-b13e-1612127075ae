package cn.tl.cloud.module.monitor.dal.mysql.assay.baseInfo;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tl.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssaySamplingPointPageReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssaySamplingPointDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 采样点 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssaySamplingPointMapper extends BaseMapperX<AssaySamplingPointDO> {

    default PageResult<AssaySamplingPointDO> selectPage(AssaySamplingPointPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AssaySamplingPointDO>()
                .eqIfPresent(AssaySamplingPointDO::getFactoryId, reqVO.getFactoryId())
                .likeIfPresent(AssaySamplingPointDO::getName, reqVO.getName())
                .eqIfPresent(AssaySamplingPointDO::getType, reqVO.getType())
                .eqIfPresent(AssaySamplingPointDO::getIsEnabled, reqVO.getIsEnabled())
                .eqIfPresent(AssaySamplingPointDO::getManagerId, reqVO.getManagerId())
                .orderByDesc(AssaySamplingPointDO::getId));
    }

    default List<AssaySamplingPointDO> selectList(AssaySamplingPointPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AssaySamplingPointDO>()
                .eqIfPresent(AssaySamplingPointDO::getFactoryId, reqVO.getFactoryId())
                .likeIfPresent(AssaySamplingPointDO::getName, reqVO.getName())
                .eqIfPresent(AssaySamplingPointDO::getType, reqVO.getType())
                .eqIfPresent(AssaySamplingPointDO::getIsEnabled, reqVO.getIsEnabled())
                .eqIfPresent(AssaySamplingPointDO::getManagerId, reqVO.getManagerId())
                .orderByDesc(AssaySamplingPointDO::getId));
    }

    default List<AssaySamplingPointDO> selectListByType(Long factoryId, String type) {
        return selectList(new LambdaQueryWrapperX<AssaySamplingPointDO>()
                .eq(AssaySamplingPointDO::getFactoryId, factoryId)
                .eq(AssaySamplingPointDO::getType, type)
                .eq(AssaySamplingPointDO::getIsEnabled, true)
                .orderByDesc(AssaySamplingPointDO::getId));
    }

    default List<AssaySamplingPointDO> selectSimpleList(Long factoryId) {
        return selectList(new LambdaQueryWrapperX<AssaySamplingPointDO>()
                .eq(AssaySamplingPointDO::getFactoryId, factoryId)
                .eq(AssaySamplingPointDO::getIsEnabled, true)
                .orderByDesc(AssaySamplingPointDO::getId));
    }

    default AssaySamplingPointDO selectByFactoryIdAndCode(Long factoryId, String code) {
        return selectOne(new LambdaQueryWrapperX<AssaySamplingPointDO>()
                .eq(AssaySamplingPointDO::getFactoryId, factoryId)
                .eq(AssaySamplingPointDO::getCode, code));
    }

}
