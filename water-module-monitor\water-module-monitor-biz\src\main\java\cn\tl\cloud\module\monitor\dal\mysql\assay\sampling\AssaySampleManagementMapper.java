package cn.tl.cloud.module.monitor.dal.mysql.assay.sampling;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tl.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySampleManagementPageReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySampleManagementDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.util.List;

/**
 * 样品管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssaySampleManagementMapper extends BaseMapperX<AssaySampleManagementDO> {

    default PageResult<AssaySampleManagementDO> selectPage(AssaySampleManagementPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AssaySampleManagementDO>()
                .eqIfPresent(AssaySampleManagementDO::getFactoryId, reqVO.getFactoryId())
                .likeIfPresent(AssaySampleManagementDO::getSampleCode, reqVO.getSampleCode())
                .eqIfPresent(AssaySampleManagementDO::getExecutionId, reqVO.getExecutionId())
                .eqIfPresent(AssaySampleManagementDO::getSampleType, reqVO.getSampleType())
                .eqIfPresent(AssaySampleManagementDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AssaySampleManagementDO::getSamplingPersonId, reqVO.getSamplingPersonId())
                .betweenIfPresent(AssaySampleManagementDO::getSamplingDate, reqVO.getStartDate(), reqVO.getEndDate())
                .orderByDesc(AssaySampleManagementDO::getId));
    }

    default List<AssaySampleManagementDO> selectList(AssaySampleManagementPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AssaySampleManagementDO>()
                .eqIfPresent(AssaySampleManagementDO::getFactoryId, reqVO.getFactoryId())
                .likeIfPresent(AssaySampleManagementDO::getSampleCode, reqVO.getSampleCode())
                .eqIfPresent(AssaySampleManagementDO::getExecutionId, reqVO.getExecutionId())
                .eqIfPresent(AssaySampleManagementDO::getSampleType, reqVO.getSampleType())
                .eqIfPresent(AssaySampleManagementDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AssaySampleManagementDO::getSamplingPersonId, reqVO.getSamplingPersonId())
                .betweenIfPresent(AssaySampleManagementDO::getSamplingDate, reqVO.getStartDate(), reqVO.getEndDate())
                .orderByDesc(AssaySampleManagementDO::getId));
    }

    default AssaySampleManagementDO selectByFactoryIdAndSampleCode(Long factoryId, String sampleCode) {
        return selectOne(new LambdaQueryWrapperX<AssaySampleManagementDO>()
                .eq(AssaySampleManagementDO::getFactoryId, factoryId)
                .eq(AssaySampleManagementDO::getSampleCode, sampleCode));
    }

    default List<AssaySampleManagementDO> selectByExecutionId(Long executionId) {
        return selectList(new LambdaQueryWrapperX<AssaySampleManagementDO>()
                .eq(AssaySampleManagementDO::getExecutionId, executionId)
                .orderByDesc(AssaySampleManagementDO::getId));
    }

    default List<AssaySampleManagementDO> selectByStatus(Long factoryId, String status) {
        return selectList(new LambdaQueryWrapperX<AssaySampleManagementDO>()
                .eq(AssaySampleManagementDO::getFactoryId, factoryId)
                .eq(AssaySampleManagementDO::getStatus, status)
                .orderByDesc(AssaySampleManagementDO::getId));
    }

    default List<AssaySampleManagementDO> selectExpiredSamples(Long factoryId, LocalDate currentDate) {
        return selectList(new LambdaQueryWrapperX<AssaySampleManagementDO>()
                .eq(AssaySampleManagementDO::getFactoryId, factoryId)
                .lt(AssaySampleManagementDO::getExpiryDate, currentDate)
                .ne(AssaySampleManagementDO::getStatus, "destroyed")
                .orderByAsc(AssaySampleManagementDO::getExpiryDate));
    }

    default Long selectCountByStatus(Long factoryId, String status) {
        return selectCount(new LambdaQueryWrapperX<AssaySampleManagementDO>()
                .eq(AssaySampleManagementDO::getFactoryId, factoryId)
                .eq(AssaySampleManagementDO::getStatus, status));
    }

}
