package cn.tl.cloud.module.monitor.dal.mysql.assay.sampling;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tl.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySamplingExecutionPageReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySamplingExecutionDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.util.List;

/**
 * 采样执行 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssaySamplingExecutionMapper extends BaseMapperX<AssaySamplingExecutionDO> {

    default PageResult<AssaySamplingExecutionDO> selectPage(AssaySamplingExecutionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AssaySamplingExecutionDO>()
                .eqIfPresent(AssaySamplingExecutionDO::getFactoryId, reqVO.getFactoryId())
                .eqIfPresent(AssaySamplingExecutionDO::getTaskId, reqVO.getTaskId())
                .eqIfPresent(AssaySamplingExecutionDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AssaySamplingExecutionDO::getSampleStatus, reqVO.getSampleStatus())
                .betweenIfPresent(AssaySamplingExecutionDO::getActualSamplingTime,
                    reqVO.getStartDate() != null ? reqVO.getStartDate().atStartOfDay() : null,
                    reqVO.getEndDate() != null ? reqVO.getEndDate().atTime(23, 59, 59) : null)
                .orderByDesc(AssaySamplingExecutionDO::getId));
    }

    default List<AssaySamplingExecutionDO> selectList(AssaySamplingExecutionPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AssaySamplingExecutionDO>()
                .eqIfPresent(AssaySamplingExecutionDO::getFactoryId, reqVO.getFactoryId())
                .eqIfPresent(AssaySamplingExecutionDO::getTaskId, reqVO.getTaskId())
                .eqIfPresent(AssaySamplingExecutionDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AssaySamplingExecutionDO::getSampleStatus, reqVO.getSampleStatus())
                .betweenIfPresent(AssaySamplingExecutionDO::getActualSamplingTime,
                    reqVO.getStartDate() != null ? reqVO.getStartDate().atStartOfDay() : null,
                    reqVO.getEndDate() != null ? reqVO.getEndDate().atTime(23, 59, 59) : null)
                .orderByDesc(AssaySamplingExecutionDO::getId));
    }

    default AssaySamplingExecutionDO selectByFactoryIdAndTaskId(Long factoryId, Long taskId) {
        return selectOne(new LambdaQueryWrapperX<AssaySamplingExecutionDO>()
                .eq(AssaySamplingExecutionDO::getFactoryId, factoryId)
                .eq(AssaySamplingExecutionDO::getTaskId, taskId));
    }

    default List<AssaySamplingExecutionDO> selectByStatus(Long factoryId, String status) {
        return selectList(new LambdaQueryWrapperX<AssaySamplingExecutionDO>()
                .eq(AssaySamplingExecutionDO::getFactoryId, factoryId)
                .eq(AssaySamplingExecutionDO::getStatus, status)
                .orderByDesc(AssaySamplingExecutionDO::getId));
    }

    default List<AssaySamplingExecutionDO> selectByDateRange(Long factoryId, LocalDate startDate, LocalDate endDate) {
        return selectList(new LambdaQueryWrapperX<AssaySamplingExecutionDO>()
                .eq(AssaySamplingExecutionDO::getFactoryId, factoryId)
                .between(AssaySamplingExecutionDO::getActualSamplingTime,
                    startDate.atStartOfDay(), endDate.atTime(23, 59, 59))
                .orderByDesc(AssaySamplingExecutionDO::getActualSamplingTime));
    }

    default Long selectCountByStatus(Long factoryId, String status) {
        return selectCount(new LambdaQueryWrapperX<AssaySamplingExecutionDO>()
                .eq(AssaySamplingExecutionDO::getFactoryId, factoryId)
                .eq(AssaySamplingExecutionDO::getStatus, status));
    }

}
