package cn.tl.cloud.module.monitor.service.acqconf;

import cn.tl.cloud.module.monitor.controller.admin.acqconf.vo.IotDevicePageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.acqconf.vo.IotPointPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.acqconf.vo.IotStatisticsRespVO;
import cn.tl.cloud.module.monitor.dal.dataobject.acqconf.IotGroup;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Map;

/**
 *
 */
public interface IotService {


    /**
     * 统计厂站数量、设备数量、指标数量
     * @return
     */
    IotStatisticsRespVO statistics();


    /**
     * 分页查询设备列表
     * @param reqVO
     * @return
     */
    Page devicePage(IotDevicePageReqVO reqVO);

    /**
     * 查询所有分组/厂站
     * @return
     */
    List<IotGroup> listGroup();


    /**
     * 分页查询点位列表
     * @param reqVO
     * @return
     */
    Page pointPage(IotPointPageReqVO reqVO);
}
