package cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.rule;

import cn.tl.cloud.framework.apilog.core.annotation.ApiAccessLog;
import cn.tl.cloud.framework.common.pojo.CommonResult;
import cn.tl.cloud.framework.common.pojo.PageParam;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.framework.excel.core.util.ExcelUtils;

import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.rule.vo.RulePageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.rule.vo.RuleRespVO;
import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.rule.vo.RuleSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.monitor.alarm.rule.RuleDO;
import cn.tl.cloud.module.monitor.dal.dataobject.monitor.alarm.rulefactor.RuleFactorDO;
import cn.tl.cloud.module.monitor.service.monitor.alarm.rule.RuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.tl.cloud.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.tl.cloud.framework.common.pojo.CommonResult.success;



@Tag(name = "管理后台 - 告警规则")
@RestController
@RequestMapping("/monitor/alarm/rules")
@Validated
public class RuleController {

    @Resource
    private RuleService ruleService;


    //TODO:删除缓存
    @PostMapping("/create")
    @Operation(summary = "创建告警规则")
    //@PreAuthorize("@ss.hasPermission('alarm:rule:create')")
    public CommonResult<Long> createRule(@Valid @RequestBody RuleSaveReqVO createReqVO) {
        return success(ruleService.createRule(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新告警规则")
    //@PreAuthorize("@ss.hasPermission('alarm:rule:update')")
    public CommonResult<Boolean> updateRule(@Valid @RequestBody RuleSaveReqVO updateReqVO) {
        ruleService.updateRule(updateReqVO);
        return success(true);
    }



    @DeleteMapping("/delete")
    @Operation(summary = "删除告警规则")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermission('alarm:rule:delete')")
    public CommonResult<Boolean> deleteRule(@RequestParam("id") Long id) {
        ruleService.deleteRule(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得告警规则")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('alarm:rule:query')")
    public CommonResult<RuleSaveReqVO> getRule(@RequestParam("id") Long id) {
        RuleSaveReqVO rule = ruleService.getRuleAndRuleFactors(id);
        return success(rule);
    }

    @GetMapping("/page")
    @Operation(summary = "获得告警规则分页")
    ////@PreAuthorize("@ss.hasPermission('alarm:rule:query')")
    public CommonResult<PageResult<RuleRespVO>> getRulePage(@Valid RulePageReqVO pageReqVO) {
        PageResult<RuleDO> pageResult = ruleService.getRulePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RuleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出告警规则 Excel")
    //@PreAuthorize("@ss.hasPermission('alarm:rule:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportRuleExcel(@Valid RulePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<RuleDO> list = ruleService.getRulePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "告警规则.xls", "数据", RuleRespVO.class,
                        BeanUtils.toBean(list, RuleRespVO.class));
    }

    // ==================== 子表（告警规则因子表（组合表达式变量配置/监测因子）） ====================

    @GetMapping("/rule-factor/list-by-rule-id")
    @Operation(summary = "获得告警规则因子表（组合表达式变量配置/监测因子）列表")
    @Parameter(name = "ruleId", description = "关联告警规则ID")
    //@PreAuthorize("@ss.hasPermission('alarm:rule:query')")
    public CommonResult<List<RuleFactorDO>> getRuleFactorListByRuleId(@RequestParam("ruleId") Long ruleId) {
        return success(ruleService.getRuleFactorListByRuleId(ruleId));
    }

}
