package cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 检测项目 Response VO")
@Data
public class AssayTestProjectRespVO {

    @Schema(description = "项目ID", example = "1")
    private Long id;

    @Schema(description = "水厂ID", example = "1")
    private Long factoryId;

    @Schema(description = "项目代码", example = "WATER_ORGANIC")
    private String code;

    @Schema(description = "项目名称", example = "有机物检测")
    private String name;

    @Schema(description = "所属类型ID", example = "1")
    private Long categoryId;

    @Schema(description = "项目描述", example = "有机污染物检测项目")
    private String description;

    @Schema(description = "启用状态", example = "true")
    private Boolean isEnabled;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
