package cn.tl.cloud.module.monitor.dal.mysql.assay.baseInfo;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tl.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo.AssayTestCategoryPageReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestCategoryDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检测项目类型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssayTestCategoryMapper extends BaseMapperX<AssayTestCategoryDO> {

    default PageResult<AssayTestCategoryDO> selectPage(AssayTestCategoryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AssayTestCategoryDO>()
                .eqIfPresent(AssayTestCategoryDO::getFactoryId, reqVO.getFactoryId())
                .likeIfPresent(AssayTestCategoryDO::getName, reqVO.getName())
                .eqIfPresent(AssayTestCategoryDO::getIsEnabled, reqVO.getIsEnabled())
                .orderByDesc(AssayTestCategoryDO::getId));
    }

    default List<AssayTestCategoryDO> selectList(AssayTestCategoryPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AssayTestCategoryDO>()
                .eqIfPresent(AssayTestCategoryDO::getFactoryId, reqVO.getFactoryId())
                .likeIfPresent(AssayTestCategoryDO::getName, reqVO.getName())
                .eqIfPresent(AssayTestCategoryDO::getIsEnabled, reqVO.getIsEnabled())
                .orderByDesc(AssayTestCategoryDO::getId));
    }

    default List<AssayTestCategoryDO> selectSimpleList(Long factoryId) {
        return selectList(new LambdaQueryWrapperX<AssayTestCategoryDO>()
                .eq(AssayTestCategoryDO::getFactoryId, factoryId)
                .eq(AssayTestCategoryDO::getIsEnabled, true)
                .orderByDesc(AssayTestCategoryDO::getId));
    }

    default AssayTestCategoryDO selectByFactoryIdAndCode(Long factoryId, String code) {
        return selectOne(new LambdaQueryWrapperX<AssayTestCategoryDO>()
                .eq(AssayTestCategoryDO::getFactoryId, factoryId)
                .eq(AssayTestCategoryDO::getCode, code));
    }

    Page<AssayTestCategoryDO> selectPageByFactoryId(@Param("page") Page<?> page,
                                                    @Param("factoryId") Long factoryId);


}
