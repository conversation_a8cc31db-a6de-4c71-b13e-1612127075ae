<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.tl.cloud.module.monitor.dal.mysql.assay.testPlan.AssaySamplingTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.tl.cloud.module.monitor.dal.dataobject.assay.testPlan.AssaySamplingTaskDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="factory_id" property="factoryId" jdbcType="BIGINT"/>
        <result column="task_code" property="taskCode" jdbcType="VARCHAR"/>
        <result column="plan_id" property="planId" jdbcType="BIGINT"/>
        <result column="task_datetime" property="taskDatetime" jdbcType="TIMESTAMP"/>
        <result column="test_item" property="testItem" jdbcType="BIGINT"/>
        <result column="sampling_point" property="samplingPoint" jdbcType="BIGINT"/>
        <result column="sampler_id" property="samplerId" jdbcType="BIGINT"/>
        <result column="tester_id" property="testerId" jdbcType="BIGINT"/>
        <result column="reviewer_id" property="reviewerId" jdbcType="BIGINT"/>
        <result column="priority" property="priority" jdbcType="VARCHAR"/>
        <result column="expected_sample_quantity" property="expectedSampleQuantity" jdbcType="INTEGER"/>
        <result column="expected_sample_nature" property="expectedSampleNature" jdbcType="VARCHAR"/>
        <result column="expected_sample_appearance" property="expectedSampleAppearance" jdbcType="VARCHAR"/>
        <result column="expected_supernatant" property="expectedSupernatant" jdbcType="VARCHAR"/>
        <result column="sampling_instructions" property="samplingInstructions" jdbcType="LONGVARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="complete_time" property="completeTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="extra_field" property="extraField" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="BIT"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, factory_id, task_code, plan_id, task_datetime, test_item, sampling_point,
        sampler_id, tester_id, reviewer_id, priority, expected_sample_quantity,
        expected_sample_nature, expected_sample_appearance, expected_supernatant,
        sampling_instructions, status, start_time, complete_time, remark, extra_field,
        creator, create_time, updater, update_time, deleted
    </sql>

    <!-- 批量插入任务 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO assay_sampling_task (
            factory_id, task_code, plan_id, task_datetime, test_item, sampling_point,
            sampler_id, tester_id, reviewer_id, priority, expected_sample_quantity,
            expected_sample_nature, expected_sample_appearance, expected_supernatant,
            sampling_instructions, status, remark, extra_field, creator, create_time,
            updater, update_time, deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.factoryId}, #{item.taskCode}, #{item.planId}, #{item.taskDatetime},
                #{item.testItem}, #{item.samplingPoint}, #{item.samplerId}, #{item.testerId},
                #{item.reviewerId}, #{item.priority}, #{item.expectedSampleQuantity},
                #{item.expectedSampleNature}, #{item.expectedSampleAppearance},
                #{item.expectedSupernatant}, #{item.samplingInstructions}, #{item.status},
                #{item.remark}, #{item.extraField}, #{item.creator}, #{item.createTime},
                #{item.updater}, #{item.updateTime}, #{item.deleted}
            )
        </foreach>
    </insert>

    <!-- 查询任务详情（包含关联信息） -->
    <select id="selectTaskWithDetails" parameterType="java.lang.Long" resultType="java.util.Map">
        SELECT 
            t.*,
            p.name as plan_name,
            p.type as plan_type,
            ti.name as test_item_name,
            sp.name as sampling_point_name,
            u1.nickname as sampler_name,
            u2.nickname as tester_name,
            u3.nickname as reviewer_name
        FROM assay_sampling_task t
        LEFT JOIN assay_sampling_plan p ON t.plan_id = p.id
        LEFT JOIN assay_test_indicator ti ON t.test_item = ti.id
        LEFT JOIN assay_sampling_point sp ON t.sampling_point = sp.id
        LEFT JOIN system_users u1 ON t.sampler_id = u1.id
        LEFT JOIN system_users u2 ON t.tester_id = u2.id
        LEFT JOIN system_users u3 ON t.reviewer_id = u3.id
        WHERE t.id = #{id} AND t.deleted = 0
    </select>

    <!-- 查询人员冲突任务 -->
    <select id="selectPersonConflictTasksWithDetails" resultType="java.util.Map">
        SELECT 
            t.id,
            t.task_code,
            t.task_datetime,
            t.status,
            p.name as plan_name,
            CASE 
                WHEN t.sampler_id = #{personId} THEN 'sampler'
                WHEN t.tester_id = #{personId} THEN 'tester'
                WHEN t.reviewer_id = #{personId} THEN 'reviewer'
            END as role_type
        FROM assay_sampling_task t
        LEFT JOIN assay_sampling_plan p ON t.plan_id = p.id
        WHERE t.factory_id = #{factoryId}
          AND (t.sampler_id = #{personId} OR t.tester_id = #{personId} OR t.reviewer_id = #{personId})
          AND t.task_datetime BETWEEN #{startTime} AND #{endTime}
          AND t.deleted = 0
          <if test="excludeTaskId != null">
              AND t.id != #{excludeTaskId}
          </if>
        ORDER BY t.task_datetime
    </select>

    <!-- 统计任务状态分布 -->
    <select id="selectTaskStatusStatistics" parameterType="java.lang.Long" resultType="java.util.Map">
        SELECT 
            status,
            COUNT(*) as count
        FROM assay_sampling_task
        WHERE factory_id = #{factoryId}
          AND deleted = 0
        GROUP BY status
    </select>

</mapper>
