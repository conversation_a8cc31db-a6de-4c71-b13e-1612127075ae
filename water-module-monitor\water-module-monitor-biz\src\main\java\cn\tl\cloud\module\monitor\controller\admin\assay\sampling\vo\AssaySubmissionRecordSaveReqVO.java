package cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;

@Schema(description = "管理后台 - 送检记录新增/修改 Request VO")
@Data
public class AssaySubmissionRecordSaveReqVO {

    @Schema(description = "记录ID", example = "1")
    private Long id;

    @Schema(description = "水厂ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "水厂ID不能为空")
    private Long factoryId;

    @Schema(description = "记录编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "SUB-001")
    @NotBlank(message = "记录编号不能为空")
    @Size(max = 50, message = "记录编号长度不能超过50个字符")
    private String recordCode;

    @Schema(description = "关联采样执行ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "关联采样执行ID不能为空")
    private Long executionId;

    @Schema(description = "送检日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-01-15")
    @NotNull(message = "送检日期不能为空")
    private LocalDate submissionDate;

    @Schema(description = "送检人员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    @NotNull(message = "送检人员ID不能为空")
    private Long submissionPersonId;

    @Schema(description = "检测项目ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "检测项目ID不能为空")
    private Long testItem;

    @Schema(description = "送检单文件URL", example = "http://example.com/file.pdf")
    @Size(max = 500, message = "送检单文件URL长度不能超过500个字符")
    private String fileUrl;

    @Schema(description = "备注信息", example = "正常送检")
    @Size(max = 1000, message = "备注信息长度不能超过1000个字符")
    private String remark;

}
