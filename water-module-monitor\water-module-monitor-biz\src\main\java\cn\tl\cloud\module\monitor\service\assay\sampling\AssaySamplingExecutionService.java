package cn.tl.cloud.module.monitor.service.assay.sampling;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySamplingExecutionPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySamplingExecutionSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySamplingExecutionDO;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 采样执行 Service 接口
 *
 * <AUTHOR>
 */
public interface AssaySamplingExecutionService {

    /**
     * 创建采样执行记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSamplingExecution(@Valid AssaySamplingExecutionSaveReqVO createReqVO);

    /**
     * 更新采样执行记录
     *
     * @param updateReqVO 更新信息
     */
    void updateSamplingExecution(@Valid AssaySamplingExecutionSaveReqVO updateReqVO);

    /**
     * 删除采样执行记录
     *
     * @param id 编号
     * @param factoryId 水厂ID
     */
    void deleteSamplingExecution(Long id, Long factoryId);

    /**
     * 获得采样执行记录
     *
     * @param id 编号
     * @param factoryId 水厂ID
     * @return 采样执行记录
     */
    AssaySamplingExecutionDO getSamplingExecution(Long id, Long factoryId);

    /**
     * 获得采样执行分页
     *
     * @param pageReqVO 分页查询
     * @return 采样执行分页
     */
    PageResult<AssaySamplingExecutionDO> getSamplingExecutionPage(AssaySamplingExecutionPageReqVO pageReqVO);

    /**
     * 获得采样执行列表
     *
     * @param reqVO 查询条件
     * @return 采样执行列表
     */
    List<AssaySamplingExecutionDO> getSamplingExecutionList(AssaySamplingExecutionPageReqVO reqVO);

    /**
     * 确认采样（开始采样）
     *
     * @param id 执行ID
     * @param factoryId 水厂ID
     * @param operatorId 操作人ID
     * @return 操作结果
     */
    Map<String, Object> confirmSampling(Long id, Long factoryId, Long operatorId);

    /**
     * 完成采样
     *
     * @param id 执行ID
     * @param factoryId 水厂ID
     * @param operatorId 操作人ID
     * @param executionData 执行数据
     * @return 操作结果
     */
    Map<String, Object> completeSampling(Long id, Long factoryId, Long operatorId, Map<String, Object> executionData);

    /**
     * 记录异常
     *
     * @param id 执行ID
     * @param factoryId 水厂ID
     * @param operatorId 操作人ID
     * @param abnormalReason 异常原因
     * @param handleMeasures 处理措施
     * @return 操作结果
     */
    Map<String, Object> recordAbnormal(Long id, Long factoryId, Long operatorId, 
                                      String abnormalReason, String handleMeasures);

    /**
     * 获取执行详情（包含任务信息）
     *
     * @param id 执行ID
     * @param factoryId 水厂ID
     * @return 执行详情
     */
    Map<String, Object> getExecutionDetail(Long id, Long factoryId);

    /**
     * 根据任务ID获取执行记录
     *
     * @param taskId 任务ID
     * @param factoryId 水厂ID
     * @return 执行记录
     */
    AssaySamplingExecutionDO getExecutionByTaskId(Long taskId, Long factoryId);

    /**
     * 获取执行状态统计
     *
     * @param factoryId 水厂ID
     * @return 状态统计
     */
    Map<String, Object> getExecutionStatistics(Long factoryId);

}
