<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.tl.cloud.module.monitor.dal.mysql.assay.baseInfo.AssayTestIndicatorMapper">
    <select id="selectListByProjectIds" resultType="cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestIndicatorDO">
        SELECT * FROM assay_test_indicator
        WHERE factory_id = #{factoryId}
        AND project_id IN
        <foreach collection="projectIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>