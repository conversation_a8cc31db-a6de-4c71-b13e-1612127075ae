<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.tl.cloud.module.report.dal.mysql.prodconsumptiondata.ProdConsumptionDataMapper">

    <!-- 查询原始数据表进行统计计算 -->
    <select id="selectDataFromDataTable" resultType="cn.tl.cloud.module.report.dal.dataobject.stat.ProdConsumptionDataDO">
        SELECT 
            id,
            date,
            factory_id,
            reporter_id,
            reviewer_id,
            review_status,
            prod_vol,
            in_flow_vol,
            treat_vol,
            elec_cons,
            elec_single_cons,
            carbon_usage,
            carbon_single_cons,
            sodium_hypo_usage,
            sodium_hypo_single_cons,
            pac_usage,
            pac_single_cons,
            ferric_sulf_usage,
            ferric_sulf_single_cons,
            naoh_usage,
            naoh_single_cons,
            an_pam_usage,
            an_pam_single_cons,
            cat_pam_usage,
            cat_pam_single_cons,
            sludge60_prod,
            sludge80_prod,
            dry_sludge_prod,
            sludge_rate,
            cat_pam_sludge_usage,
            cat_pam_sludge_single_cons,
            pac_sludge_usage,
            pac_sludge_single_cons,
            liq_iron_salt_usage,
            liq_iron_salt_single_cons,
            lime_usage,
            lime_single_cons,
            create_time,
            update_time,
            creator,
            updater,
            deleted
        FROM water_prod_consumption_data
        WHERE deleted = 0
<!--        AND review_status IN (1, 2)-->
        AND factory_id = #{factoryId}
        AND date BETWEEN #{startDate} AND #{endDate}
        ORDER BY date ASC
    </select>

    <!-- 查询聚合统计表 -->
    <select id="selectStatFromStatTable" resultType="java.util.Map">
        SELECT 
            <!-- 产量统计 -->
            ROUND(AVG(prod_vol_avg), 4) as prod_vol_avg,
            MAX(prod_vol_max) as prod_vol_max,
            MIN(prod_vol_min) as prod_vol_min,
            SUM(prod_vol_sum) as prod_vol_total,
            
            <!-- 进水水量统计 -->
            ROUND(AVG(in_flow_vol_avg), 4) as in_flow_vol_avg,
            MAX(in_flow_vol_max) as in_flow_vol_max,
            MIN(in_flow_vol_min) as in_flow_vol_min,
            SUM(in_flow_vol_sum) as in_flow_vol_total,
            
            <!-- 处理水量统计 -->
            ROUND(AVG(out_flow_vol_avg), 4) as treat_vol_avg,
            MAX(out_flow_vol_max) as treat_vol_max,
            MIN(out_flow_vol_min) as treat_vol_min,
            SUM(out_flow_vol_sum) as treat_vol_total,
            
            <!-- 电量消耗统计 -->
            ROUND(AVG(elec_cons_avg), 4) as elec_cons_avg,
            MAX(elec_cons_max) as elec_cons_max,
            MIN(elec_cons_min) as elec_cons_min,
            SUM(elec_cons_sum) as elec_cons_total,
            
            <!-- 电单耗统计 -->
            ROUND(AVG(elec_single_cons_avg), 4) as elec_single_cons_avg,
            MAX(elec_single_cons_max) as elec_single_cons_max,
            MIN(elec_single_cons_min) as elec_single_cons_min,
            SUM(elec_single_cons_sum) as elec_single_cons_total,
            
            <!-- 碳源用量统计 -->
            ROUND(AVG(carbon_usage_avg), 4) as carbon_usage_avg,
            MAX(carbon_usage_max) as carbon_usage_max,
            MIN(carbon_usage_min) as carbon_usage_min,
            SUM(carbon_usage_sum) as carbon_usage_total,
            
            <!-- 碳源单耗统计 -->
            ROUND(AVG(carbon_single_cons_avg), 4) as carbon_single_cons_avg,
            MAX(carbon_single_cons_max) as carbon_single_cons_max,
            MIN(carbon_single_cons_min) as carbon_single_cons_min,
            SUM(carbon_single_cons_sum) as carbon_single_cons_total,
            
            <!-- 次氯酸钠用量统计 -->
            ROUND(AVG(sodium_hypo_usage_avg), 4) as sodium_hypo_usage_avg,
            MAX(sodium_hypo_usage_max) as sodium_hypo_usage_max,
            MIN(sodium_hypo_usage_min) as sodium_hypo_usage_min,
            SUM(sodium_hypo_usage_sum) as sodium_hypo_usage_total,
            
            <!-- 次氯酸钠单耗统计 -->
            ROUND(AVG(sodium_hypo_single_cons_avg), 4) as sodium_hypo_single_cons_avg,
            MAX(sodium_hypo_single_cons_max) as sodium_hypo_single_cons_max,
            MIN(sodium_hypo_single_cons_min) as sodium_hypo_single_cons_min,
            SUM(sodium_hypo_single_cons_sum) as sodium_hypo_single_cons_total,
            
            <!-- PAC用量统计 -->
            ROUND(AVG(pac_usage_avg), 4) as pac_usage_avg,
            MAX(pac_usage_max) as pac_usage_max,
            MIN(pac_usage_min) as pac_usage_min,
            SUM(pac_usage_sum) as pac_usage_total,
            
            <!-- PAC单耗统计 -->
            ROUND(AVG(pac_single_cons_avg), 4) as pac_single_cons_avg,
            MAX(pac_single_cons_max) as pac_single_cons_max,
            MIN(pac_single_cons_min) as pac_single_cons_min,
            SUM(pac_single_cons_sum) as pac_single_cons_total,
            
            <!-- 聚合硫酸铁用量统计 -->
            ROUND(AVG(ferric_sulf_usage_avg), 4) as ferric_sulf_usage_avg,
            MAX(ferric_sulf_usage_max) as ferric_sulf_usage_max,
            MIN(ferric_sulf_usage_min) as ferric_sulf_usage_min,
            SUM(ferric_sulf_usage_sum) as ferric_sulf_usage_total,
            
            <!-- 聚合硫酸铁单耗统计 -->
            ROUND(AVG(ferric_sulf_single_cons_avg), 4) as ferric_sulf_single_cons_avg,
            MAX(ferric_sulf_single_cons_max) as ferric_sulf_single_cons_max,
            MIN(ferric_sulf_single_cons_min) as ferric_sulf_single_cons_min,
            SUM(ferric_sulf_single_cons_sum) as ferric_sulf_single_cons_total,
            
            <!-- 氢氧化钠用量统计 -->
            ROUND(AVG(naoh_usage_avg), 4) as naoh_usage_avg,
            MAX(naoh_usage_max) as naoh_usage_max,
            MIN(naoh_usage_min) as naoh_usage_min,
            SUM(naoh_usage_sum) as naoh_usage_total,
            
            <!-- 氢氧化钠单耗统计 -->
            ROUND(AVG(naoh_single_cons_avg), 4) as naoh_single_cons_avg,
            MAX(naoh_single_cons_max) as naoh_single_cons_max,
            MIN(naoh_single_cons_min) as naoh_single_cons_min,
            SUM(naoh_single_cons_sum) as naoh_single_cons_total,
            
            <!-- 阴离子PAM用量统计 -->
            ROUND(AVG(an_pam_usage_avg), 4) as an_pam_usage_avg,
            MAX(an_pam_usage_max) as an_pam_usage_max,
            MIN(an_pam_usage_min) as an_pam_usage_min,
            SUM(an_pam_usage_sum) as an_pam_usage_total,
            
            <!-- 阴离子PAM单耗统计 -->
            ROUND(AVG(an_pam_single_cons_avg), 4) as an_pam_single_cons_avg,
            MAX(an_pam_single_cons_max) as an_pam_single_cons_max,
            MIN(an_pam_single_cons_min) as an_pam_single_cons_min,
            SUM(an_pam_single_cons_sum) as an_pam_single_cons_total,
            
            <!-- 阳离子PAM用量统计 -->
            ROUND(AVG(cat_pam_usage_avg), 4) as cat_pam_usage_avg,
            MAX(cat_pam_usage_max) as cat_pam_usage_max,
            MIN(cat_pam_usage_min) as cat_pam_usage_min,
            SUM(cat_pam_usage_sum) as cat_pam_usage_total,
            
            <!-- 阳离子PAM单耗统计 -->
            ROUND(AVG(cat_pam_single_cons_avg), 4) as cat_pam_single_cons_avg,
            MAX(cat_pam_single_cons_max) as cat_pam_single_cons_max,
            MIN(cat_pam_single_cons_min) as cat_pam_single_cons_min,
            SUM(cat_pam_single_cons_sum) as cat_pam_single_cons_total,
            
            <!-- 60%污泥产量统计 -->
            ROUND(AVG(sludge60_prod_avg), 4) as sludge60_prod_avg,
            MAX(sludge60_prod_max) as sludge60_prod_max,
            MIN(sludge60_prod_min) as sludge60_prod_min,
            SUM(sludge60_prod_sum) as sludge60_prod_total,
            
            <!-- 80%污泥产量统计 -->
            ROUND(AVG(sludge80_prod_avg), 4) as sludge80_prod_avg,
            MAX(sludge80_prod_max) as sludge80_prod_max,
            MIN(sludge80_prod_min) as sludge80_prod_min,
            SUM(sludge80_prod_sum) as sludge80_prod_total,
            
            <!-- 绝干污泥产量统计 -->
            ROUND(AVG(dry_sludge_prod_avg), 4) as dry_sludge_prod_avg,
            MAX(dry_sludge_prod_max) as dry_sludge_prod_max,
            MIN(dry_sludge_prod_min) as dry_sludge_prod_min,
            SUM(dry_sludge_prod_sum) as dry_sludge_prod_total,
            
            <!-- 产泥率统计 -->
            ROUND(AVG(sludge_rate_avg), 4) as sludge_rate_avg,
            MAX(sludge_rate_max) as sludge_rate_max,
            MIN(sludge_rate_min) as sludge_rate_min,
            SUM(sludge_rate_sum) as sludge_rate_total,
            
            <!-- 污泥处理阳离子PAM用量统计 -->
            ROUND(AVG(cat_pam_sludge_usage_avg), 4) as cat_pam_sludge_usage_avg,
            MAX(cat_pam_sludge_usage_max) as cat_pam_sludge_usage_max,
            MIN(cat_pam_sludge_usage_min) as cat_pam_sludge_usage_min,
            SUM(cat_pam_sludge_usage_sum) as cat_pam_sludge_usage_total,
            
            <!-- 污泥处理阳离子PAM单耗统计 -->
            ROUND(AVG(cat_pam_sludge_single_cons_avg), 4) as cat_pam_sludge_single_cons_avg,
            MAX(cat_pam_sludge_single_cons_max) as cat_pam_sludge_single_cons_max,
            MIN(cat_pam_sludge_single_cons_min) as cat_pam_sludge_single_cons_min,
            SUM(cat_pam_sludge_single_cons_sum) as cat_pam_sludge_single_cons_total,
            
            <!-- 污泥处理PAC用量统计 -->
            ROUND(AVG(pac_sludge_usage_avg), 4) as pac_sludge_usage_avg,
            MAX(pac_sludge_usage_max) as pac_sludge_usage_max,
            MIN(pac_sludge_usage_min) as pac_sludge_usage_min,
            SUM(pac_sludge_usage_sum) as pac_sludge_usage_total,
            
            <!-- 污泥处理PAC单耗统计 -->
            ROUND(AVG(pac_sludge_single_cons_avg), 4) as pac_sludge_single_cons_avg,
            MAX(pac_sludge_single_cons_max) as pac_sludge_single_cons_max,
            MIN(pac_sludge_single_cons_min) as pac_sludge_single_cons_min,
            SUM(pac_sludge_single_cons_sum) as pac_sludge_single_cons_total,
            
            <!-- 液体铁盐用量统计 -->
            ROUND(AVG(liq_iron_salt_usage_avg), 4) as liq_iron_salt_usage_avg,
            MAX(liq_iron_salt_usage_max) as liq_iron_salt_usage_max,
            MIN(liq_iron_salt_usage_min) as liq_iron_salt_usage_min,
            SUM(liq_iron_salt_usage_sum) as liq_iron_salt_usage_total,
            
            <!-- 液体铁盐单耗统计 -->
            ROUND(AVG(liq_iron_salt_single_cons_avg), 4) as liq_iron_salt_single_cons_avg,
            MAX(liq_iron_salt_single_cons_max) as liq_iron_salt_single_cons_max,
            MIN(liq_iron_salt_single_cons_min) as liq_iron_salt_single_cons_min,
            SUM(liq_iron_salt_single_cons_sum) as liq_iron_salt_single_cons_total,
            
            <!-- 石灰用量统计 -->
            ROUND(AVG(lime_usage_avg), 4) as lime_usage_avg,
            MAX(lime_usage_max) as lime_usage_max,
            MIN(lime_usage_min) as lime_usage_min,
            SUM(lime_usage_sum) as lime_usage_total,
            
            <!-- 石灰单耗统计 -->
            ROUND(AVG(lime_single_cons_avg), 4) as lime_single_cons_avg,
            MAX(lime_single_cons_max) as lime_single_cons_max,
            MIN(lime_single_cons_min) as lime_single_cons_min,
            SUM(lime_single_cons_sum) as lime_single_cons_total
            
        FROM water_prod_consumption_stat
        WHERE factory_id = #{factoryId}
        AND stat_date BETWEEN #{startDate} AND #{endDate}
    </select>

</mapper>
