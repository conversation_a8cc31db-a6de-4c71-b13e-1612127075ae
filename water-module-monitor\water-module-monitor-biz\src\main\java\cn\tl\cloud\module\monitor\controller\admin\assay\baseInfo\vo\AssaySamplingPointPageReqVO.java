package cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo;

import cn.tl.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 采样点分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AssaySamplingPointPageReqVO extends PageParam {

    @Schema(description = "水厂ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long factoryId;

    @Schema(description = "采样点名称", example = "进水口1号")
    private String name;

    @Schema(description = "采样点类型", example = "inlet")
    private String type;

    @Schema(description = "启用状态", example = "true")
    private Boolean isEnabled;

    @Schema(description = "管理人ID", example = "1001")
    private Long managerId;

}
