package cn.tl.cloud.module.report.service.report.prodconsumptiondata;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.module.report.controller.admin.indicator.indicatordata.vo.IndicatorDataSaveReqVO;
import cn.tl.cloud.module.report.controller.admin.report.vo.consumption.*;
import cn.tl.cloud.module.report.dal.dataobject.stat.ProdConsumptionDataDO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

/**
 * 生产消耗数据表，记录每日的产量、能耗、原材料消耗和污泥处理情况 Service 接口
 *
 * <AUTHOR>
 */
public interface ProdConsumptionDataService extends IService<ProdConsumptionDataDO> {

    /**
     * 创建生产消耗数据表，记录每日的产量、能耗、原材料消耗和污泥处理情况
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProdConsumptionData(@Valid ProdConsumptionDataSaveReqVO createReqVO);

    /**
     * 更新生产消耗数据表，记录每日的产量、能耗、原材料消耗和污泥处理情况
     *
     * @param updateReqVO 更新信息
     */
    void updateProdConsumptionData(@Valid ProdConsumptionDataSaveReqVO updateReqVO);

    /**
     * 删除生产消耗数据表，记录每日的产量、能耗、原材料消耗和污泥处理情况
     *
     * @param id 编号
     */
    void deleteProdConsumptionData(Long id);

    /**
     * 获得生产消耗数据表，记录每日的产量、能耗、原材料消耗和污泥处理情况
     *
     * @param id 编号
     * @return 生产消耗数据表，记录每日的产量、能耗、原材料消耗和污泥处理情况
     */
    ProdConsumptionDataDO getProdConsumptionData(Long id);

    /**
     * 获得生产消耗数据表，记录每日的产量、能耗、原材料消耗和污泥处理情况分页
     *
     * @param pageReqVO 分页查询
     * @return 生产消耗数据表，记录每日的产量、能耗、原材料消耗和污泥处理情况分页
     */
    PageResult<ProdConsumptionDataDO> getProdConsumptionDataPage(ProdConsumptionDataPageReqVO pageReqVO);

    List<ProdConsumptionDataDO> getProdConsumptionDataList(MonthDataReqVO pageReqVO);

    List<Long> saveOrUpdateBatch(@Valid List<ProdConsumptionDataSaveReqVO> createReqVOList);



    /**
     * 查询所有水厂在指定日期的统计数据
     */
    List<WaterProdConsumptionStats> getAllFactoryStats(LocalDate startDate, LocalDate endDate);


    List<Long> insert(List<IndicatorDataSaveReqVO> indicatorDataSaveReqVOList);

    /**
     * 补录数据，不经过审核
     * @param createReqVOList
     * @return
     */
    List<Long> additionalRecording(List<ProdConsumptionDataSaveReqVO> createReqVOList);

    List<Long> saveTemporarily(@Valid List<ProdConsumptionDataSaveReqVO> createReqVOList);

    List<ProdConsumptionStatRespVO> getQualityReport(Long factoryId, LocalDate startDate, LocalDate endDate);
}