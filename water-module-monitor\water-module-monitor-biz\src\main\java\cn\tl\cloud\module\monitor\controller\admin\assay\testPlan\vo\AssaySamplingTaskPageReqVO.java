package cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo;

import cn.tl.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDate;

@Schema(description = "管理后台 - 采样任务分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AssaySamplingTaskPageReqVO extends PageParam {

    @Schema(description = "水厂ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long factoryId;

    @Schema(description = "计划ID", example = "1")
    private Long planId;

    @Schema(description = "任务状态", example = "pending")
    private String status;

    @Schema(description = "采样人员ID", example = "1001")
    private Long samplerId;

    @Schema(description = "开始日期", example = "2024-01-01")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-01-31")
    private LocalDate endDate;

}
