package cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 检测指标 Response VO")
@Data
public class AssayTestIndicatorRespVO {

    @Schema(description = "指标ID", example = "1")
    private Long id;

    @Schema(description = "水厂ID", example = "1")
    private Long factoryId;

    @Schema(description = "指标代码", example = "COD001")
    private String code;

    @Schema(description = "指标名称", example = "COD")
    private String name;

    @Schema(description = "所属项目ID", example = "1")
    private Long projectId;

    @Schema(description = "检测方法", example = "重铬酸钾法")
    private String method;

    @Schema(description = "数据单位", example = "mg/L")
    private String unit;

    @Schema(description = "标准值下限", example = "0.0")
    private BigDecimal standardMin;

    @Schema(description = "标准值上限", example = "50.0")
    private BigDecimal standardMax;

    @Schema(description = "检测仪器", example = "COD分析仪")
    private String equipment;

    @Schema(description = "精度要求", example = "±5%")
    private String precisionLimit;

    @Schema(description = "样品量(mL)", example = "100")
    private Integer sampleVolume;

    @Schema(description = "检测耗时(分钟)", example = "60")
    private Integer detectionTimeMinutes;

    @Schema(description = "启用状态", example = "true")
    private Boolean isEnabled;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
