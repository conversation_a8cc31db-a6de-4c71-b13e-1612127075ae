package cn.tl.cloud.module.monitor.service.assay.sampling;

import cn.tl.cloud.framework.common.exception.util.ServiceExceptionUtil;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySubmissionRecordPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySubmissionRecordSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySampleManagementDO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySamplingExecutionDO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySubmissionRecordDO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.testPlan.AssaySamplingTaskDO;
import cn.tl.cloud.module.monitor.dal.mysql.assay.sampling.AssaySampleManagementMapper;
import cn.tl.cloud.module.monitor.dal.mysql.assay.sampling.AssaySamplingExecutionMapper;
import cn.tl.cloud.module.monitor.dal.mysql.assay.sampling.AssaySubmissionRecordMapper;
import cn.tl.cloud.module.monitor.dal.mysql.assay.testPlan.AssaySamplingTaskMapper;
import cn.tl.cloud.module.monitor.enums.ErrorCodeConstants;
import cn.tl.cloud.module.monitor.service.assay.common.AssayOperationLogService;
import cn.tl.cloud.module.monitor.service.assay.testPlan.AssaySamplingTaskService;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 送检记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssaySubmissionRecordServiceImpl implements AssaySubmissionRecordService {

    @Resource
    private AssaySubmissionRecordMapper submissionRecordMapper;
    
    @Resource
    private AssaySamplingExecutionMapper samplingExecutionMapper;
    
    @Resource
    private AssaySamplingTaskMapper samplingTaskMapper;
    
    @Resource
    private AssaySampleManagementMapper sampleManagementMapper;
    
    @Resource
    private AssayOperationLogService operationLogService;
    
    @Resource
    private AssaySamplingTaskService samplingTaskService;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    public Long createSubmissionRecord(@Valid AssaySubmissionRecordSaveReqVO createReqVO) {
        // 校验执行记录存在
        validateExecutionExists(createReqVO.getExecutionId(), createReqVO.getFactoryId());
        
        // 校验记录编号唯一性
        validateRecordCodeUnique(createReqVO.getFactoryId(), createReqVO.getRecordCode(), null);
        
        // 校验该执行记录是否已存在送检记录
        AssaySubmissionRecordDO existingRecord = submissionRecordMapper.selectByExecutionId(createReqVO.getExecutionId());
        if (existingRecord != null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SUBMISSION_RECORD_ALREADY_EXISTS);
        }
        
        // 插入
        AssaySubmissionRecordDO submissionRecord = BeanUtils.toBean(createReqVO, AssaySubmissionRecordDO.class);
        submissionRecord.setStatus("unSubmitted"); // 默认状态为未送检
        submissionRecordMapper.insert(submissionRecord);
        
        // 记录操作日志
        try {
            operationLogService.recordLog(createReqVO.getFactoryId(), "assay_submission_record", 
                    submissionRecord.getId(), "create", "创建送检记录", 
                    null, null, objectMapper.writeValueAsString(submissionRecord));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        return submissionRecord.getId();
    }

    @Override
    public void updateSubmissionRecord(@Valid AssaySubmissionRecordSaveReqVO updateReqVO) {
        // 校验存在
        AssaySubmissionRecordDO oldRecord = validateSubmissionRecordExists(updateReqVO.getId(), updateReqVO.getFactoryId());
        
        // 校验执行记录存在
        validateExecutionExists(updateReqVO.getExecutionId(), updateReqVO.getFactoryId());
        
        // 校验记录编号唯一性
        validateRecordCodeUnique(updateReqVO.getFactoryId(), updateReqVO.getRecordCode(), updateReqVO.getId());
        
        // 更新
        AssaySubmissionRecordDO updateObj = BeanUtils.toBean(updateReqVO, AssaySubmissionRecordDO.class);
        submissionRecordMapper.updateById(updateObj);
        
        // 记录操作日志
        AssaySubmissionRecordDO newRecord = submissionRecordMapper.selectById(updateReqVO.getId());
        try {
            operationLogService.recordLog(updateReqVO.getFactoryId(), "assay_submission_record", 
                    updateReqVO.getId(), "update", "更新送检记录", 
                    null, objectMapper.writeValueAsString(oldRecord), objectMapper.writeValueAsString(newRecord));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void deleteSubmissionRecord(Long id, Long factoryId) {
        // 校验存在
        AssaySubmissionRecordDO record = validateSubmissionRecordExists(id, factoryId);
        
        // 删除
        submissionRecordMapper.deleteById(id);
        
        // 记录操作日志
        try {
            operationLogService.recordLog(factoryId, "assay_submission_record", 
                    id, "delete", "删除送检记录", 
                    null, objectMapper.writeValueAsString(record), null);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> completeSubmission(Long id, Long factoryId, Long operatorId, String fileUrl) {
        // 校验送检记录存在且状态为unSubmitted
        AssaySubmissionRecordDO record = validateSubmissionRecordExists(id, factoryId);
        if (!"unSubmitted".equals(record.getStatus())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SUBMISSION_RECORD_STATUS_ERROR);
        }
        
        String oldStatus = record.getStatus();
        
        // 1. 更新送检记录状态：unSubmitted -> submitted
        AssaySubmissionRecordDO updateObj = new AssaySubmissionRecordDO();
        updateObj.setId(id);
        updateObj.setStatus("submitted");
        updateObj.setFileUrl(fileUrl);
        submissionRecordMapper.updateById(updateObj);
        
        // 2. 获取关联的执行记录和任务
        AssaySamplingExecutionDO execution = samplingExecutionMapper.selectById(record.getExecutionId());
        if (execution != null) {
            AssaySamplingTaskDO task = samplingTaskMapper.selectById(execution.getTaskId());
            if (task != null) {
                // 3. 更新任务状态：submitted -> testing
                samplingTaskService.updateTaskStatus(task.getId(), "testing", "送检完成，开始检验");
                
                // 记录任务状态变更日志
                operationLogService.recordStatusChange(factoryId, "assay_sampling_task", 
                        task.getId(), "submitted", "testing", operatorId, "送检完成，开始检验");
            }
            
            // 4. 创建样品管理记录（状态为submitted）
            AssaySampleManagementDO sample = new AssaySampleManagementDO();
            sample.setFactoryId(factoryId);
            sample.setSampleCode(generateSampleCode(factoryId, execution.getId()));
            sample.setExecutionId(execution.getId());
            sample.setSampleType("water"); // 默认水样
            sample.setVolume(execution.getActualSampleQuantity() != null ? execution.getActualSampleQuantity() : 100);
            sample.setAppearance(execution.getActualSampleAppearance());
            sample.setPreservationMethod("refrigerated"); // 默认冷藏
            sample.setStorageLocation("待分配");
            sample.setExpiryDate(record.getSubmissionDate().plusDays(7)); // 默认7天有效期
            sample.setTestItem(record.getTestItem());
            sample.setSamplingPersonId(record.getSubmissionPersonId());
            sample.setSamplingDate(record.getSubmissionDate());
            sample.setStatus("submitted"); // 状态为已送检
            sample.setRemark("送检完成自动创建");
            sampleManagementMapper.insert(sample);
            
            // 记录样品管理创建日志
            try {
                operationLogService.recordLog(factoryId, "assay_sample_management", 
                        sample.getId(), "create", "送检完成自动创建样品管理记录", 
                        operatorId, null, objectMapper.writeValueAsString(sample));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
        
        // 5. 记录送检记录状态变更日志
        operationLogService.recordStatusChange(factoryId, "assay_submission_record", 
                id, oldStatus, "submitted", operatorId, "完成送检");
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("submissionRecordId", id);
        result.put("status", "submitted");
        result.put("fileUrl", fileUrl);
        result.put("completeTime", LocalDateTime.now());
        result.put("message", "送检完成，已自动创建样品管理记录并更新任务状态");
        
        return result;
    }

    private AssaySubmissionRecordDO validateSubmissionRecordExists(Long id, Long factoryId) {
        AssaySubmissionRecordDO record = submissionRecordMapper.selectById(id);
        if (record == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SUBMISSION_RECORD_NOT_EXISTS);
        }
        if (!record.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FACTORY_DATA_ACCESS_DENIED);
        }
        return record;
    }

    private void validateExecutionExists(Long executionId, Long factoryId) {
        AssaySamplingExecutionDO execution = samplingExecutionMapper.selectById(executionId);
        if (execution == null || !execution.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SUBMISSION_RECORD_EXECUTION_NOT_EXISTS);
        }
    }

    private void validateRecordCodeUnique(Long factoryId, String recordCode, Long id) {
        AssaySubmissionRecordDO record = submissionRecordMapper.selectByFactoryIdAndRecordCode(factoryId, recordCode);
        if (record == null) {
            return;
        }
        if (id == null || !record.getId().equals(id)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SUBMISSION_RECORD_CODE_DUPLICATE);
        }
    }

    private String generateSampleCode(Long factoryId, Long executionId) {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return String.format("SAMPLE-%d-%s-%03d", factoryId, dateStr, 
            (int)(System.currentTimeMillis() % 1000));
    }

    @Override
    public AssaySubmissionRecordDO getSubmissionRecord(Long id, Long factoryId) {
        return validateSubmissionRecordExists(id, factoryId);
    }

    @Override
    public PageResult<AssaySubmissionRecordDO> getSubmissionRecordPage(AssaySubmissionRecordPageReqVO pageReqVO) {
        return submissionRecordMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AssaySubmissionRecordDO> getSubmissionRecordList(AssaySubmissionRecordPageReqVO reqVO) {
        return submissionRecordMapper.selectList(reqVO);
    }

    @Override
    public Map<String, Object> getSubmissionDetail(Long id, Long factoryId) {
        // 校验送检记录存在
        AssaySubmissionRecordDO record = validateSubmissionRecordExists(id, factoryId);
        
        // TODO: 查询详细信息（包含执行信息、状态历史等）
        Map<String, Object> result = new HashMap<>();
        result.put("submissionInfo", record);
        result.put("statusHistory", operationLogService.getStatusChangeHistory(factoryId, "assay_submission_record", id));
        
        return result;
    }

    @Override
    public List<AssaySubmissionRecordDO> getPendingSubmissions(Long factoryId) {
        return submissionRecordMapper.selectByStatus(factoryId, "unSubmitted");
    }

    @Override
    public Map<String, Object> getSubmissionStatistics(Long factoryId) {
        Map<String, Object> result = new HashMap<>();
        result.put("unSubmittedCount", submissionRecordMapper.selectCountByStatus(factoryId, "unSubmitted"));
        result.put("submittedCount", submissionRecordMapper.selectCountByStatus(factoryId, "submitted"));
        
        return result;
    }

}
