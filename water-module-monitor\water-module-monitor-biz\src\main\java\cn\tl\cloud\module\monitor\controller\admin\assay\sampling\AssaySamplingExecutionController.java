package cn.tl.cloud.module.monitor.controller.admin.assay.sampling;

import cn.tl.cloud.framework.common.pojo.CommonResult;
import cn.tl.cloud.framework.common.pojo.PageParam;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.framework.excel.core.util.ExcelUtils;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySamplingExecutionPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySamplingExecutionRespVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.sampling.vo.AssaySamplingExecutionSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySamplingExecutionDO;
import cn.tl.cloud.module.monitor.service.assay.sampling.AssaySamplingExecutionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static cn.tl.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 采样执行")
@RestController
@RequestMapping("/monitor/assay-sampling-execution")
@Validated
public class AssaySamplingExecutionController {

    @Resource
    private AssaySamplingExecutionService samplingExecutionService;

    @PostMapping("/create")
    @Operation(summary = "创建采样执行记录")
    public CommonResult<Long> createSamplingExecution(@Valid @RequestBody AssaySamplingExecutionSaveReqVO createReqVO) {
        return success(samplingExecutionService.createSamplingExecution(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新采样执行记录")
    public CommonResult<Boolean> updateSamplingExecution(@Valid @RequestBody AssaySamplingExecutionSaveReqVO updateReqVO) {
        samplingExecutionService.updateSamplingExecution(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除采样执行记录")
    @Parameter(name = "id", description = "编号", required = true)
    @Parameter(name = "factoryId", description = "水厂ID", required = true)
    public CommonResult<Boolean> deleteSamplingExecution(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        samplingExecutionService.deleteSamplingExecution(id, factoryId);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得采样执行记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    public CommonResult<AssaySamplingExecutionRespVO> getSamplingExecution(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        AssaySamplingExecutionDO samplingExecution = samplingExecutionService.getSamplingExecution(id, factoryId);
        return success(BeanUtils.toBean(samplingExecution, AssaySamplingExecutionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得采样执行分页")
    public CommonResult<PageResult<AssaySamplingExecutionRespVO>> getSamplingExecutionPage(@Valid AssaySamplingExecutionPageReqVO pageReqVO) {
        PageResult<AssaySamplingExecutionDO> pageResult = samplingExecutionService.getSamplingExecutionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssaySamplingExecutionRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得采样执行列表")
    public CommonResult<List<AssaySamplingExecutionRespVO>> getSamplingExecutionList(@Valid AssaySamplingExecutionPageReqVO reqVO) {
        List<AssaySamplingExecutionDO> list = samplingExecutionService.getSamplingExecutionList(reqVO);
        return success(BeanUtils.toBean(list, AssaySamplingExecutionRespVO.class));
    }

    @PostMapping("/confirm-sampling")
    @Operation(summary = "确认采样（开始采样）")
    public CommonResult<Map<String, Object>> confirmSampling(@RequestParam("id") Long id,
                                                            @RequestParam("factoryId") Long factoryId,
                                                            @RequestParam("operatorId") Long operatorId) {
        Map<String, Object> result = samplingExecutionService.confirmSampling(id, factoryId, operatorId);
        return success(result);
    }

    @PostMapping("/complete-sampling")
    @Operation(summary = "完成采样")
    public CommonResult<Map<String, Object>> completeSampling(@RequestParam("id") Long id,
                                                             @RequestParam("factoryId") Long factoryId,
                                                             @RequestParam("operatorId") Long operatorId,
                                                             @RequestBody(required = false) Map<String, Object> executionData) {
        Map<String, Object> result = samplingExecutionService.completeSampling(id, factoryId, operatorId, executionData);
        return success(result);
    }

    @PostMapping("/record-abnormal")
    @Operation(summary = "记录异常")
    public CommonResult<Map<String, Object>> recordAbnormal(@RequestParam("id") Long id,
                                                           @RequestParam("factoryId") Long factoryId,
                                                           @RequestParam("operatorId") Long operatorId,
                                                           @RequestParam("abnormalReason") String abnormalReason,
                                                           @RequestParam(value = "handleMeasures", required = false) String handleMeasures) {
        Map<String, Object> result = samplingExecutionService.recordAbnormal(id, factoryId, operatorId, abnormalReason, handleMeasures);
        return success(result);
    }

    @GetMapping("/detail")
    @Operation(summary = "获取执行详情")
    @Parameter(name = "id", description = "执行ID", required = true, example = "1")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    public CommonResult<Map<String, Object>> getExecutionDetail(@RequestParam("id") Long id, @RequestParam("factoryId") Long factoryId) {
        Map<String, Object> result = samplingExecutionService.getExecutionDetail(id, factoryId);
        return success(result);
    }

    @GetMapping("/by-task")
    @Operation(summary = "根据任务ID获取执行记录")
    @Parameter(name = "taskId", description = "任务ID", required = true, example = "1")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    public CommonResult<AssaySamplingExecutionRespVO> getExecutionByTaskId(@RequestParam("taskId") Long taskId, @RequestParam("factoryId") Long factoryId) {
        AssaySamplingExecutionDO execution = samplingExecutionService.getExecutionByTaskId(taskId, factoryId);
        return success(BeanUtils.toBean(execution, AssaySamplingExecutionRespVO.class));
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取执行状态统计")
    @Parameter(name = "factoryId", description = "水厂ID", required = true, example = "1")
    public CommonResult<Map<String, Object>> getExecutionStatistics(@RequestParam("factoryId") Long factoryId) {
        Map<String, Object> result = samplingExecutionService.getExecutionStatistics(factoryId);
        return success(result);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出采样执行 Excel")
    public void exportSamplingExecutionExcel(@Valid AssaySamplingExecutionPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AssaySamplingExecutionDO> list = samplingExecutionService.getSamplingExecutionList(pageReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "采样执行.xls", "数据", AssaySamplingExecutionRespVO.class,
                        BeanUtils.toBean(list, AssaySamplingExecutionRespVO.class));
    }

}
