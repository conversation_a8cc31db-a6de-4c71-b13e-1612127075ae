package cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Schema(description = "管理后台 - 任务生成 Request VO")
@Data
public class AssayTaskGenerateReqVO {

    @Schema(description = "水厂ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "水厂ID不能为空")
    private Long factoryId;

    @Schema(description = "计划ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "计划ID不能为空")
    private Long planId;

    @Schema(description = "生成类型", example = "auto")
    private String generateType;

    @Schema(description = "开始日期", example = "2024-01-01")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-01-31")
    private LocalDate endDate;

}
