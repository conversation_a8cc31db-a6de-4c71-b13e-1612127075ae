package cn.tl.cloud.module.monitor.controller.admin.acqconf;

import cn.tl.cloud.framework.common.pojo.CommonResult;
import cn.tl.cloud.module.monitor.service.acqconf.IotService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 *
 */
@RestController
@RequestMapping("/acq-conf/iot-group")
public class IotGroupController {

    @Resource
    private IotService iotService;


    /**
     * 查询所有分组/厂站列表
     * @return
     */
    @GetMapping("/list")
    public CommonResult list() {
        return CommonResult.success(iotService.listGroup());
    }



}
