package cn.tl.cloud.module.monitor.service.monitor.alarm.event;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;

import cn.tl.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.event.vo.EventPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.event.vo.EventRespVO;
import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.event.vo.EventSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.monitor.alarm.event.EventDO;
import cn.tl.cloud.module.monitor.dal.mysql.monitor.alarm.event.EventMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.util.Collections;
import java.util.List;

import static cn.tl.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.tl.cloud.module.monitor.enums.ErrorCodeConstants.EVENT_NOT_EXISTS;


/**
 * 告警事件 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EventServiceImpl implements EventService {

    @Resource
    private EventMapper eventMapper;

    @Override
    public Long createEvent(EventSaveReqVO createReqVO) {
        // 插入
        EventDO event = BeanUtils.toBean(createReqVO, EventDO.class);
        eventMapper.insert(event);
        // 返回
        return event.getId();
    }

    @Override
    public void updateEvent(EventSaveReqVO updateReqVO) {
        // 校验存在
        validateEventExists(updateReqVO.getId());
        // 更新
        EventDO updateObj = BeanUtils.toBean(updateReqVO, EventDO.class);
        eventMapper.updateById(updateObj);
    }

    @Override
    public void deleteEvent(Long id) {
        // 校验存在
        validateEventExists(id);
        // 删除
        eventMapper.deleteById(id);
    }

    private void validateEventExists(Long id) {
        if (eventMapper.selectById(id) == null) {
            throw exception(EVENT_NOT_EXISTS);
        }
    }

    @Override
    public EventDO getEvent(Long id) {
        return eventMapper.selectById(id);
    }

    @Override
    public PageResult<EventDO> getEventPage(EventPageReqVO pageReqVO) {
        return eventMapper.selectPage(pageReqVO);
    }

    @Override
    public List<EventRespVO> getEventList(EventPageReqVO reqVO) {
        LambdaQueryWrapperX<EventDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<EventDO>()
                .eqIfPresent(EventDO::getRuleId, reqVO.getRuleId())
                .likeIfPresent(EventDO::getRuleName, reqVO.getRuleName())
                .eqIfPresent(EventDO::getFactoryId, reqVO.getFactoryId())
                .likeIfPresent(EventDO::getFactoryName, reqVO.getFactoryName())
                .eqIfPresent(EventDO::getDeviceId, reqVO.getDeviceId())
                .likeIfPresent(EventDO::getDeviceName, reqVO.getDeviceName())
                .betweenIfPresent(EventDO::getAlarmTime, reqVO.getAlarmTime())
                .eqIfPresent(EventDO::getFactorCode, reqVO.getFactorCode())
                .eqIfPresent(EventDO::getFactorType, reqVO.getFactorType())
                .likeIfPresent(EventDO::getFactorName, reqVO.getFactorName())
                .eqIfPresent(EventDO::getFactorValue, reqVO.getFactorValue())
                .eqIfPresent(EventDO::getRuleType, reqVO.getRuleType())
                .eqIfPresent(EventDO::getOperator, reqVO.getOperator())
                .eqIfPresent(EventDO::getThreshold, reqVO.getThreshold())
                .eqIfPresent(EventDO::getUnit, reqVO.getUnit())
                .eqIfPresent(EventDO::getExpression, reqVO.getExpression())
                .eqIfPresent(EventDO::getLevel, reqVO.getLevel())
                .eqIfPresent(EventDO::getStatus, reqVO.getStatus())
                .eqIfPresent(EventDO::getHandler, reqVO.getHandler())
                .betweenIfPresent(EventDO::getProcessTime, reqVO.getProcessTime())
                .eqIfPresent(EventDO::getNotifyStatus, reqVO.getNotifyStatus())
                .eqIfPresent(EventDO::getNotifyRecords, reqVO.getNotifyRecords())
                .betweenIfPresent(EventDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(EventDO::getId);
        List<EventRespVO> eventDOS = eventMapper.queryList(lambdaQueryWrapperX);
        return eventDOS;
    }

}
