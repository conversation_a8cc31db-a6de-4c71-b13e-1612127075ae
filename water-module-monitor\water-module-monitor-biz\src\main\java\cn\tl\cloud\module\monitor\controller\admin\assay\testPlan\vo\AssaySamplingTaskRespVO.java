package cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 采样任务 Response VO")
@Data
public class AssaySamplingTaskRespVO {

    @Schema(description = "任务ID", example = "1")
    private Long id;

    @Schema(description = "水厂ID", example = "1")
    private Long factoryId;

    @Schema(description = "任务编号", example = "TASK-1-20250718-001")
    private String taskCode;

    @Schema(description = "计划ID", example = "1")
    private Long planId;

    @Schema(description = "计划名称", example = "进水水质日常监测计划")
    private String planName;

    @Schema(description = "任务执行时间", example = "2025-07-18T08:00:00")
    private LocalDateTime taskDatetime;

    @Schema(description = "检测项目ID", example = "1")
    private Long testItem;

    @Schema(description = "检测项目名称", example = "COD")
    private String testItemName;

    @Schema(description = "采样点ID", example = "1")
    private Long samplingPoint;

    @Schema(description = "采样点名称", example = "进水口1号")
    private String samplingPointName;

    @Schema(description = "采样人员ID", example = "1001")
    private Long samplerId;

    @Schema(description = "采样人员姓名", example = "张三")
    private String samplerName;

    @Schema(description = "检测人员ID", example = "1002")
    private Long testerId;

    @Schema(description = "检测人员姓名", example = "李四")
    private String testerName;

    @Schema(description = "审核人员ID", example = "1003")
    private Long reviewerId;

    @Schema(description = "审核人员姓名", example = "王五")
    private String reviewerName;

    @Schema(description = "优先级", example = "normal")
    private String priority;

    @Schema(description = "预期样品量(mL)", example = "100")
    private Integer expectedSampleQuantity;

    @Schema(description = "预期样品性质", example = "liquid")
    private String expectedSampleNature;

    @Schema(description = "预期样品外观", example = "微黄色，略浑浊")
    private String expectedSampleAppearance;

    @Schema(description = "预期上清液情况", example = "清澈")
    private String expectedSupernatant;

    @Schema(description = "采样说明", example = "采样前需要冲洗采样瓶3次")
    private String samplingInstructions;

    @Schema(description = "任务状态", example = "pending")
    private String status;

    @Schema(description = "开始时间", example = "2024-01-15T08:00:00")
    private LocalDateTime startTime;

    @Schema(description = "完成时间", example = "2024-01-15T10:00:00")
    private LocalDateTime completeTime;

    @Schema(description = "备注", example = "正常执行")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
