package cn.tl.cloud.module.report.service.report.prodconsumptiondata;

import cn.hutool.core.lang.Pair;
import cn.tl.cloud.framework.common.exception.ErrorCode;
import cn.tl.cloud.framework.common.exception.ServiceException;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.framework.security.core.util.SecurityFrameworkUtils;
import cn.tl.cloud.module.report.controller.admin.indicator.indicatordata.vo.IndicatorDataSaveReqVO;
import cn.tl.cloud.module.report.controller.admin.report.vo.consumption.*;
import cn.tl.cloud.module.report.dal.dataobject.factory.FactoryDO;
import cn.tl.cloud.module.report.dal.dataobject.stat.ProdConsumptionDataDO;
import cn.tl.cloud.module.report.dal.mysql.indicator.IndicatorReviewFlowMapper;
import cn.tl.cloud.module.report.dal.mysql.prodconsumptiondata.ProdConsumptionDataMapper;
import cn.tl.cloud.module.report.service.factory.FactoryService;
import cn.tl.cloud.module.report.service.indicator.IndicatorDataService;
import cn.tl.cloud.module.system.api.permission.PermissionApi;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.tl.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.tl.cloud.module.report.common.contants.FactoryConstant.REPORT_MODULE_CODE;
import static cn.tl.cloud.module.report.common.contants.FactoryConstant.REPORT_ROLE_CODE_FILLER;
import static cn.tl.cloud.module.report.enums.ErrorCodeConstants.PROD_CONSUMPTION_DATA_NOT_EXISTS;
import static cn.tl.cloud.module.report.utils.BeanPropertiesCopyUtil.copyNonNullProperties;

/**
 * 生产消耗数据表，记录每日的产量、能耗、原材料消耗和污泥处理情况 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProdConsumptionDataServiceImpl extends ServiceImpl<ProdConsumptionDataMapper, ProdConsumptionDataDO> implements ProdConsumptionDataService {

    @Resource
    private ProdConsumptionDataMapper prodConsumptionDataMapper;


    @Resource
    private FactoryService factoryService;

    @Resource
    private IndicatorDataService indicatorDataService;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private IndicatorReviewFlowMapper indicatorReviewFlowMapper;

    private void setProdVal(ProdConsumptionDataSaveReqVO reqVO){
        reqVO.setProdVol(reqVO.getTreatVol());
    }

    private void volDivide(ProdConsumptionDataSaveReqVO reqVO) {
        reqVO.setInFlowVol(safeDivide(reqVO.getInFlowVol(), BigDecimal.valueOf(1000)));
        reqVO.setTreatVol(safeDivide(reqVO.getTreatVol(), BigDecimal.valueOf(1000)));
        reqVO.setProdVol(safeDivide(reqVO.getProdVol(), BigDecimal.valueOf(1000)));
        //if (Objects.isNull(treatVol)) {
        //    throw new ServiceException(50001, "处理水量不能为空");
        //}
        //
        //if (treatVol.compareTo(BigDecimal.ZERO) == 0) {
        //    throw new ServiceException(50002, "处理水量为 0 ");
        //}
    }

    private void calcIndicatorAvg(ProdConsumptionDataSaveReqVO reqVO) {
        // 由m³转km³
        setSingleConsumption(reqVO, reqVO.getProdVol());
    }

    private void setSingleConsumption(ProdConsumptionDataSaveReqVO reqVO, BigDecimal prodVol) {
        // 1. 电单耗
        reqVO.setElecSingleCons(safeDivide(reqVO.getElecCons(), prodVol));
        // 2. 原材料消耗
        // 碳源
        reqVO.setCarbonSingleCons(safeDivide(reqVO.getCarbonUsage(), prodVol));
        // 次氯酸钠
        reqVO.setSodiumHypoSingleCons(safeDivide(reqVO.getSodiumHypoUsage(), prodVol));
        // 聚合氯化铝
        reqVO.setPacSingleCons(safeDivide(reqVO.getPacUsage(), prodVol));
        // 聚合硫酸铁
        reqVO.setFerricSulfSingleCons(safeDivide(reqVO.getFerricSulfUsage(), prodVol));
        // 氢氧化纳
        reqVO.setNaohSingleCons(safeDivide(reqVO.getNaohUsage(), prodVol));
        // 固体聚丙烯酰胺（阴离子）
        reqVO.setAnPamSingleCons(safeDivide(reqVO.getAnPamUsage(), prodVol));
        // 固体聚丙烯酰胺（阳离子）
        reqVO.setCatPamSingleCons(safeDivide(reqVO.getCatPamUsage(), prodVol));

        // 产泥率计算
        // 1. sludge60Prod、sludge80Prod 两者有一个有值就进行计算
        BigDecimal sludge60Prod = reqVO.getSludge60Prod();
        BigDecimal sludge80Prod = reqVO.getSludge80Prod();


        // 如果至少有一个污泥产量不为null，进行计算
        if (sludge60Prod != null || sludge80Prod != null) {
            // 将null值替换为0
            sludge60Prod = sludge60Prod == null ? BigDecimal.ZERO : sludge60Prod;
            sludge80Prod = sludge80Prod == null ? BigDecimal.ZERO : sludge80Prod;

            // 如果处理水量为null或为0，直接设置污泥率为0
            if (prodVol == null || prodVol.compareTo(BigDecimal.ZERO) == 0) {
                reqVO.setSludgeRate(BigDecimal.ZERO);
            } else {
                // 计算污泥率
                reqVO.setSludgeRate(safeDivide(sludge60Prod.add(sludge80Prod), prodVol.divide(BigDecimal.TEN)));
            }
            // 计算绝干污泥产量：sludge60Prod * 0.4 + sludge80Prod * 0.2
            BigDecimal drySludgeProd = sludge60Prod.multiply(new BigDecimal("0.4"))
                    .add(sludge80Prod.multiply(new BigDecimal("0.2")));

            reqVO.setDrySludgeProd(safeDivide(drySludgeProd, BigDecimal.ONE));
        } else {
            // 如果两个污泥产量都为null，设置默认值
            reqVO.setSludgeRate(BigDecimal.ZERO);
            reqVO.setDrySludgeProd(BigDecimal.ZERO);
        }

        // 污泥
        // 污泥固体聚丙烯酰胺（阳离子）
        reqVO.setCatPamSludgeSingleCons(safeDivide(reqVO.getCatPamSludgeUsage(), prodVol));
        // 污泥聚合氯化铝
        reqVO.setPacSludgeSingleCons(safeDivide(reqVO.getPacSludgeUsage(), prodVol));
        // 污泥液体铁盐
        reqVO.setLiqIronSaltSingleCons(safeDivide(reqVO.getLiqIronSaltUsage(), prodVol));
        // 石灰
        reqVO.setLimeSingleCons(safeDivide(reqVO.getLimeUsage(), prodVol));
    }

    private static BigDecimal safeDivide(BigDecimal a, BigDecimal b) {
        if (a == null || b == null || BigDecimal.ZERO.compareTo(b) == 0) {
            return BigDecimal.ZERO;
        }
        return a.divide(b, 4, BigDecimal.ROUND_HALF_UP);
    }

    @Override
    public Long createProdConsumptionData(ProdConsumptionDataSaveReqVO createReqVO) {
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        Long factoryId = createReqVO.getFactoryId();
        // 权限校验
        hasFactoryOperationPermission(currentUserId,factoryId);

        setProdVal(createReqVO);
        volDivide(createReqVO);
        calcIndicatorAvg(createReqVO);

        // 插入
        ProdConsumptionDataDO prodConsumptionData = BeanUtils.toBean(createReqVO, ProdConsumptionDataDO.class);

        prodConsumptionDataMapper.insert(prodConsumptionData);
        // 返回
        return prodConsumptionData.getId();
    }

    @Override
    public void updateProdConsumptionData(ProdConsumptionDataSaveReqVO updateReqVO) {

        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        Long factoryId = updateReqVO.getFactoryId();
        // 权限校验
        hasFactoryOperationPermission(currentUserId,factoryId);

        // 校验存在
        validateProdConsumptionDataExists(updateReqVO.getId());

        // 数据库查完整对象
        ProdConsumptionDataDO dbData = prodConsumptionDataMapper.selectById(updateReqVO.getId());
        ProdConsumptionDataSaveReqVO mergedReqVO = BeanUtils.toBean(dbData, ProdConsumptionDataSaveReqVO.class);

        // 合并非空字段
        copyNonNullProperties(updateReqVO, mergedReqVO);

        // 重新计算指标
        setProdVal(mergedReqVO);
        volDivide(mergedReqVO);
        calcIndicatorAvg(mergedReqVO);

        // 更新
        ProdConsumptionDataDO updateObj = BeanUtils.toBean(updateReqVO, ProdConsumptionDataDO.class);


        prodConsumptionDataMapper.updateById(updateObj);
    }

    @Override
    public void deleteProdConsumptionData(Long id) {
        // 校验存在
        validateProdConsumptionDataExists(id);
        // 删除
        prodConsumptionDataMapper.deleteById(id);
    }

    private void validateProdConsumptionDataExists(Long id) {
        if (prodConsumptionDataMapper.selectById(id) == null) {
            throw exception(PROD_CONSUMPTION_DATA_NOT_EXISTS);
        }
    }

    @Override
    public ProdConsumptionDataDO getProdConsumptionData(Long id) {
        return prodConsumptionDataMapper.selectById(id);
    }

    @Override
    public PageResult<ProdConsumptionDataDO> getProdConsumptionDataPage(ProdConsumptionDataPageReqVO pageReqVO) {
        return prodConsumptionDataMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ProdConsumptionDataDO> getProdConsumptionDataList(MonthDataReqVO pageReqVO) {
        // 解析月份（如 "2025-04"）获取起止日期
        String month = pageReqVO.getMonth(); // 格式为 yyyy-MM
        LocalDate startDate = LocalDate.parse(month + "-01");
        LocalDate endDate = startDate.with(TemporalAdjusters.lastDayOfMonth());


        // 查询数据
        return prodConsumptionDataMapper.selectList(
                Wrappers.lambdaQuery(ProdConsumptionDataDO.class)
                        .eq(ProdConsumptionDataDO::getFactoryId, pageReqVO.getFactoryId())
                        .between(ProdConsumptionDataDO::getDate, startDate, endDate)
                        .orderByAsc(ProdConsumptionDataDO::getDate)
        );
    }

    /**
     * 添加审核逻辑后，界面回显应该能看到数据，且审核通过或驳回的数据才能修改
     * 若未经审核，修改时应提示待审核数据，避免数据重复提交
     *
     * @param reqList
     * @return
     */
    @Override
    @Transactional
    public List<Long> saveOrUpdateBatch(List<ProdConsumptionDataSaveReqVO> reqList) {
        if (CollectionUtils.isEmpty(reqList)) {
            return Collections.emptyList();
        }

        // 获取当前用户信息
        /*填写或审核均会经过此方法
                填写时，填写人id在此处不处理,在indicatorData相关方法中写入
                审核时，indicatorData相关方法构造的reqList中的每个对象有填写人id；审核人id将在归档时写入
                故当宽表中填写人id为空时，审核人id就是填写人id*/
        //或者填写人id在前端赋值
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        Long factoryId = reqList.get(0).getFactoryId();
        // 默认审核阶段为 1（初审）
        int flowStage = 1;

        // 权限校验
        boolean isAdmin = hasFactoryOperationPermission(currentUserId, factoryId);


        // 判断是否已审核
        boolean alreadyReviewed = reqList.get(0).getReviewStatus() == 1;
        // 是否直接归档
        // 如果是管理员或者已经审核就直接归档
        boolean shouldArchive = isAdmin || alreadyReviewed;

        // 如果是管理员或审核流程走完，数据直接归档到宽表[审核状态为已审核]
        // 设置审核状态和审核人
        boolean finalIsAdmin = isAdmin;
        reqList.forEach(vo -> {
            vo.setReviewStatus(shouldArchive ? 1 : 0);
            if (finalIsAdmin) {
                vo.setReviewerId(currentUserId);
            }
        });

        if (isAdmin) {
            flowStage = 4; // 管理员归档阶段
        } else if (alreadyReviewed) {
            flowStage = 2; // 普通用户归档阶段
        }

        // 存入宽表
        List<Long> resultIds = saveUpdateWideTable(reqList);

        //避免审核通过二次创建
        /*什么时候走这个逻辑
         *   仅当所有用户第一次提交【】
         * */
        // 若为首次提交或当前用户具有管理员权限，则生成并保存指标数据
        Integer reviewStatus = reqList.get(0).getReviewStatus();
        boolean isFirstSubmission = reviewStatus == 0;
        boolean reporterIsAdmin = Boolean.TRUE.equals(
                permissionApi.hasAnyRoles(reqList.get(0).getReporterId(), "report_admin", "super_admin").getData());
        if (isFirstSubmission || reporterIsAdmin) {
            reviewStatus = (reviewStatus == 0) ? 2 : 3;
            List<IndicatorDataSaveReqVO> indicatorDataList = createIndicatorDataByReportData(reqList, shouldArchive ? 1 : 0,
                    reviewStatus);

            // 2. 调用指标数据服务批量保存
            if (!indicatorDataList.isEmpty()) {
                return indicatorDataService.saveOrUpdateBatch(indicatorDataList, flowStage);
            }
        }
        return resultIds;
    }


    /**
     *
     * @param reqList
     * @return
     */
    public List<Long> saveUpdateWideTable(List<ProdConsumptionDataSaveReqVO> reqList) {
        if (CollectionUtils.isEmpty(reqList)) {
            return Collections.emptyList();
        }

        // 是否是非管理员并且是已审核数据
        boolean isNotAdmin = !permissionApi.hasAnyPermissions(reqList.get(0).getReporterId(), "report_admin", "super_admin").getData();
        boolean isApproved = reqList.get(0).getReviewStatus() == 1;

        reqList.forEach(this::setProdVal);
        // 计算产量
        if (!(isNotAdmin && isApproved)) {
            reqList.forEach(this::volDivide);
        }
        reqList.forEach(this::calcIndicatorAvg);


        // 组装 key：factoryId + date
        List<Pair<Long, LocalDate>> keys = reqList.stream()
                .map(vo -> Pair.of(vo.getFactoryId(), vo.getDate()))
                .collect(Collectors.toList());

        // 查询已有数据
        LambdaQueryWrapper<ProdConsumptionDataDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(wrapper -> {
            for (Pair<Long, LocalDate> key : keys) {
                wrapper.or(w -> w.eq(ProdConsumptionDataDO::getFactoryId, key.getKey())
                        .eq(ProdConsumptionDataDO::getDate, key.getValue()));
            }
        });

        List<ProdConsumptionDataDO> existingList = prodConsumptionDataMapper.selectList(queryWrapper);

        // 构建已存在的数据映射
        Map<String, ProdConsumptionDataDO> existingMap = existingList.stream()
                .collect(Collectors.toMap(
                        e -> e.getFactoryId() + "_" + e.getDate(),
                        Function.identity()
                ));

        // 分类处理新增和更新
        List<ProdConsumptionDataDO> toInsert = new ArrayList<>();
        List<ProdConsumptionDataDO> toUpdate = new ArrayList<>();
        List<Long> resultIds = new ArrayList<>();
        for (ProdConsumptionDataSaveReqVO reqVO : reqList) {
            String key = reqVO.getFactoryId() + "_" + reqVO.getDate();
            ProdConsumptionDataDO existing = new ProdConsumptionDataDO();
            BeanUtils.copyProperties(reqVO, existing);

            if (existingMap.containsKey(key)) {
               /* ProdConsumptionDataDO existing = existingMap.get(key);
                BeanUtils.copyProperties(reqVO, existing);*/
                existing.setId(existingMap.get(key).getId());
                toUpdate.add(existing);
                resultIds.add(existing.getId());
            } else {
                ProdConsumptionDataDO entity = new ProdConsumptionDataDO();
                BeanUtils.copyProperties(reqVO, entity);
                toInsert.add(entity);
            }
        }

        if (!toInsert.isEmpty()) {
            this.saveBatch(toInsert);
            resultIds.addAll(toInsert.stream().map(ProdConsumptionDataDO::getId).collect(Collectors.toList()));
        }

        if (!toUpdate.isEmpty()) {
            this.updateBatchById(toUpdate);
        }
        return resultIds;
    }

    private final Map<String, Function<ProdConsumptionDataSaveReqVO, BigDecimal>> FIELD_EXTRACTORS = createFieldExtractors();

    private Map<String, Function<ProdConsumptionDataSaveReqVO, BigDecimal>> createFieldExtractors() {
        Map<String, Function<ProdConsumptionDataSaveReqVO, BigDecimal>> map = new HashMap<>();
        map.put("进水水量（km³）", ProdConsumptionDataSaveReqVO::getInFlowVol);
        map.put("出水水量（km³）", ProdConsumptionDataSaveReqVO::getTreatVol);
        map.put("电量消耗（kWh）", ProdConsumptionDataSaveReqVO::getElecCons);
        map.put("碳源用量（kg）", ProdConsumptionDataSaveReqVO::getCarbonUsage);
        map.put("次氯酸钠用量（kg）", ProdConsumptionDataSaveReqVO::getSodiumHypoUsage);
        map.put("聚合氯化铝用量（kg）", ProdConsumptionDataSaveReqVO::getPacUsage);
        map.put("聚合硫酸铁用量（kg）", ProdConsumptionDataSaveReqVO::getFerricSulfUsage);
        map.put("氢氧化钠用量（kg）", ProdConsumptionDataSaveReqVO::getNaohUsage);
        map.put("固体聚丙烯酰胺（阴离子）用量（kg）", ProdConsumptionDataSaveReqVO::getAnPamUsage);
        map.put("固体聚丙烯酰胺（阳离子）用量（kg）", ProdConsumptionDataSaveReqVO::getCatPamUsage);
        map.put("60%污泥产量（吨）", ProdConsumptionDataSaveReqVO::getSludge60Prod);
        map.put("80%污泥产量（吨）", ProdConsumptionDataSaveReqVO::getSludge80Prod);
        map.put("绝干污泥产量（吨）", ProdConsumptionDataSaveReqVO::getDrySludgeProd);
        map.put("污泥处理中固体聚丙烯酰胺（阳离子）用量（kg）", ProdConsumptionDataSaveReqVO::getCatPamSludgeUsage);
        map.put("污泥处理中聚合氯化铝用量（kg）", ProdConsumptionDataSaveReqVO::getPacSludgeUsage);
        map.put("液体铁盐用量（kg）", ProdConsumptionDataSaveReqVO::getLiqIronSaltUsage);
        map.put("石灰用量（kg）", ProdConsumptionDataSaveReqVO::getLimeUsage);
        return map;
    }

    /**
     * 将数据转换为指标数据
     * @param reqList 数据list
     * @param isArchive 是否归档
     * @param reviewStatus 5:补录状态
     * @return
     */
    private List<IndicatorDataSaveReqVO> createIndicatorDataByReportData(List<ProdConsumptionDataSaveReqVO> reqList,
                                                                         Integer isArchive, Integer reviewStatus) {
        List<IndicatorDataSaveReqVO> indicatorDataList = new ArrayList<>();

        for (ProdConsumptionDataSaveReqVO reqVO : reqList) {
            for (Map.Entry<String, Function<ProdConsumptionDataSaveReqVO, BigDecimal>> entry : FIELD_EXTRACTORS.entrySet()) {
                BigDecimal value = entry.getValue().apply(reqVO);
                if (value != null) {
                    indicatorDataList.add(IndicatorDataSaveReqVO.builder()
                            .factoryId(reqVO.getFactoryId())
                            .bizType("consumption")
                            .indicatorName(entry.getKey())
                            .indicatorValue(value)
                            .reportDate(reqVO.getDate())
                            .reporterId(reqVO.getReporterId())
                            .isArchived(isArchive)
                            .status(reviewStatus)
                            .build());
                }
            }
        }
        return indicatorDataList;
    }


    @Override
    public List<WaterProdConsumptionStats> getAllFactoryStats(LocalDate startDate, LocalDate endDate) {
        List<WaterProdConsumptionStats> list = prodConsumptionDataMapper.getAllStats(startDate, endDate);
        List<WaterProdConsumptionStats> resultList = new ArrayList<>();

        // 如果是整月类型的查询， 从另外一张表走

        // 否则 执行下边的内容

        // 获取集团级水厂
        FactoryDO parentFactory = factoryService.getOne(new LambdaQueryWrapper<FactoryDO>().eq(FactoryDO::getLevel, 1));
        List<WaterProdConsumptionStats> collect = list.stream().filter(item -> item.getLevel() == 3).collect(Collectors.toList());
        WaterProdConsumptionStats parentStats = new WaterProdConsumptionStats();
        parentStats.setFactoryId(parentFactory.getId());
        parentStats.setFactoryName(parentFactory.getName());
        parentStats.setOrderNum(parentFactory.getOrderNum());
        getDailySummary(parentStats, collect);
        getMonthlySummary(parentStats, collect);
        calculateSingleConsumptions(parentStats, false);
        calculateSingleConsumptions(parentStats, true);
        resultList.add(parentStats);

        // 获取区域级水厂
        List<FactoryDO> regionFactories = factoryService.list(new LambdaQueryWrapper<FactoryDO>().eq(FactoryDO::getLevel, 2));
        for (FactoryDO regionFactory : regionFactories) {
            WaterProdConsumptionStats regionStats = new WaterProdConsumptionStats();
            regionStats.setFactoryId(regionFactory.getId());
            regionStats.setFactoryName(regionFactory.getName());
            regionStats.setOrderNum(regionFactory.getOrderNum());

            List<WaterProdConsumptionStats> regionList = list.stream()
                    .filter(stats -> stats.getParentId().equals(regionFactory.getId()))
                    .collect(Collectors.toList());

            getDailySummary(regionStats, regionList);
            getMonthlySummary(regionStats, regionList);
            calculateSingleConsumptions(regionStats, false);
            calculateSingleConsumptions(regionStats, true);
            resultList.add(regionStats);
        }

        resultList.addAll(list);
        resultList.sort(Comparator.comparing(WaterProdConsumptionStats::getOrderNum));
        return resultList;
    }

    @Override
    public List<Long> insert(List<IndicatorDataSaveReqVO> indicatorDataSaveReqVOList) {

        List<ProdConsumptionDataSaveReqVO> voList = new ArrayList<>();

        for (IndicatorDataSaveReqVO vo : indicatorDataSaveReqVOList) {
            ProdConsumptionDataSaveReqVO dataSaveReqVO = new ProdConsumptionDataSaveReqVO();
            dataSaveReqVO.setReporterId(SecurityFrameworkUtils.getLoginUser().getId());
            dataSaveReqVO.setFactoryId(vo.getFactoryId());

            dataSaveReqVO.setDate(LocalDate.now());
            dataSaveReqVO.setReporterId(SecurityFrameworkUtils.getLoginUser().getId());

            String field = vo.getIndicatorName();
            if (field != null) {
                try {
                    // 动态生成 setter 方法名
                    String setterMethodName = "set" + field.substring(0, 1).toUpperCase() + field.substring(1);
                    // 获取对应的 setter 方法
                    Method setterMethod = dataSaveReqVO.getClass().getMethod(setterMethodName, vo.getIndicatorValue().getClass());
                    // 调用 setter 方法设置值
                    setterMethod.invoke(dataSaveReqVO, vo.getIndicatorValue());
                    voList.add(dataSaveReqVO);
                } catch (Exception e) {
                    e.printStackTrace(); // 处理异常，可以根据需求做更详细的错误处理
                }
            }
        }
        return saveOrUpdateBatch(voList);
    }


    /**
     * 补录数据，不经过审核直接归档
     * @param reqVOList
     * @return
     */
    @Override
    public List<Long> additionalRecording(List<ProdConsumptionDataSaveReqVO> reqVOList) {
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        Long factoryId = reqVOList.get(0).getFactoryId();
        // 权限校验
        hasFactoryOperationPermission(currentUserId,factoryId);

        // 1. 将所有请求对象设置为“已通过”（2）
        reqVOList.forEach(reqVO -> reqVO.setReviewStatus(2));
        // 2. 保存或更新宽表数据
        List<Long> res = saveUpdateWideTable(reqVOList);
        // 3. 构造指标数据对象（已归档 + 审核状态为“补录(5)”）
        //直接归档并且新增审核状态与审核流程为“补录：5”
        final int ARCHIVED = 1;
        final int REVIEW_STATUS_SUPPLEMENT = 5;
        final int FLOW_STAGE_SUPPLEMENT = 5;

        List<IndicatorDataSaveReqVO> indicatorDataList = createIndicatorDataByReportData(reqVOList,ARCHIVED,REVIEW_STATUS_SUPPLEMENT);

        // 4. 保存或更新指标数据
        if (!indicatorDataList.isEmpty()) {
            return indicatorDataService.saveOrUpdateBatch(indicatorDataList, FLOW_STAGE_SUPPLEMENT);
        }
        return res;
    }
    @Override
    public List<Long> saveTemporarily(List<ProdConsumptionDataSaveReqVO> createReqVOList) {
        // 如果为空，直接返回空list
        if (CollectionUtils.isEmpty(createReqVOList)) {
            return Collections.emptyList();
        }

        // 获取当前填报人
        Long currentUserId = SecurityFrameworkUtils.getLoginUser().getId();
        Long factoryId = createReqVOList.get(0).getFactoryId();

        // 权限校验
        hasFactoryOperationPermission(currentUserId,factoryId);

        createReqVOList.forEach(reqVO -> {
            reqVO.setReviewStatus(5); // 记录为暂存状态
            reqVO.setReporterId(currentUserId);
        });

        return saveUpdateWideTable(createReqVOList);
    }

    public static void getDailySummary(WaterProdConsumptionStats result, List<WaterProdConsumptionStats> statsList) {
        sumFields(result, statsList, false);
    }

    public static void getMonthlySummary(WaterProdConsumptionStats result, List<WaterProdConsumptionStats> statsList) {
        sumFields(result, statsList, true);
    }

    private static void sumFields(WaterProdConsumptionStats result, List<WaterProdConsumptionStats> statsList, boolean isMonthly) {

        if (statsList == null || statsList.isEmpty()) {
            return;
        }

        Field[] fields = WaterProdConsumptionStats.class.getDeclaredFields();
        for (Field field : fields) {
            // 过滤出 BigDecimal 类型的字段
            if (field.getType().equals(BigDecimal.class)) {
                boolean isMonthField = field.getName().startsWith("month");
                if (isMonthField != isMonthly) continue;

                field.setAccessible(true);
                BigDecimal sum = BigDecimal.ZERO;

                for (WaterProdConsumptionStats stats : statsList) {
                    try {
                        BigDecimal value = (BigDecimal) field.get(stats);
                        if (value != null) {
                            sum = sum.add(value);
                        }
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                }

                try {
                    field.set(result, sum);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private static void calculateSingleConsumptions(WaterProdConsumptionStats result, boolean isMonthly) {
        if (isMonthly) {
            result.setMonthElecSingleCons(safeDivide(result.getMonthElecCons(), result.getMonthTreatVol()));
            result.setMonthCarbonSingleCons(safeDivide(result.getMonthCarbonUsage(), result.getMonthTreatVol()));
            result.setMonthSodiumHypoSingleCons(safeDivide(result.getMonthSodiumHypoUsage(), result.getMonthTreatVol()));
            result.setMonthPacSingleCons(safeDivide(result.getMonthPacUsage(), result.getMonthTreatVol()));
            result.setMonthFerricSulfSingleCons(safeDivide(result.getMonthFerricSulfUsage(), result.getMonthTreatVol()));
            result.setMonthNaohSingleCons(safeDivide(result.getMonthNaohUsage(), result.getMonthTreatVol()));
            result.setMonthAnPamSingleCons(safeDivide(result.getMonthAnPamUsage(), result.getMonthTreatVol()));
            result.setMonthCatPamSingleCons(safeDivide(result.getMonthCatPamUsage(), result.getMonthTreatVol()));
            BigDecimal sum = BigDecimal.ZERO;
            sum = sum.add(result.getMonthSludge60Prod()).add(result.getMonthSludge80Prod());
            result.setMonthSludgeRate(safeDivide(sum, result.getMonthTreatVol()));
            result.setMonthCatPamSludgeSingleCons(safeDivide(result.getMonthCatPamSludgeUsage(), result.getMonthTreatVol()));
            result.setMonthPacSludgeSingleCons(safeDivide(result.getMonthPacSludgeUsage(), result.getMonthTreatVol()));
            result.setMonthLiqIronSaltSingleCons(safeDivide(result.getMonthLiqIronSaltUsage(), result.getMonthTreatVol()));
            result.setMonthLimeSingleCons(safeDivide(result.getMonthLimeUsage(), result.getMonthTreatVol()));
        } else {
            result.setElecSingleCons(safeDivide(result.getElecCons(), result.getTreatVol()));
            result.setCarbonSingleCons(safeDivide(result.getCarbonUsage(), result.getTreatVol()));
            result.setSodiumHypoSingleCons(safeDivide(result.getSodiumHypoUsage(), result.getTreatVol()));
            result.setPacSingleCons(safeDivide(result.getPacUsage(), result.getTreatVol()));
            result.setFerricSulfSingleCons(safeDivide(result.getFerricSulfUsage(), result.getTreatVol()));
            result.setNaohSingleCons(safeDivide(result.getNaohUsage(), result.getTreatVol()));
            result.setAnPamSingleCons(safeDivide(result.getAnPamUsage(), result.getTreatVol()));
            result.setCatPamSingleCons(safeDivide(result.getCatPamUsage(), result.getTreatVol()));
            BigDecimal sum = BigDecimal.ZERO;
            sum = sum.add(result.getSludge60Prod()).add(result.getSludge80Prod());
            result.setSludgeRate(safeDivide(sum, result.getTreatVol()));
            result.setCatPamSludgeSingleCons(safeDivide(result.getCatPamSludgeUsage(), result.getTreatVol()));
            result.setPacSludgeSingleCons(safeDivide(result.getPacSludgeUsage(), result.getTreatVol()));
            result.setLiqIronSaltSingleCons(safeDivide(result.getLiqIronSaltUsage(), result.getTreatVol()));
            result.setLimeSingleCons(safeDivide(result.getLimeUsage(), result.getTreatVol()));
        }
    }



    /**
     * 提取权限校验方法
     * @param userId
     * @param factoryId
     * @return
     */
    private boolean hasFactoryOperationPermission(Long userId, Long factoryId) {
        boolean isAdmin;
        try {
            isAdmin = Boolean.TRUE.equals(permissionApi.hasAnyRoles(userId, "report_admin", "super_admin").getData());
        } catch (Exception e) {
            // 权限检查失败时，默认为非管理员
            isAdmin = false;
        }

        // 判断是否有模块功能权限
        if (!isAdmin) {
            Boolean hasOperationPermission = permissionApi.hasFactoryOperationPermission(userId, factoryId, REPORT_MODULE_CODE, REPORT_ROLE_CODE_FILLER);
            if (!hasOperationPermission) {
                throw new ServiceException(new ErrorCode(40100, "操作失败：当前用户没有权限进行该厂站填报操作"));
            }
        }

        return isAdmin;
    }

    /**
     * 查询生产消耗的统计/汇总数据
     */
    @Override
    public List<ProdConsumptionStatRespVO> getQualityReport(Long factoryId, LocalDate startDate, LocalDate endDate) {
        // 判断是否为 2024 年且查询整月数据
        if (startDate.getYear() == 2024 && endDate.getYear() == 2024 && isFullMonth(startDate, endDate)) {
            // 查询聚合表 water_prod_consumption_stat
            Map<String, Object> statMap = prodConsumptionDataMapper.selectStatFromStatTable(factoryId, startDate, endDate);
            ProdConsumptionStatRespVO result = convertMapToStatRespVO(statMap, factoryId);
            return Collections.singletonList(result);
        } else {
            // 查询原始表 water_prod_consumption_data 后进行统计计算
            List<ProdConsumptionDataDO> dataList = prodConsumptionDataMapper.selectDataFromDataTable(factoryId, startDate, endDate);
            ProdConsumptionStatRespVO result = computeStats(dataList, factoryId);
            return Collections.singletonList(result);
        }
    }


    /**
     * 判断查询日期范围是否为整月
     */
    private boolean isFullMonth(LocalDate startDate, LocalDate endDate) {
        // 检查开始日期是否为月初（1号）
        if (startDate.getDayOfMonth() != 1) {
            return false;
        }

        // 检查结束日期是否为月末
        LocalDate lastDayOfMonth = startDate.with(TemporalAdjusters.lastDayOfMonth());
        return endDate.equals(lastDayOfMonth);
    }

    /**
     * 计算统计值
     */
    private ProdConsumptionStatRespVO computeStats(List<ProdConsumptionDataDO> dataList, Long factoryId) {
        ProdConsumptionStatRespVO result = new ProdConsumptionStatRespVO();
        result.setFactoryId(factoryId);

        if (dataList == null || dataList.isEmpty()) {
            return result;
        }

        Field[] fields = ProdConsumptionDataDO.class.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);

            // 跳过非BigDecimal字段和不需要统计的字段
            if (!BigDecimal.class.equals(field.getType()) ||
                shouldSkipField(field.getName())) {
                continue;
            }

            List<BigDecimal> values = new ArrayList<>();
            for (ProdConsumptionDataDO data : dataList) {
                try {
                    BigDecimal value = (BigDecimal) field.get(data);
                    if (value != null) {
                        values.add(value);
                    }
                } catch (IllegalAccessException e) {
                    // 忽略访问异常
                }
            }

            if (!values.isEmpty()) {
                ProdConsumptionStatRespVO.StatValue stat = calculateConsumptionStat(values, field.getName());

                try {
                    Field respField = ProdConsumptionStatRespVO.class.getDeclaredField(field.getName());
                    respField.setAccessible(true);
                    respField.set(result, stat);
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    // 字段可能不匹配，忽略
                }
            }
        }

        return result;
    }

    /**
     * 判断是否应该跳过统计的字段
     */
    private boolean shouldSkipField(String fieldName) {
        // 跳过ID、审核状态等非统计字段
        return "id".equals(fieldName) ||
               "factoryId".equals(fieldName) ||
               "reporterId".equals(fieldName) ||
               "reviewerId".equals(fieldName) ||
               "reviewStatus".equals(fieldName);
    }

    /**
     * 计算最大值、最小值、平均值和总和
     */
    private ProdConsumptionStatRespVO.StatValue calculateConsumptionStat(List<BigDecimal> values, String fieldName) {
        ProdConsumptionStatRespVO.StatValue stat = new ProdConsumptionStatRespVO.StatValue();

        if (values.isEmpty()) {
            stat.setAvg(BigDecimal.ZERO);
            stat.setMax(BigDecimal.ZERO);
            stat.setMin(BigDecimal.ZERO);
            // 只有非单耗字段才设置累计值
            if (!isSingleConsField(fieldName)) {
                stat.setTotal(BigDecimal.ZERO);
            }
            return stat;
        }

        // 判断是否为水量字段，需要乘以1000进行单位转换
        boolean isVolumeField = isRawDataVolumeField(fieldName);
        BigDecimal scaleFactor = isVolumeField ? new BigDecimal("1000") : BigDecimal.ONE;

        BigDecimal max = values.stream().max(Comparator.naturalOrder()).orElse(BigDecimal.ZERO).multiply(scaleFactor);
        BigDecimal min = values.stream().min(Comparator.naturalOrder()).orElse(BigDecimal.ZERO).multiply(scaleFactor);
        BigDecimal total = values.stream().reduce(BigDecimal.ZERO, BigDecimal::add).multiply(scaleFactor);
        BigDecimal avg = total.divide(BigDecimal.valueOf(values.size()), 4, RoundingMode.HALF_UP);

        stat.setMax(max);
        stat.setMin(min);
        stat.setAvg(avg);

        // 只有非单耗字段才设置累计值
        if (!isSingleConsField(fieldName)) {
            stat.setTotal(total);
        }

        return stat;
    }

    /**
     * 判断是否为原始数据中的水量字段（需要乘以1000的字段）
     */
    private boolean isRawDataVolumeField(String fieldName) {
        return "prodVol".equals(fieldName) ||           // 产量
               "inFlowVol".equals(fieldName) ||         // 进水水量
               "treatVol".equals(fieldName);            // 处理水量
    }

    /**
     * 判断是否为单耗字段（单耗字段不计算累计值）
     */
    private boolean isSingleConsField(String fieldName) {
        // 单耗类字段：包含SingleCons或single_cons的字段
        // 产泥率字段：sludgeRate或sludge_rate也不计算累计值
        return fieldName.contains("SingleCons") ||
               fieldName.contains("single_cons") ||
               "sludgeRate".equals(fieldName) ||
               "sludge_rate".equals(fieldName);
    }

    /**
     * 将Map数据转换为ProdConsumptionStatRespVO对象
     */
    private ProdConsumptionStatRespVO convertMapToStatRespVO(Map<String, Object> statMap, Long factoryId) {
        if (statMap == null || statMap.isEmpty()) {
            ProdConsumptionStatRespVO emptyResult = new ProdConsumptionStatRespVO();
            emptyResult.setFactoryId(factoryId);
            return emptyResult;
        }

        ProdConsumptionStatRespVO result = new ProdConsumptionStatRespVO();
        result.setFactoryId(factoryId);

        // 设置各个指标的统计值
        result.setProdVol(createConsumptionStatValue(statMap, "prod_vol"));
        result.setInFlowVol(createConsumptionStatValue(statMap, "in_flow_vol"));
        result.setTreatVol(createConsumptionStatValue(statMap, "treat_vol"));
        result.setElecCons(createConsumptionStatValue(statMap, "elec_cons"));
        result.setElecSingleCons(createConsumptionStatValue(statMap, "elec_single_cons"));
        result.setCarbonUsage(createConsumptionStatValue(statMap, "carbon_usage"));
        result.setCarbonSingleCons(createConsumptionStatValue(statMap, "carbon_single_cons"));
        result.setSodiumHypoUsage(createConsumptionStatValue(statMap, "sodium_hypo_usage"));
        result.setSodiumHypoSingleCons(createConsumptionStatValue(statMap, "sodium_hypo_single_cons"));
        result.setPacUsage(createConsumptionStatValue(statMap, "pac_usage"));
        result.setPacSingleCons(createConsumptionStatValue(statMap, "pac_single_cons"));
        result.setFerricSulfUsage(createConsumptionStatValue(statMap, "ferric_sulf_usage"));
        result.setFerricSulfSingleCons(createConsumptionStatValue(statMap, "ferric_sulf_single_cons"));
        result.setNaohUsage(createConsumptionStatValue(statMap, "naoh_usage"));
        result.setNaohSingleCons(createConsumptionStatValue(statMap, "naoh_single_cons"));
        result.setAnPamUsage(createConsumptionStatValue(statMap, "an_pam_usage"));
        result.setAnPamSingleCons(createConsumptionStatValue(statMap, "an_pam_single_cons"));
        result.setCatPamUsage(createConsumptionStatValue(statMap, "cat_pam_usage"));
        result.setCatPamSingleCons(createConsumptionStatValue(statMap, "cat_pam_single_cons"));
        result.setSludge60Prod(createConsumptionStatValue(statMap, "sludge60_prod"));
        result.setSludge80Prod(createConsumptionStatValue(statMap, "sludge80_prod"));
        result.setDrySludgeProd(createConsumptionStatValue(statMap, "dry_sludge_prod"));
        result.setSludgeRate(createConsumptionStatValue(statMap, "sludge_rate"));
        result.setCatPamSludgeUsage(createConsumptionStatValue(statMap, "cat_pam_sludge_usage"));
        result.setCatPamSludgeSingleCons(createConsumptionStatValue(statMap, "cat_pam_sludge_single_cons"));
        result.setPacSludgeUsage(createConsumptionStatValue(statMap, "pac_sludge_usage"));
        result.setPacSludgeSingleCons(createConsumptionStatValue(statMap, "pac_sludge_single_cons"));
        result.setLiqIronSaltUsage(createConsumptionStatValue(statMap, "liq_iron_salt_usage"));
        result.setLiqIronSaltSingleCons(createConsumptionStatValue(statMap, "liq_iron_salt_single_cons"));
        result.setLimeUsage(createConsumptionStatValue(statMap, "lime_usage"));
        result.setLimeSingleCons(createConsumptionStatValue(statMap, "lime_single_cons"));

        return result;
    }

    /**
     * 从Map中创建StatValue对象
     */
    private ProdConsumptionStatRespVO.StatValue createConsumptionStatValue(Map<String, Object> statMap, String prefix) {
        ProdConsumptionStatRespVO.StatValue statValue = new ProdConsumptionStatRespVO.StatValue();

        Object avgObj = statMap.get(prefix + "_avg");
        Object maxObj = statMap.get(prefix + "_max");
        Object minObj = statMap.get(prefix + "_min");
        Object totalObj = statMap.get(prefix + "_total");

        // 判断是否为水量字段，需要乘以1000进行单位转换
        boolean isVolumeField = isVolumeField(prefix);

        statValue.setAvg(convertAndScale(avgObj, isVolumeField));
        statValue.setMax(convertAndScale(maxObj, isVolumeField));
        statValue.setMin(convertAndScale(minObj, isVolumeField));

        // 只要查出来的total值不为null，就设置到响应中
        if (totalObj != null) {
            statValue.setTotal(convertAndScale(totalObj, isVolumeField));
        }

        return statValue;
    }

    /**
     * 判断是否为水量字段（需要乘以1000的字段）
     */
    private boolean isVolumeField(String fieldName) {
        return "prod_vol".equals(fieldName) ||           // 产量
               "in_flow_vol".equals(fieldName) ||        // 进水水量
               "treat_vol".equals(fieldName);            // 处理水量
    }

    /**
     * 转换数值并根据需要进行缩放
     */
    private BigDecimal convertAndScale(Object obj, boolean needScale) {
        if (obj == null) {
            return null;
        }

        BigDecimal value = new BigDecimal(obj.toString());

        // 如果是水量字段，需要乘以1000
        if (needScale) {
            value = value.multiply(new BigDecimal("1000"));
        }

        return value;
    }

}
