<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.tl.cloud.module.monitor.dal.mysql.assay.testPlan.AssaySamplingPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.tl.cloud.module.monitor.dal.dataobject.assay.testPlan.AssaySamplingPlanDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="factory_id" property="factoryId" jdbcType="BIGINT"/>
        <result column="plan_code" property="planCode" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="LONGVARCHAR"/>
        <result column="frequency" property="frequency" jdbcType="VARCHAR"/>
        <result column="start_date" property="startDate" jdbcType="DATE"/>
        <result column="end_date" property="endDate" jdbcType="DATE"/>
        <result column="plan_datetime" property="planDatetime" jdbcType="TIMESTAMP"/>
        <result column="reason" property="reason" jdbcType="LONGVARCHAR"/>
        <result column="priority" property="priority" jdbcType="VARCHAR"/>
        <result column="test_item" property="testItem" jdbcType="BIGINT"/>
        <result column="sampling_point" property="samplingPoint" jdbcType="BIGINT"/>
        <result column="sampler_id" property="samplerId" jdbcType="BIGINT"/>
        <result column="tester_id" property="testerId" jdbcType="BIGINT"/>
        <result column="reviewer_id" property="reviewerId" jdbcType="BIGINT"/>
        <result column="expected_sample_quantity" property="expectedSampleQuantity" jdbcType="INTEGER"/>
        <result column="expected_sample_nature" property="expectedSampleNature" jdbcType="VARCHAR"/>
        <result column="expected_sample_appearance" property="expectedSampleAppearance" jdbcType="VARCHAR"/>
        <result column="expected_supernatant" property="expectedSupernatant" jdbcType="VARCHAR"/>
        <result column="sampling_instructions" property="samplingInstructions" jdbcType="LONGVARCHAR"/>
        <result column="is_enabled" property="isEnabled" jdbcType="BIT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="extra_field" property="extraField" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="BIT"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, factory_id, plan_code, name, type, description, frequency, start_date, end_date,
        plan_datetime, reason, priority, test_item, sampling_point, sampler_id, tester_id,
        reviewer_id, expected_sample_quantity, expected_sample_nature, expected_sample_appearance,
        expected_supernatant, sampling_instructions, is_enabled, remark, extra_field,
        creator, create_time, updater, update_time, deleted
    </sql>

    <!-- 根据计划ID查询关联的任务数量 -->
    <select id="selectTaskCountByPlanId" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM assay_sampling_task
        WHERE plan_id = #{planId}
          AND deleted = 0
    </select>

    <!-- 查询指定时间范围内的冲突计划 -->
    <select id="selectConflictPlansWithDetails" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM assay_sampling_plan
        WHERE factory_id = #{factoryId}
          AND sampling_point = #{samplingPoint}
          AND is_enabled = 1
          AND deleted = 0
          AND (
              (type = 'regular' AND start_date &lt;= #{endDate} AND end_date &gt;= #{startDate})
              OR
              (type = 'temporary' AND DATE(plan_datetime) BETWEEN #{startDate} AND #{endDate})
          )
          <if test="excludePlanId != null">
              AND id != #{excludePlanId}
          </if>
        ORDER BY start_date, plan_datetime
    </select>

    <!-- 查询日历视图数据 -->
    <select id="selectCalendarPlans" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM assay_sampling_plan
        WHERE factory_id = #{factoryId}
          AND is_enabled = 1
          AND deleted = 0
          AND (
              (type = 'regular' AND start_date &lt;= #{endDate} AND end_date &gt;= #{startDate})
              OR
              (type = 'temporary' AND DATE(plan_datetime) BETWEEN #{startDate} AND #{endDate})
          )
          <if test="type != null and type != ''">
              AND type = #{type}
          </if>
        ORDER BY start_date, plan_datetime
    </select>

</mapper>
