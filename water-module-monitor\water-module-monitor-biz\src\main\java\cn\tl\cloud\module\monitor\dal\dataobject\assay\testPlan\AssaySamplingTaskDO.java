package cn.tl.cloud.module.monitor.dal.dataobject.assay.testPlan;

import cn.tl.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 采样任务 DO
 *
 * <AUTHOR>
 */
@TableName("assay_sampling_task")
@KeySequence("assay_sampling_task_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssaySamplingTaskDO extends BaseDO {

    /**
     * 任务ID
     */
    @TableId
    private Long id;
    
    /**
     * 水厂ID
     */
    private Long factoryId;
    
    /**
     * 任务编号
     */
    private String taskCode;
    
    /**
     * 来源计划ID
     */
    private Long planId;
    
    /**
     * 任务执行时间
     */
    private LocalDateTime taskDatetime;
    
    /**
     * 检测项目ID
     */
    private Long testItem;
    
    /**
     * 采样点ID
     */
    private Long samplingPoint;
    
    /**
     * 采样人员ID
     */
    private Long samplerId;
    
    /**
     * 检测人员ID
     */
    private Long testerId;
    
    /**
     * 审核人员ID
     */
    private Long reviewerId;
    
    /**
     * 优先级
     */
    private String priority;
    
    /**
     * 预期样品量(mL)
     */
    private Integer expectedSampleQuantity;
    
    /**
     * 预期样品性质
     */
    private String expectedSampleNature;
    
    /**
     * 预期样品外观
     */
    private String expectedSampleAppearance;
    
    /**
     * 预期上清液情况
     */
    private String expectedSupernatant;
    
    /**
     * 采样说明
     */
    private String samplingInstructions;
    
    /**
     * 状态(pending-待执行,sampling-采样中,submitting-送检中,testing-检验中,completed-已完成,cancelled-已取消)
     */
    private String status;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime completeTime;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 扩展字段，存储额外的业务数据
     */
    private String extraField;

}
