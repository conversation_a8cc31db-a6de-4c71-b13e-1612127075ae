package cn.tl.cloud.module.report.service.report.prodqualitydata;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.module.report.controller.admin.indicator.indicatordata.vo.IndicatorDataSaveReqVO;
import cn.tl.cloud.module.report.controller.admin.report.vo.consumption.MonthDataReqVO;
import cn.tl.cloud.module.report.controller.admin.report.vo.quality.DateRangeReqVO;
import cn.tl.cloud.module.report.controller.admin.report.vo.quality.ProdQualityDataPageReqVO;
import cn.tl.cloud.module.report.controller.admin.report.vo.quality.ProdQualityDataSaveReqVO;
import cn.tl.cloud.module.report.controller.admin.report.vo.quality.ProdQualityStatRespVO;
import cn.tl.cloud.module.report.dal.dataobject.stat.ProdQualityDataDO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

/**
 * 进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量 Service 接口
 *
 * <AUTHOR>
 */
public interface ProdQualityDataService extends IService<ProdQualityDataDO> {

    /**
     * 创建进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProdQualityData(@Valid ProdQualityDataSaveReqVO createReqVO);

    /**
     * 更新进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量
     *
     * @param updateReqVO 更新信息
     */
    void updateProdQualityData(@Valid ProdQualityDataSaveReqVO updateReqVO);

    /**
     * 删除进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量
     *
     * @param id 编号
     */
    void deleteProdQualityData(Long id);

    /**
     * 获得进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量
     *
     * @param id 编号
     * @return 进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量
     */
    ProdQualityDataDO getProdQualityData(Long id);

    /**
     * 获得进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量分页
     *
     * @param pageReqVO 分页查询
     * @return 进出水水质数据表，记录每日的进水和出水水质指标，以及出水流量和日处理量分页
     */
    PageResult<ProdQualityDataDO> getProdQualityDataPage(ProdQualityDataPageReqVO pageReqVO);

    List<ProdQualityDataDO> getProdQualityDataList(MonthDataReqVO monthDataReqVO);

    List<Long> saveOrUpdateBatch(@Valid List<ProdQualityDataSaveReqVO> createReqVOList);

    /**
     * 批量插入指标数据
     */
    List<Long> insert(List<IndicatorDataSaveReqVO> indicatorDataSaveReqVOList);

    List<Long> additionalRecording(List<ProdQualityDataSaveReqVO> createReqVOList);

    List<Long> saveTemporarily(@Valid List<ProdQualityDataSaveReqVO> createReqVOList);

    List<ProdQualityDataDO> getProdQualityDataByDateRange(DateRangeReqVO reqVO);

    /**
     * 查询所有生产水质的统计/汇总数据
     */
    List<ProdQualityStatRespVO> getQualityReport(Long factoryId, LocalDate startDate, LocalDate endDate);
}