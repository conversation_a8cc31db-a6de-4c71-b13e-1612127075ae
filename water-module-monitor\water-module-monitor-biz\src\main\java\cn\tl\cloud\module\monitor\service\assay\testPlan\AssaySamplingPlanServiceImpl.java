package cn.tl.cloud.module.monitor.service.assay.testPlan;

import cn.tl.cloud.framework.common.exception.util.ServiceExceptionUtil;
import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.common.util.object.BeanUtils;
import cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo.AssaySamplingPlanPageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.assay.testPlan.vo.AssaySamplingPlanSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssaySamplingPointDO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.baseInfo.AssayTestIndicatorDO;
import cn.tl.cloud.module.monitor.dal.dataobject.assay.testPlan.AssaySamplingPlanDO;
import cn.tl.cloud.module.monitor.dal.mysql.assay.baseInfo.AssaySamplingPointMapper;
import cn.tl.cloud.module.monitor.dal.mysql.assay.baseInfo.AssayTestIndicatorMapper;
import cn.tl.cloud.module.monitor.dal.mysql.assay.testPlan.AssaySamplingPlanMapper;
import cn.tl.cloud.module.monitor.dal.mysql.assay.testPlan.AssaySamplingTaskMapper;
import cn.tl.cloud.module.monitor.enums.ErrorCodeConstants;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;

/**
 * 采样计划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssaySamplingPlanServiceImpl implements AssaySamplingPlanService {

    @Resource
    private AssaySamplingPlanMapper samplingPlanMapper;
    
    @Resource
    private AssaySamplingTaskMapper samplingTaskMapper;
    
    @Resource
    private AssayTestIndicatorMapper testIndicatorMapper;
    
    @Resource
    private AssaySamplingPointMapper samplingPointMapper;

    @Override
    public Long createSamplingPlan(@Valid AssaySamplingPlanSaveReqVO createReqVO) {
        // 校验基础数据
        validateBasicData(createReqVO);
        
        // 校验计划编号唯一性
        validatePlanCodeUnique(createReqVO.getFactoryId(), createReqVO.getPlanCode(), null);
        
        // 校验计划名称唯一性
        validatePlanNameUnique(createReqVO.getFactoryId(), createReqVO.getName(), null);
        
        // 校验人员不能相同
        validatePersonNotSame(createReqVO.getSamplerId(), createReqVO.getTesterId(), createReqVO.getReviewerId());
        
        // 校验计划类型相关字段
        validatePlanTypeFields(createReqVO);
        
        // 插入
        AssaySamplingPlanDO samplingPlan = BeanUtils.toBean(createReqVO, AssaySamplingPlanDO.class);
        samplingPlanMapper.insert(samplingPlan);
        
        // 返回
        return samplingPlan.getId();
    }

    @Override
    public void updateSamplingPlan(@Valid AssaySamplingPlanSaveReqVO updateReqVO) {
        // 校验存在
        validateSamplingPlanExists(updateReqVO.getId(), updateReqVO.getFactoryId());
        
        // 校验基础数据
        validateBasicData(updateReqVO);
        
        // 校验计划编号唯一性
        validatePlanCodeUnique(updateReqVO.getFactoryId(), updateReqVO.getPlanCode(), updateReqVO.getId());
        
        // 校验计划名称唯一性
        validatePlanNameUnique(updateReqVO.getFactoryId(), updateReqVO.getName(), updateReqVO.getId());
        
        // 校验人员不能相同
        validatePersonNotSame(updateReqVO.getSamplerId(), updateReqVO.getTesterId(), updateReqVO.getReviewerId());
        
        // 校验计划类型相关字段
        validatePlanTypeFields(updateReqVO);
        
        // 更新
        AssaySamplingPlanDO updateObj = BeanUtils.toBean(updateReqVO, AssaySamplingPlanDO.class);
        samplingPlanMapper.updateById(updateObj);
    }

    @Override
    public void deleteSamplingPlan(Long id, Long factoryId) {
        // 校验存在
        validateSamplingPlanExists(id, factoryId);
        
        // 校验是否存在关联任务
        Long taskCount = samplingTaskMapper.selectCountByPlanId(id);
        if (taskCount > 0) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_PLAN_HAS_TASKS);
        }
        
        // 删除
        samplingPlanMapper.deleteById(id);
    }

    private void validateSamplingPlanExists(Long id, Long factoryId) {
        AssaySamplingPlanDO samplingPlan = samplingPlanMapper.selectById(id);
        if (samplingPlan == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_PLAN_NOT_EXISTS);
        }
        if (!samplingPlan.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FACTORY_DATA_ACCESS_DENIED);
        }
    }

    private void validateBasicData(AssaySamplingPlanSaveReqVO reqVO) {
        // 校验检测项目存在
        AssayTestIndicatorDO testIndicator = testIndicatorMapper.selectById(reqVO.getTestItem());
        if (testIndicator == null || !testIndicator.getFactoryId().equals(reqVO.getFactoryId())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_PLAN_TEST_ITEM_NOT_EXISTS);
        }
        
        // 校验采样点存在
        AssaySamplingPointDO samplingPoint = samplingPointMapper.selectById(reqVO.getSamplingPoint());
        if (samplingPoint == null || !samplingPoint.getFactoryId().equals(reqVO.getFactoryId())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_PLAN_SAMPLING_POINT_NOT_EXISTS);
        }
    }

    private void validatePlanCodeUnique(Long factoryId, String planCode, Long id) {
        AssaySamplingPlanDO samplingPlan = samplingPlanMapper.selectByFactoryIdAndPlanCode(factoryId, planCode);
        if (samplingPlan == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的记录
        if (id == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_PLAN_CODE_DUPLICATE);
        }
        if (!samplingPlan.getId().equals(id)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_PLAN_CODE_DUPLICATE);
        }
    }

    private void validatePlanNameUnique(Long factoryId, String name, Long id) {
        AssaySamplingPlanDO samplingPlan = samplingPlanMapper.selectByFactoryIdAndName(factoryId, name);
        if (samplingPlan == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的记录
        if (id == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_PLAN_NAME_DUPLICATE);
        }
        if (!samplingPlan.getId().equals(id)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_PLAN_NAME_DUPLICATE);
        }
    }

    private void validatePersonNotSame(Long samplerId, Long testerId, Long reviewerId) {
        if (samplerId.equals(testerId) || samplerId.equals(reviewerId) || testerId.equals(reviewerId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_PLAN_SAME_PERSON_ERROR);
        }
    }

    private void validatePlanTypeFields(AssaySamplingPlanSaveReqVO reqVO) {
        if ("regular".equals(reqVO.getType())) {
            // 常规计划必须有频率、开始日期、结束日期
            if (reqVO.getFrequency() == null || reqVO.getStartDate() == null || reqVO.getEndDate() == null) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_PLAN_REGULAR_FIELDS_REQUIRED);
            }
            // 开始日期不能晚于结束日期
            if (reqVO.getStartDate().isAfter(reqVO.getEndDate())) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_PLAN_DATE_RANGE_ERROR);
            }
        } else if ("temporary".equals(reqVO.getType())) {
            // 临时计划必须有创建原因和执行时间
            if (reqVO.getReason() == null || reqVO.getReason().trim().isEmpty()) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_PLAN_TEMPORARY_REASON_REQUIRED);
            }
            if (reqVO.getPlanDatetime() == null) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.SAMPLING_PLAN_TEMPORARY_DATETIME_REQUIRED);
            }
        }
    }

    @Override
    public AssaySamplingPlanDO getSamplingPlan(Long id, Long factoryId) {
        AssaySamplingPlanDO samplingPlan = samplingPlanMapper.selectById(id);
        if (samplingPlan == null) {
            return null;
        }
        if (!samplingPlan.getFactoryId().equals(factoryId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FACTORY_DATA_ACCESS_DENIED);
        }
        return samplingPlan;
    }

    @Override
    public PageResult<AssaySamplingPlanDO> getSamplingPlanPage(AssaySamplingPlanPageReqVO pageReqVO) {
        return samplingPlanMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AssaySamplingPlanDO> getSamplingPlanList(AssaySamplingPlanPageReqVO reqVO) {
        return samplingPlanMapper.selectList(reqVO);
    }

    @Override
    public List<AssaySamplingPlanDO> getEnabledSamplingPlanList(Long factoryId) {
        return samplingPlanMapper.selectEnabledPlans(factoryId);
    }

    @Override
    public Map<String, Object> checkPlanConflicts(Long factoryId, Long samplingPoint, 
                                                  LocalDate startDate, LocalDate endDate,
                                                  Long samplerId, Long testerId, Long reviewerId,
                                                  Long excludePlanId) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> conflicts = new ArrayList<>();
        
        // 检查采样点冲突
        List<AssaySamplingPlanDO> conflictPlans = samplingPlanMapper.selectConflictPlans(
            factoryId, samplingPoint, startDate, endDate, excludePlanId);
        
        for (AssaySamplingPlanDO plan : conflictPlans) {
            Map<String, Object> conflict = new HashMap<>();
            conflict.put("type", "sampling_point");
            conflict.put("conflictPlanId", plan.getId());
            conflict.put("conflictPlanName", plan.getName());
            conflict.put("samplingPointId", samplingPoint);
            conflict.put("message", "采样点在该时间已有任务安排");
            conflicts.add(conflict);
        }
        
        // TODO: 检查人员冲突（需要查询任务表）
        
        result.put("hasConflicts", !conflicts.isEmpty());
        result.put("conflictCount", conflicts.size());
        result.put("conflicts", conflicts);
        
        if (!conflicts.isEmpty()) {
            List<String> suggestions = Arrays.asList(
                "建议调整采样时间避开冲突",
                "建议重新分配执行人员",
                "建议使用其他可用采样点"
            );
            result.put("suggestions", suggestions);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getPlanCalendar(Long factoryId, Integer year, Integer month, String type) {
        // 计算月份的开始和结束日期
        YearMonth yearMonth = YearMonth.of(year, month);
        LocalDate startDate = yearMonth.atDay(1);
        LocalDate endDate = yearMonth.atEndOfMonth();
        
        // 查询计划数据
        AssaySamplingPlanPageReqVO reqVO = new AssaySamplingPlanPageReqVO();
        reqVO.setFactoryId(factoryId);
        reqVO.setType(type);
        List<AssaySamplingPlanDO> plans = samplingPlanMapper.selectList(reqVO);
        
        // 构建日历数据
        Map<String, Object> result = new HashMap<>();
        result.put("year", year);
        result.put("month", month);
        result.put("factoryId", factoryId);
        result.put("totalPlans", plans.size());
        
        // TODO: 构建详细的日历视图数据
        List<Map<String, Object>> days = new ArrayList<>();
        result.put("days", days);
        
        return result;
    }

}
