package cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Schema(description = "管理后台 - 检测项目新增/修改 Request VO")
@Data
public class AssayTestProjectSaveReqVO {

    @Schema(description = "项目ID", example = "1")
    private Long id;

    @Schema(description = "水厂ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "水厂ID不能为空")
    private Long factoryId;

    @Schema(description = "项目代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "WATER_ORGANIC")
    @NotBlank(message = "项目代码不能为空")
    @Size(max = 50, message = "项目代码长度不能超过50个字符")
    private String code;

    @Schema(description = "项目名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "有机物检测")
    @NotBlank(message = "项目名称不能为空")
    @Size(max = 100, message = "项目名称长度不能超过100个字符")
    private String name;

    @Schema(description = "所属类型ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "所属类型ID不能为空")
    private Long categoryId;

    @Schema(description = "项目描述", example = "有机污染物检测项目")
    @Size(max = 1000, message = "项目描述长度不能超过1000个字符")
    private String description;

    @Schema(description = "启用状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "启用状态不能为空")
    private Boolean isEnabled;

}
