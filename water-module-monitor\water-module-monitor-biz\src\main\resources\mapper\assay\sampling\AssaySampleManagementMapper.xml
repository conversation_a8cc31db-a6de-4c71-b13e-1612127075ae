<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.tl.cloud.module.monitor.dal.mysql.assay.sampling.AssaySampleManagementMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.tl.cloud.module.monitor.dal.dataobject.assay.sampling.AssaySampleManagementDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="factory_id" property="factoryId" jdbcType="BIGINT"/>
        <result column="sample_code" property="sampleCode" jdbcType="VARCHAR"/>
        <result column="execution_id" property="executionId" jdbcType="BIGINT"/>
        <result column="sample_type" property="sampleType" jdbcType="VARCHAR"/>
        <result column="volume" property="volume" jdbcType="INTEGER"/>
        <result column="appearance" property="appearance" jdbcType="VARCHAR"/>
        <result column="preservation_method" property="preservationMethod" jdbcType="VARCHAR"/>
        <result column="storage_location" property="storageLocation" jdbcType="VARCHAR"/>
        <result column="storage_temperature" property="storageTemperature" jdbcType="INTEGER"/>
        <result column="expiry_date" property="expiryDate" jdbcType="DATE"/>
        <result column="test_item" property="testItem" jdbcType="BIGINT"/>
        <result column="sampling_person_id" property="samplingPersonId" jdbcType="BIGINT"/>
        <result column="sampling_date" property="samplingDate" jdbcType="DATE"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="destroy_reason" property="destroyReason" jdbcType="VARCHAR"/>
        <result column="destroy_time" property="destroyTime" jdbcType="TIMESTAMP"/>
        <result column="destroy_operator_id" property="destroyOperatorId" jdbcType="BIGINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="extra_field" property="extraField" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="BIT"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, factory_id, sample_code, execution_id, sample_type, volume, appearance,
        preservation_method, storage_location, storage_temperature, expiry_date,
        test_item, sampling_person_id, sampling_date, status, destroy_reason,
        destroy_time, destroy_operator_id, remark, extra_field,
        creator, create_time, updater, update_time, deleted
    </sql>

    <!-- 查询样品详情（包含关联信息） -->
    <select id="selectSampleWithDetails" parameterType="java.lang.Long" resultType="java.util.Map">
        SELECT 
            s.*,
            e.task_id,
            t.task_code,
            ti.name as test_item_name,
            u1.nickname as sampling_person_name,
            u2.nickname as destroy_operator_name
        FROM assay_sample_management s
        LEFT JOIN assay_sampling_execution e ON s.execution_id = e.id
        LEFT JOIN assay_sampling_task t ON e.task_id = t.id
        LEFT JOIN assay_test_indicator ti ON s.test_item = ti.id
        LEFT JOIN system_users u1 ON s.sampling_person_id = u1.id
        LEFT JOIN system_users u2 ON s.destroy_operator_id = u2.id
        WHERE s.id = #{id} AND s.deleted = 0
    </select>

    <!-- 批量更新样品状态 -->
    <update id="batchUpdateStatus">
        UPDATE assay_sample_management 
        SET status = #{status}, updater = #{updater}, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 查询即将过期的样品 -->
    <select id="selectExpiringSamples" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM assay_sample_management
        WHERE factory_id = #{factoryId}
          AND expiry_date BETWEEN #{startDate} AND #{endDate}
          AND status NOT IN ('destroyed', 'completed')
          AND deleted = 0
        ORDER BY expiry_date ASC
    </select>

    <!-- 统计样品状态分布 -->
    <select id="selectStatusStatistics" parameterType="java.lang.Long" resultType="java.util.Map">
        SELECT 
            status,
            COUNT(*) as count,
            sample_type,
            preservation_method
        FROM assay_sample_management
        WHERE factory_id = #{factoryId}
          AND deleted = 0
        GROUP BY status, sample_type, preservation_method
    </select>

</mapper>
