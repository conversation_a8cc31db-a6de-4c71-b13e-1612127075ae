package cn.tl.cloud.module.monitor.dal.mysql.monitor.alarm.rule;

import cn.tl.cloud.framework.common.pojo.PageResult;
import cn.tl.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tl.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;

import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.rule.vo.RulePageReqVO;
import cn.tl.cloud.module.monitor.controller.admin.monitor.alarm.rule.vo.RuleSaveReqVO;
import cn.tl.cloud.module.monitor.dal.dataobject.monitor.alarm.rule.RuleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * 告警规则 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RuleMapper extends BaseMapperX<RuleDO> {

    default PageResult<RuleDO> selectPage(RulePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RuleDO>()
                .eqIfPresent(RuleDO::getFactoryId, reqVO.getFactoryId())
                .likeIfPresent(RuleDO::getName, reqVO.getName())
                .eqIfPresent(RuleDO::getRuleType, reqVO.getRuleType())
                .eqIfPresent(RuleDO::getExpression, reqVO.getExpression())
                .eqIfPresent(RuleDO::getLevel, reqVO.getLevel())
                .eqIfPresent(RuleDO::getEnabled, reqVO.getEnabled())
                .eqIfPresent(RuleDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(RuleDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(RuleDO::getId));
    }

    @Select("SELECT * FROM alarm_rule WHERE enabled = 1 AND deleted = 0")
    List<RuleDO> selectEnabledRules();


    @Select("SELECT * FROM alarm_rule WHERE enabled = 1 AND deleted = 0 AND factory_id = #{factoryId}")
    List<RuleDO> selectEnabledRulesByFactoryId(String factoryId);




}
