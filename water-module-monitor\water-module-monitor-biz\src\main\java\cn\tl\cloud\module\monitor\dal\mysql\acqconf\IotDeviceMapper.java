package cn.tl.cloud.module.monitor.dal.mysql.acqconf;

import cn.tl.cloud.module.monitor.dal.dataobject.acqconf.IotDevice;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;


/**
 *
 */
@Mapper
@DS("iot")
public interface IotDeviceMapper {

    /**
     * 查询设备个数
     * @return
     */
    @Select("select count(dd.id) from dc3_device dd left join dc3_group dg on dd.group_id = dg.id where dg.deleted = 0 and dd.deleted = 0 and dg.enable_flag = 1 ")
    Long count();


    /**
     * 分页查询设备列表
     * @param iotDevicePage
     * @param queryWrapper
     * @return
     */
    @Select(" select dd.id,dd.device_code as deviceCode,dd.device_name as deviceName,dd.remark as remark,dd.group_id as factoryId,dg.group_name as factoryName,ddr.driver_name as driverType from dc3_device dd \n" +
            " left join dc3_group dg on dd.group_id = dg.id\n" +
            " left join dc3_driver ddr on dd.driver_id = ddr.id ${ew.customSqlSegment}  ")
    Page<IotDevice> queryPage(@Param("page") Page<IotDevice> iotDevicePage, @Param(Constants.WRAPPER) QueryWrapper<IotDevice> queryWrapper);
}
