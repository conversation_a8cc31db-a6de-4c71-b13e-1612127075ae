package cn.tl.cloud.module.monitor.service.assay.common;

import cn.tl.cloud.module.monitor.dal.dataobject.assay.common.AssayOperationLogDO;
import cn.tl.cloud.module.monitor.dal.mysql.assay.common.AssayOperationLogMapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 操作日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssayOperationLogServiceImpl implements AssayOperationLogService {

    @Resource
    private AssayOperationLogMapper operationLogMapper;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    public void recordLog(Long factoryId, String tableId, Long recordId, String operationType, 
                         String operationDesc, Long operatorId, String beforeData, String afterData) {
        AssayOperationLogDO log = AssayOperationLogDO.builder()
                .factoryId(factoryId)
                .tableId(tableId)
                .recordId(recordId)
                .operationType(operationType)
                .operationDesc(operationDesc)
                .operatorId(operatorId)
                .operationTime(LocalDateTime.now())
                .beforeData(beforeData)
                .afterData(afterData)
                .build();
        
        operationLogMapper.insert(log);
    }

    @Override
    public void batchRecordLogs(List<AssayOperationLogDO> logs) {
        if (logs != null && !logs.isEmpty()) {
            for (AssayOperationLogDO log : logs) {
                if (log.getOperationTime() == null) {
                    log.setOperationTime(LocalDateTime.now());
                }
                operationLogMapper.insert(log);
            }
        }
    }

    @Override
    public void recordStatusChange(Long factoryId, String tableId, Long recordId, 
                                  String oldStatus, String newStatus, Long operatorId, String remark) {
        Map<String, Object> beforeData = new HashMap<>();
        beforeData.put("status", oldStatus);
        
        Map<String, Object> afterData = new HashMap<>();
        afterData.put("status", newStatus);
        
        String operationDesc = String.format("状态从 %s 变更为 %s", oldStatus, newStatus);
        if (remark != null && !remark.trim().isEmpty()) {
            operationDesc += "，备注：" + remark;
        }

        try {
            recordLog(factoryId, tableId, recordId, "status_change", operationDesc,
                     operatorId, objectMapper.writeValueAsString(beforeData), objectMapper.writeValueAsString(afterData));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<AssayOperationLogDO> getRecordLogs(Long factoryId, String tableId, Long recordId) {
        return operationLogMapper.selectByRecordId(factoryId, tableId, recordId);
    }

    @Override
    public List<Map<String, Object>> getStatusChangeHistory(Long factoryId, String tableId, Long recordId) {
        return operationLogMapper.selectStatusChangeHistory(factoryId, tableId, recordId);
    }

    @Override
    public Map<String, Object> getOperationStatistics(Long factoryId) {
        List<Map<String, Object>> statistics = operationLogMapper.selectOperationStatistics(factoryId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("statistics", statistics);
        
        // 统计总数
        Map<String, Integer> typeCounts = new HashMap<>();
        for (Map<String, Object> stat : statistics) {
            String operationType = (String) stat.get("operation_type");
            Integer count = ((Number) stat.get("count")).intValue();
            typeCounts.put(operationType, typeCounts.getOrDefault(operationType, 0) + count);
        }
        result.put("typeCounts", typeCounts);
        
        return result;
    }

    @Override
    public List<AssayOperationLogDO> getRecentLogs(Long factoryId, Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 50; // 默认50条
        }
        return operationLogMapper.selectRecentLogs(factoryId, limit);
    }

}
