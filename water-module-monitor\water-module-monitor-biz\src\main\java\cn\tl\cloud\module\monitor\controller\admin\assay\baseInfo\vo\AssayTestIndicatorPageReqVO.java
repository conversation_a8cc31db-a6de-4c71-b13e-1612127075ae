package cn.tl.cloud.module.monitor.controller.admin.assay.baseInfo.vo;

import cn.tl.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 检测指标分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AssayTestIndicatorPageReqVO extends PageParam {

    @Schema(description = "水厂ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long factoryId;

    @Schema(description = "所属项目ID", example = "1")
    private Long projectId;

    @Schema(description = "指标名称", example = "COD")
    private String name;

    @Schema(description = "启用状态", example = "true")
    private Boolean isEnabled;

    @Schema(description = "类型ID筛选", example = "1")
    private Long categoryId;

}
